package com.howbuy.tms.high.batch.service.service.file.fileexport.excel.impl;

import com.github.pagehelper.Page;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.batch.dao.po.batch.IoAppointmentReportExportPo;
import com.howbuy.tms.high.batch.facade.query.queryappointmentreport.QueryAppointmentReportFacade;
import com.howbuy.tms.high.batch.facade.query.queryappointmentreport.QueryAppointmentReportRequest;
import com.howbuy.tms.high.batch.facade.query.queryappointmentreport.QueryAppointmentReportResponse;
import com.howbuy.tms.high.batch.facade.query.queryappointmentreport.bean.AppointmentReportBean;
import com.howbuy.tms.high.batch.service.common.HighBatchConstants;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.IoAppointmentReportExportRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.excel.AbstractExcelFileExportService;
import com.howbuy.tms.high.batch.service.service.file.fileexport.excel.bean.ExcelFileExportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * @Description:预约报表
 */
@Service
@Slf4j
public class ExportAppointmentReportService extends AbstractExcelFileExportService<IoAppointmentReportExportPo> {
    @Autowired
    private QueryAppointmentReportFacade queryAppointmentReportFacade;
    @Autowired
    private IoAppointmentReportExportRepository ioAppointmentReportExportRepository;

    @Override
    public List<Map<String, Object>> getMapList(List<IoAppointmentReportExportPo> results) {
        List<Map<String, Object>> dataList = new LinkedList<Map<String, Object>>();
        for (IoAppointmentReportExportPo bean : results) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("getSubmitTaDate", bean.getSubmitTaDate());
            map.put("getTaInfo", bean.getTaInfo());
            map.put("getFundManInfo", bean.getFundManInfo());
            map.put("getFundInfo", bean.getFundInfo());
            map.put("getPurPersonCount", bean.getPurPersonCount() == null ? "--" : bean.getPurPersonCount());
            map.put("getPurOrderCount", bean.getPurOrderCount() == null ? "--" : bean.getPurOrderCount());
            map.put("getPurAmtCount", bean.getPurAmtCount() == null ? "--" : MoneyUtil.formatMoneyToString(new BigDecimal(bean.getPurAmtCount()), 2));
            map.put("getPurNetAmtCount", bean.getPurNetAmtCount() == null ? "--" : MoneyUtil.formatMoneyToString(new BigDecimal(bean.getPurNetAmtCount()), 2));
            map.put("getPurFeeCount", bean.getPurFeeCount() == null ? "--" : MoneyUtil.formatMoneyToString(new BigDecimal(bean.getPurFeeCount()), 2));
            map.put("getPurAppointEndDate", bean.getPurAppointEndDate() == null ? "--" : bean.getPurAppointEndDate());
            map.put("getRedeemPersonCount", bean.getRedeemPersonCount() == null ? "--" : bean.getRedeemPersonCount());
            map.put("getRedeemOrderCount", bean.getRedeemOrderCount() == null ? "--" : bean.getRedeemOrderCount());
            map.put("getRedeemVolCount", bean.getRedeemVolCount() == null ? "--" : MoneyUtil.formatMoneyToString(new BigDecimal(bean.getRedeemVolCount()), 2));
            map.put("getRedeemAppointEndDate", bean.getRedeemAppointEndDate() == null ? "--" : bean.getRedeemAppointEndDate());
            map.put("getMemo", bean.getMemo() == null ? "--" : bean.getMemo());
            dataList.add(map);
        }
        return dataList;
    }

    @Override
    public String[] getDsTitles() {
        return new String[]{"getSubmitTaDate", "getTaInfo", "getFundManInfo", "getFundInfo", "getPurPersonCount", "getPurOrderCount", "getPurAmtCount",
                "getPurNetAmtCount", "getPurFeeCount", "getPurAppointEndDate", "getRedeemPersonCount", "getRedeemOrderCount", "getRedeemVolCount", "getRedeemAppointEndDate", "getMemo"};
    }

    @Override
    public String[] getHeaders() {
        return new String[]{"上报TA日期", "TA代码", "基金管理人", "产品代码", "认申购人数", "认申购笔数", "认申购金额", "认申购净金额", "认申购手续费", "认申购预约截止日", "赎回人数", "赎回笔数", "赎回份额", "赎回预约截止日", "备注"};
    }

    @Override
    public String getBusinessCode() {
        return FilePathStoreBusinessCodeConfig.SIMU_MIDDLE_REPORT;
    }

    @Override
    public Page<IoAppointmentReportExportPo> queryIoByParams(Map<String, Object> params, int pageNum, int pageSize) {
        return ioAppointmentReportExportRepository.selectPageByExportDt(String.valueOf(params.get(HighBatchConstants.TA_TRADE_DT)), pageNum, pageSize);
    }

    @Override
    public void deleteIoByParams(Map<String, Object> params) {
        ioAppointmentReportExportRepository.deleteByTaTradeDt((String)params.get(HighBatchConstants.TA_TRADE_DT));
    }

    @Override
    public void insertIoByParams(Map<String, Object> params) {
        QueryAppointmentReportRequest queryAppointmentReportRequest = new QueryAppointmentReportRequest();
        // 上报开始日期
        queryAppointmentReportRequest.setSubmitTaDateBegin((String)params.get("tradeDt"));
        // 基金管理人
        queryAppointmentReportRequest.setFundManCodeList((List)params.get("fundManCodeList"));
        // 显示备注
        queryAppointmentReportRequest.setShowMemo(YesOrNoEnum.YES.getCode());
        queryAppointmentReportRequest.setMailFlag(YesOrNoEnum.YES.getCode());
        QueryAppointmentReportResponse result = queryAppointmentReportFacade.execute(queryAppointmentReportRequest);
        if (!ExceptionCodes.SUCCESS.equals(result.getReturnCode())) {
            log.error("导出高端预约报表失败");
            throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR, "导出文件失败");
        }

        List<AppointmentReportBean> beanList = result.getList();
        List<IoAppointmentReportExportPo> ioAppointmentReportExportPos = new ArrayList<>();
        String taTradeDt = (String)params.get(HighBatchConstants.TA_TRADE_DT);
        if (!CollectionUtils.isEmpty(beanList)) {
            for(AppointmentReportBean bean : beanList){
                if (!overZero(bean.getPurOrderCount()) && !overZero(bean.getRedeemOrderCount()) && filterateMemo(bean.getMemo())) {
                    continue;
                }
                IoAppointmentReportExportPo po = new IoAppointmentReportExportPo();
                BeanUtils.copyProperties(bean, po);
                po.setCreateDtm(new Date());
                po.setUpdateDtm(new Date());
                po.setTaTradeDt(taTradeDt);
                ioAppointmentReportExportPos.add(po);
            }
        }

        if(!CollectionUtils.isEmpty(ioAppointmentReportExportPos)) {
            ioAppointmentReportExportRepository.batchInsert(ioAppointmentReportExportPos);
        }
    }

    @Override
    public void specialProcess(ExcelFileExportContext context, String absolutePath) throws Exception {

    }

    private boolean overZero(String str) {
        return StringUtils.isNotEmpty(str) && new BigDecimal(str).compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断备注是否需要过滤
     *
     * @param memo
     * @return
     */
    private boolean filterateMemo(String memo) {
        if (StringUtils.isEmpty(memo)) {
            return true;
        }

        return !memo.contains("增加") && !memo.contains("减少");
    }
}
