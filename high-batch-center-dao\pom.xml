<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  
  <parent>
    <groupId>com.howbuy.tms</groupId>
    <artifactId>high-batch-center</artifactId>
    <version>4.8.50-RELEASE</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  
  <artifactId>high-batch-center-dao</artifactId>
  <name>high-batch-center-dao</name>
  <packaging>jar</packaging>
  
  <dependencies>
	  <dependency>
		  <groupId>org.mybatis.spring.boot</groupId>
		  <artifactId>mybatis-spring-boot-starter</artifactId>
		  <exclusions>
			  <exclusion>
				  <groupId>org.slf4j</groupId>
				  <artifactId>slf4j-api</artifactId>
			  </exclusion>
		  </exclusions>
	  </dependency>

	  <dependency>
		  <groupId>com.alibaba</groupId>
		  <artifactId>druid</artifactId>
	  </dependency>

	  <dependency>
		  <groupId>com.github.pagehelper</groupId>
		  <artifactId>pagehelper</artifactId>
	  </dependency>

	  <dependency>
		  <groupId>mysql</groupId>
		  <artifactId>mysql-connector-java</artifactId>
	  </dependency>

	  <dependency>
		  <groupId>com.alibaba</groupId>
		  <artifactId>fastjson</artifactId>
		  <version>${fastjson.version}</version>
	  </dependency>

	  <dependency>
		  <groupId>org.projectlombok</groupId>
		  <artifactId>lombok</artifactId>
	  </dependency>
	  <dependency>
		  <groupId>org.mybatis.generator</groupId>
		  <artifactId>mybatis-generator-core</artifactId>
		  <version>1.4.0</version>
		  <scope>test</scope>
	  </dependency>
  </dependencies>

	<build>
		<plugins>
			<!-- mybatis generator 自动生成代码插件 -->
			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>1.4.0</version>
				<configuration>
					<configurationFile>src/test/resources/generatorConfig-high-order.xml</configurationFile>
					<overwrite>true</overwrite>
					<verbose>true</verbose>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>mysql</groupId>
						<artifactId>mysql-connector-java</artifactId>
						<version>8.0.28</version>
					</dependency>
					<dependency>
						<groupId>com.github.oceanc</groupId>
						<artifactId>mybatis3-generator-plugin</artifactId>
						<version>0.4.0</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>
</project>