/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.crmfilemonitor;

import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.batch.CmFileProcessRecPo;
import com.howbuy.tms.high.batch.service.common.CrmFileReceiveTypeEnum;
import com.howbuy.tms.high.batch.service.common.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.CmHkNaFileImportService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.CmHkTradeFileImportService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.CmHkVolFileImportService;
import com.howbuy.tms.high.batch.service.task.ParseNaFeeFileTask;
import com.howbuy.tms.high.batch.service.task.ParseTradeFileTask;
import com.howbuy.tms.high.batch.service.task.ParseVolFileTask;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: crm文件处理
 * @date 2019年4月10日 上午10:08:15
 * @since JDK 1.6
 */
@Service("crmHkDayFileProcessor")
public class CrmHkDayFileProcessor extends AbstractCrmFileProcessorService {
    private static Logger logger = LogManager.getLogger(CrmHkDayFileProcessor.class);
    @Autowired
    private CmHkVolFileImportService cmVolFileImportService;
    @Autowired
    private CmHkTradeFileImportService cmTradeFileImportService;
    @Autowired
    private CmHkNaFileImportService cmNaFileImportService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;


    @Override
    protected void processFile(CmFileProcessRecPo po) throws Exception {
        // 查询上个工作日
        Date lastDay = DateUtils.addDay(new Date(), -1);
        String lastWorkDay = DateUtils.formatToString(lastDay, DateUtils.YYYYMMDD);
        List<ThreadExceptionStatus> statusList = new ArrayList<>();
        List<AbstractHowbuyBaseTask> taskList = new ArrayList<>();
        taskList.add(new ParseVolFileTask(po, statusList, lastWorkDay, cmVolFileImportService, volFilePathName, CrmFileReceiveTypeEnum.HK_DAY.getType()));
        taskList.add(new ParseTradeFileTask(po, statusList, lastWorkDay, cmTradeFileImportService, tradeDirectFilePathName, CrmFileReceiveTypeEnum.HK_DAY.getType()));
        taskList.add(new ParseNaFeeFileTask(po, statusList, lastWorkDay, cmNaFileImportService, CrmFileReceiveTypeEnum.HK_DAY.getType(), naFeeFilePathName));
        howBuyRunTaskUil.runTask(taskList);
        for (ThreadExceptionStatus status : statusList) {
            if (status.isExsitException()) {
                if (status.getException() != null) {
                    throw new Exception("CrmFileProcessor checkFundCount err:" + status.getException().getMessage());
                } else {
                    throw new Exception("CrmFileProcessor checkFundCount err");
                }
            }
        }
        po.setProcessStatus(ProcessStatusEnum.PROCESS_SUCCESS.getCode());
    }

    /**
     * 写入正式表
     */
    @Override
    protected void insertFormalTable(CmFileProcessRecPo po) {
        if (!ProcessStatusEnum.PROCESS_SUCCESS.getCode().equals(po.getProcessStatus())) {
            logger.warn("CrmFileProcessor|insertFormalTable|versionNo:{} and ProcessStatus:{}", po.getVersionNo(), po.getProcessStatus());
            return;
        }
        cmDataProcessService.execBatchInsert(po.getVersionNo() + "", YesOrNoEnum.YES.getCode());
    }

    /**
     * checkFundCount:持仓总数校验：最新的临时表总数/正式表总数 < 0.9则抛出异常
     *
     * @param po
     * <AUTHOR>
     * @date 2019年5月9日 下午3:10:48
     */
    @Override
    protected void checkFundCount(CmFileProcessRecPo po) throws Exception {
        // 临时表总数
        Long tmpCount = cmDataProcessService.countTmpFundByVersionNo(po.getVersionNo());
        logger.info("CrmFileProcessor-checkFundCount-tmpCount:{}", tmpCount);
        // 实际表总数
        Long count = cmDataProcessService.countFund(YesOrNoEnum.YES.getCode());
        logger.info("CrmFileProcessor-checkFundCount-count:{}", count);
        if (count > 0) {
            DecimalFormat format = new DecimalFormat("0.00");
            String rateStr = format.format((float) tmpCount / count);
            BigDecimal rateDecimal = new BigDecimal(rateStr);
            logger.info("CrmFileProcessor-checkFundCount-rateDecimal:{}", rateDecimal);
            if (RATE.compareTo(rateDecimal) > 0) {
                throw new Exception("CrmFileProcessor checkFundCount err");
            }
        }
    }

}
