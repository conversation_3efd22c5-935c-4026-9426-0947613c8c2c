/**
 * Copyright (c) 2020, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.generatefile;

import com.github.pagehelper.Page;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessRecPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaBusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.WorkdayPo;
import com.howbuy.tms.high.batch.dao.po.order.ExportHeFundAckFileRecPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.facade.enums.HeExportFileTypeEnum;
import com.howbuy.tms.high.batch.service.common.*;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.CmFileProcessRecRepository;
import com.howbuy.tms.high.batch.service.repository.ExportHeFundAckFileRecRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.bean.TxtFileExportContext;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.impl.ExportHeFundAckFileService;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 生成高端基金确认文件处理类
 * @date 2020/1/14 16:22
 * @since JDK 1.7
 */
@Service
public class GenerateHighFundAckAssertFileService extends AbstractGenerateAssertFileService {

    private static Logger logger = LogManager.getLogger(GenerateHighFundAckAssertFileService.class);

    @Autowired
    private ExportHeFundAckFileRecRepository exportHeFundAckFileRecRepository;

    @Autowired
    private CmFileProcessRecRepository cmFileProcessRecRepository;

    @Autowired
    private ExportHeFundAckFileService exportHeFundAckFileService;

    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    /**
     * @param
     * @return void
     * @description: 交易确认文件生成
     * @author: hongdong.xie
     * @date: 2020/1/15 15:48
     * @since JDK 1.7
     */
    public void generate() throws Exception {
        WorkdayPo workdayPo = workdayService.getWorkay(WorkdayTypeEnum.SYS_TYPE);
        String tradeDt = workdayService.getSaleSysCurrWorkay();
        String lastWorkDay = workdayPo.getLastWorkday();

        // 判断当前日期是否是工作日
        String nowDay = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        if (!StringUtils.equals(nowDay, tradeDt)) {
            logger.info("GenerateHighFundAckFileService|generate|nowDay:{} is not work day :{}", nowDay, tradeDt);
            return;
        }

        // 判断ok文件是否已经生成
        FundFileProcessRecPo processPo = fundFileProcessRecRepository.selectByUniqueKey(tradeDt, FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode(), Constant.ALL_TA_CODE);
        if (processPo != null && ProcessStatusEnum.NOTICE_SUCCESS.getCode().equals(processPo.getProcessStatus())) {
            logger.info("GenerateHighCmBlackFileService|generate|ok文件{}已生成", processPo.getFileName());
            return;
        }

        //2.查询确认处理日终各TA节点信息
        List<TaBusinessBatchFlowPo> flowList = taBusinessBatchFlowRepository.selectTaBusinessBatchFlowByTaskId(tradeDt, BusinessProcessingStepEnum.BPS_ACK_DAY_END_PROCESS.getCode(), SysCodeEnum.BATCH_HIGH.getCode());
        if (CollectionUtils.isEmpty(flowList)) {
            logger.info("flowList is empty,tradeDt:{},taskId:{},sysCode:{}", tradeDt, BusinessProcessingStepEnum.BPS_ACK_DAY_END_PROCESS.getCode(), SysCodeEnum.BATCH_HIGH.getCode());
            return;
        }

        //3.查询交易确认文件已出文件信息
        Map<String, FundFileProcessRecPo> recPoMap = fundFileProcessRecRepository.getMapByFileTypeAndTaTradeDt(FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode(), tradeDt);
        //4.针对未出文件的TA,生成文件
        List<AbstractHowbuyBaseTask> taskList = new ArrayList<>();
        for (TaBusinessBatchFlowPo po : flowList) {
            //如果节点没有成功直接跳过
            if (!BatchStatEnum.PROCESS_SUCCESS.getKey().equals(po.getFlowStat())) {
                continue;
            }
            FundFileProcessRecPo recPo = recPoMap.get(po.getTaCode());
            FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
            fileSdkPathInfo.setFileName(getCheckFileName(po.getTradeDt(), po.getTaCode(), null));
            fileSdkPathInfo.setMiddlePath(getPath(tradeDt));
            fileSdkPathInfo.setBusinessCode(getBusinessCode());
            String absolutePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
            //判断文件记录是否生成，如果没有则生成文件记录
            if (recPo == null) {
                recPo = addFundFileProcessRec(po.getTradeDt(), po.getTaCode(), FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode(), absolutePath);
            }
            //判断文件是否已经成功
            if (FileOpStatusEnum.MAKE_SUCCESSFUL.getKey().equals(recPo.getFileOpStatus())) {
                continue;
            }
            taskList.add(new FundAckFileTask(recPo, lastWorkDay));
        }
        howBuyRunTaskUil.runTask(taskList);

        // 直销处理
        cmProcess(tradeDt, lastWorkDay, recPoMap);

        //发送文件消息&OK文件消息
        sendFileMessage(flowList, tradeDt);
    }

    private void cmProcess(String tradeDt, String lastWorkDay, Map<String, FundFileProcessRecPo> recPoMap) throws Exception {
        // 处理直销文件
        boolean fileProcess = crmFileHasProcess(tradeDt, FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode());
        if (!fileProcess) {
            logger.info("交易记录直销文件还没有处理完成,tradeDt={}", tradeDt);
            return;
        }
        BigDecimal latestVersion = cmFileProcessRecRepository.selectLatestDayVersion();
        FundFileProcessRecPo recPo = null;
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setFileName(getCheckFileName(tradeDt, Constant.HOWBUY_CM_TA, latestVersion + ""));
        fileSdkPathInfo.setMiddlePath(getPath(tradeDt));
        fileSdkPathInfo.setBusinessCode(getBusinessCode());
        String absolutePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
        if (recPoMap.isEmpty()) {
            recPo = addFundFileProcessRec(tradeDt, Constant.HOWBUY_CM_TA, FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode(), absolutePath);
        } else {
            recPo = recPoMap.get(Constant.HOWBUY_CM_TA);
            if (recPo == null) {
                //判断文件记录是否生成，如果没有则生成文件记录
                recPo = addFundFileProcessRec(tradeDt, Constant.HOWBUY_CM_TA, FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode(), absolutePath);
            }
        }

        //判断文件是否已经成功
        if (FileOpStatusEnum.MAKE_SUCCESSFUL.getKey().equals(recPo.getFileOpStatus())) {
            logger.info("cmFileProcess ack file has process ");
        } else {
            howBuyRunTaskUil.runTask(new FundAckFileTask(recPo, lastWorkDay));
        }

    }

    /**
     * @param flowList TA批处理节点列表
     * @param tradeDt  交易日期
     * @return void
     * @description: 发送文件消息
     * @author: hongdong.xie
     * @date: 2020/1/15 15:19
     * @since JDK 1.7
     */
    private void sendFileMessage(List<TaBusinessBatchFlowPo> flowList, String tradeDt) throws Exception {
        //5.检查所有TA是否生成成功
        Map<String, FundFileProcessRecPo> recMap = fundFileProcessRecRepository.getMapByFileTypeAndTaTradeDt(FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode(), tradeDt);
        boolean isAllSuccess = true;
        if (!CollectionUtils.isEmpty(flowList)) {
            for (TaBusinessBatchFlowPo po : flowList) {
                FundFileProcessRecPo recPo = recMap.get(po.getTaCode());
                //确认处理日终执行成功且文件成功成功
                if (recPo == null || !FileOptionStatus.CREATE_SUCCESS.getCode().equals(recPo.getFileOpStatus()) || !BatchStatEnum.PROCESS_SUCCESS.getKey().equals(po.getFlowStat())) {
                    isAllSuccess = false;
                    continue;
                }
                //发送消息,只有未处理的才发送消息
                if (!ProcessStatusEnum.NOT_PROCESS.getCode().equals(recPo.getProcessStatus())) {
                    continue;
                }
                //消息发送
                boolean flag = sendMessage(recPo.getTaTradeDt(), recPo.getTaCode(), recPo.getFileName(), HeExportFileTypeEnum.EXPORT_ACK.getKey());
                if (!flag) {
                    logger.error("message send fail,tradeDt:{},taCode:{},fileName:{}", recPo.getTaTradeDt(), recPo.getTaCode(), recPo.getFileName());
                }
                //设置处理状态
                updateFileStatus(recPo);
            }
        }

        // 处理直销
        FundFileProcessRecPo recPo = recMap.get(Constant.HOWBUY_CM_TA);
        if (recPo == null || !FileOptionStatus.CREATE_SUCCESS.getCode().equals(recPo.getFileOpStatus())) {
            isAllSuccess = false;
        } else {
            //发送消息,只有未处理的才发送消息
            if (ProcessStatusEnum.NOT_PROCESS.getCode().equals(recPo.getProcessStatus())) {
                //消息发送
                sendMessage(recPo.getTaTradeDt(), recPo.getTaCode(), recPo.getFileName(), HeExportFileTypeEnum.EXPORT_ACK.getKey());
                //设置处理状态
                updateFileStatus(recPo);
            }
        }

        if (isAllSuccess) {
            //生成OK文件&发送
            FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
            fileSdkPathInfo.setFileName(getOkFileName(tradeDt));
            fileSdkPathInfo.setMiddlePath(getPath(tradeDt));
            fileSdkPathInfo.setBusinessCode(getBusinessCode());
            String absolutePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
            //校验OK文件是否已经生成
            FundFileProcessRecPo processPo = fundFileProcessRecRepository.selectByUniqueKey(tradeDt, FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode(), Constant.ALL_TA_CODE);
            if (processPo == null) {
                processPo = addFundFileProcessRec(tradeDt, Constant.ALL_TA_CODE, FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode(), absolutePath);
            }
            if (!FileOpStatusEnum.MAKE_SUCCESSFUL.getKey().equals(processPo.getFileOpStatus())) {
                fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, processPo.getFileType(), processPo.getTaCode(), FileOpStatusEnum.PROCESSING.getKey(), processPo.getFileOpStatus());
                writeOkFile(String.valueOf(flowList.size() + 1), tradeDt);
                fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, processPo.getFileType(), processPo.getTaCode(), FileOpStatusEnum.MAKE_SUCCESSFUL.getKey(), FileOpStatusEnum.PROCESSING.getKey());
            }

            if (ProcessStatusEnum.NOTICE_SUCCESS.getCode().equals(processPo.getProcessStatus())) {
                return;
            }

            //消息发送
            sendMessage(processPo.getTaTradeDt(), processPo.getTaCode(), processPo.getFileName(), HeExportFileTypeEnum.EXPORT_ACK.getKey());
            //设置处理状态
            FundFileProcessRecPo rPo2 = new FundFileProcessRecPo();
            rPo2.setRecordNo(processPo.getRecordNo());
            rPo2.setProcessStatus(ProcessStatusEnum.NOTICE_SUCCESS.getCode());
            fundFileProcessRecRepository.updateByRecordNo(rPo2);
            OpsMonitor.warn(tradeDt + "交易记录资产中心文件已推送", OpsMonitor.INFO);
        }
    }

    private void updateFileStatus(FundFileProcessRecPo recPo) {
        FundFileProcessRecPo rPo = new FundFileProcessRecPo();
        rPo.setRecordNo(recPo.getRecordNo());
        rPo.setFileOpStatus(FileOptionStatus.CREATE_SUCCESS.getCode());
        rPo.setProcessStatus(ProcessStatusEnum.NOTICE_SUCCESS.getCode());
        fundFileProcessRecRepository.updateByRecordNo(rPo);
    }


    /**
     * @description: 生成确认文件任务
     * @author: hongdong.xie
     * @date: 2020/1/15 13:23
     * @since JDK 1.7
     */
    private class FundAckFileTask extends AbstractHowbuyBaseTask {

        private FundFileProcessRecPo recPo;

        private String lastWorkDay;

        public FundAckFileTask(FundFileProcessRecPo recPo, String lastWorkDay) {
            this.recPo = recPo;
            this.lastWorkDay = lastWorkDay;
        }

        @Override
        protected void callTask() {
            String tradeDt = recPo.getTaTradeDt();
            String fileType = recPo.getFileType();
            String taCode = recPo.getTaCode();
            String fileOpStatus = recPo.getFileOpStatus();
            try {
                //1.更新文件处理记录为处理中
                int count = fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, fileType, taCode, FileOpStatusEnum.PROCESSING.getKey(), fileOpStatus);
                if (count != 1) {
                    logger.info("updateFileOpStatusByPrimaryKeyWithVersion fail,tradeDt:{},fileType:{},taCode:{},newFileStatus:{},oldFileOpStatus:{}", tradeDt, fileType, taCode, FileOpStatusEnum.PROCESSING.getKey(), fileOpStatus);
                    return;
                }
                //2.文件生成
                addNewFile(recPo, lastWorkDay);
                //3.更新文件处理记录为生成成功
                count = fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_SUCCESSFUL.getKey(), FileOpStatusEnum.PROCESSING.getKey());
                if (count != 1) {
                    logger.info("updateFileOpStatusByPrimaryKeyWithVersion fail,tradeDt:{},fileType:{},taCode:{},newFileStatus:{},oldFileOpStatus:{}", tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_SUCCESSFUL.getKey(), FileOpStatusEnum.PROCESSING.getKey());
                }
            } catch (Exception e) {
                logger.error("", e);
                //更新文件处理记录为生成失败
                fundFileProcessRecRepository.updateFileOpStatusByPrimaryKey(tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_FAILED.getKey());
                logger.info("updateFileOpStatusByPrimaryKey,tradeDt:{},fileType:{},taCode:{},fileStatus:{}", tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_FAILED.getKey());
            }
        }
    }


    @Override
    public boolean addNewFile(FundFileProcessRecPo recPo, String lastWorkDay) throws Exception {
        TxtFileExportContext txtFileExportContext = new TxtFileExportContext();
        txtFileExportContext.setRelationPath(getPath(recPo.getTaTradeDt()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("taCode", recPo.getTaCode());
        paramMap.put("taTradeDt", recPo.getTaTradeDt());
        paramMap.put("lastWorkDay", lastWorkDay);
        txtFileExportContext.setParams(paramMap);
        txtFileExportContext.setFileName(getFileNameByAbsolutePath(recPo.getFileName()));
        exportHeFundAckFileService.process(txtFileExportContext);
        return true;
    }

    @Override
    public String getCheckFileName(String tradeDt, String taCode, String latestVersion) {
        String fileName = File.separator;
        if (latestVersion == null) {
            fileName += tradeDt + "_he_trade_ack_" + taCode + ".txt";
        } else {
            fileName += tradeDt + "_v" + latestVersion + "_he_trade_ack_" + taCode + ".txt";
        }
        return fileName;
    }

    @Override
    public String getBusinessCode() {
        return FilePathStoreBusinessCodeConfig.ACK_CHECK_FILE_PATH;
    }

    @Override
    public String getPath(String tradeDt) {
        return File.separator + tradeDt;
    }

    @Override
    public String getOkFileName(String tradeDt) {
        return File.separator + tradeDt + "_he_trade_ack.txt.ok";
    }

    /**
     * @param tradeDt  交易日
     * @param taCode   TA代码
     * @param pageNo   页码
     * @param pageSize 每页大小
     * @return com.github.pagehelper.Page<com.howbuy.tms.batch.dao.po.order.ExportFundAckFileRecPo>
     * @description: 分页查询确认数据
     * @author: hongdong.xie
     * @date: 2020/1/14 18:14
     * @since JDK 1.7
     */
    @Override
    protected Page<ExportHeFundAckFileRecPo> getData(final String tradeDt, final String taCode, final int pageNo, final int pageSize) {
        Page<ExportHeFundAckFileRecPo> page = exportHeFundAckFileRecRepository.getByTradeDtTaCode(tradeDt, taCode, pageNo, pageSize);
        if (CollectionUtils.isEmpty(page)) {
            return null;
        }
        return page;
    }


}
