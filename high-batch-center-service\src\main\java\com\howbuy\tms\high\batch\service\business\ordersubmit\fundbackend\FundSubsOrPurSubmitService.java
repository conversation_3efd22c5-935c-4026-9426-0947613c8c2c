/**
 * Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.enums.database.DualentryStatusEnum;
import com.howbuy.tms.common.outerservice.fbsonline.fundsuborpur.FundSubsOrPurContext;
import com.howbuy.tms.common.outerservice.fbsonline.fundsuborpur.FundSubsOrPurOuterService;
import com.howbuy.tms.common.outerservice.fbsonline.fundsuborpur.FundSubsOrPurResult;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.order.DealOrderExtendPo;
import com.howbuy.tms.high.batch.dao.po.order.PaymentOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.service.business.ordersubmit.BaseResponse;
import com.howbuy.tms.high.batch.service.business.ordersubmit.CheckOrderSubmitService;
import com.howbuy.tms.high.batch.service.repository.DealOrderExtendRepository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Random;

import com.howbuy.tms.high.batch.service.repository.PaymentOrderRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:标准认申购接口（接口GN_FDS_NEW_01）服务
 * @reason:
 * <AUTHOR>
 * @date 2016年9月14日 下午1:59:49
 * @since JDK 1.6
 */
@Service("fundSubsOrPurSubmitService")
public class FundSubsOrPurSubmitService extends CheckOrderSubmitService {
    private static Logger logger = LogManager.getLogger(FundSubsOrPurSubmitService.class);

    @Autowired
    private FundSubsOrPurOuterService fundSubsOrPurOuterService;

    @Autowired
    private PaymentOrderRepository paymentOrderRepository;

    @Autowired
    private DealOrderExtendRepository dealOrderExtendRepository;

    @Override
    protected BaseResponse doSubmit(SimuFundCheckOrderManualSubmitVo fundCheckOrderPo) {
        FundSubsOrPurContext context = generateContext(fundCheckOrderPo);

        FundSubsOrPurResult result = fundSubsOrPurOuterService.doSubsOrPur(context);
        logger.info("FundSubsOrPurSubmitService|result:" + JSON.toJSONString(result));
        BaseResponse baseResponse = new BaseResponse();
        BeanUtils.copyProperties(result, baseResponse);
        return baseResponse;
    }

    @Override
    protected void submitSuccess(SimuFundCheckOrderPo fundCheckOrderPo) {
    }

    @Override
    protected void submitFail(SimuFundCheckOrderPo fundCheckOrderPo) {
    }

    protected FundSubsOrPurContext generateContext(SimuFundCheckOrderManualSubmitVo fundCheckOrderPo) {
        FundSubsOrPurContext context = new FundSubsOrPurContext();

        // 交易渠道 TO 交易渠道
        context.setTradeChannel(fundCheckOrderPo.getTxChannel());
        // 分销机构 TO 分销机构代码
        context.setDisCode(fundCheckOrderPo.getDisCode());
        // 网点号 TO 网点代码
        context.setOutletCode(fundCheckOrderPo.getOutletCode());
        // 上报Ta日 TO 申请日期
        context.setAppDt(fundCheckOrderPo.getAppDate());
        // 145959点 TO 申请时间
        context.setAppTm(fundCheckOrderPo.getAppTime());
        // 生效日期
        context.setEffectiveDt(fundCheckOrderPo.getEffectiveDt());
        // 生效时间
        context.setEffectiveTm(fundCheckOrderPo.getEffectiveTm());

        // IP地址 TO 交易IP
        context.setIpAddress(fundCheckOrderPo.getIpAddress());
        // 上报订单号 TO 中台订单号
        context.setSubmitDealNo(fundCheckOrderPo.getSubmitDealNo());
        // 风险确认标志
        context.setRiskFlag(fundCheckOrderPo.getRiskFlag());
        // 客户风险等级
        context.setCustRiskLevel(fundCheckOrderPo.getCustRiskLevel());
        // 交易账号 TO 交易账号
        context.setTxAcctNo(fundCheckOrderPo.getTxAcctNo());
        // 资金账号 TO 资金账号
        context.setCpAcctNo(fundCheckOrderPo.getCpAcctNo());
        // 子交易账号 TO 子交易账号
        context.setSubTxAcctNo(fundCheckOrderPo.getSubTxAcctNo());
        // // 投资者类型 TO 客户类型
        context.setInvstType(fundCheckOrderPo.getInvstType());
        // 客户姓名 TO 客户姓名
        context.setCustName(fundCheckOrderPo.getCustName());
        // 证件类型 TO 证件类型
        //context.setIdType(fundCheckOrderPo.getIdType());
        // 证件号码 TO 证件号码
        //context.setIdNo(fundCheckOrderPo.getIdNo());
        // 银行账号 TO 银行卡号
        //context.setBankAcct(fundCheckOrderPo.getBankAcct());
        // 银行代码 TO 银行编码
        context.setBankCode(fundCheckOrderPo.getBankCode());
        // 基金代码 TO 基金代码
        context.setFundCode(fundCheckOrderPo.getFundCode());
        // 申请金额 TO 购买金额
        context.setAppAmt(fundCheckOrderPo.getAppAmt());
        // 费用折扣率 TO 折扣率
        context.setDiscountRate(fundCheckOrderPo.getDiscountRate());
        // 风险确认标记 TO 风险确认标志
        context.setRiskFlag(fundCheckOrderPo.getRiskFlag());
        // 基金份额类型 TO 份额类型
        context.setShareClass(fundCheckOrderPo.getFundShareClass());
        // 备注 TO 备注
        context.setMemo(fundCheckOrderPo.getMemo());
        // 经办人姓名
        context.setTransactorName(fundCheckOrderPo.getTransactorName());
        // 经办人证件号码
        context.setTransactorIdNo(fundCheckOrderPo.getTransactorIdNo());
        // 经办人证件类型
        context.setTransactorIdType(fundCheckOrderPo.getTransactorIdType());
        context.setProtocolNo(fundCheckOrderPo.getProtocolNo());
        PaymentOrderPo paymentOrder = paymentOrderRepository.getByDealNo(fundCheckOrderPo.getDealNo());
        if (paymentOrder != null) {
            context.setPaymentType(paymentOrder.getPaymentType());
        }
        // 双录状态
        context.setDoubleRecordStatus(DualentryStatusEnum.DONE.getCode());
        // 双录时间
        Date doubleRecordDate = getDoubleRecordDate(context.getAppDt(), context.getAppTm(), fundCheckOrderPo.getAppDtm());
        logger.info("FundSubsOrPurSubmitService|submitDealNo:{}, getDoubleRecordDate:{}, submitAppDt:{}, submitAppTm:{}, appDtm:{}",
                new Object[]{fundCheckOrderPo.getSubmitDealNo(), doubleRecordDate, context.getAppDt(), context.getAppTm(), fundCheckOrderPo.getAppDtm()});
        context.setDoubleRecordDate(doubleRecordDate);

        // 申请金额
        BigDecimal appAmt = fundCheckOrderPo.getAppAmt() == null ? BigDecimal.ZERO : fundCheckOrderPo.getAppAmt();
        // 手续费
        BigDecimal fee = fundCheckOrderPo.getFee() == null ? BigDecimal.ZERO : fundCheckOrderPo.getFee();
        // 交易净金额
        BigDecimal tradeNetAmt = appAmt.subtract(fee);
        // 交易净金额
        context.setTradeNetAmt(tradeNetAmt);

        // KYC信息处理
        DealOrderExtendPo dealOrderExtendVo = dealOrderExtendRepository.getByDealNo(fundCheckOrderPo.getDealNo());
        if (dealOrderExtendVo != null) {
            context.setRiskAckDtm(dealOrderExtendVo.getRiskAckDtm());
            context.setHighRiskTipDtm(dealOrderExtendVo.getHighRiskTipDtm());
            context.setNormalCustTipDtm(dealOrderExtendVo.getNormalCustTipDtm());

            // 组合信息处理
            context.setIsCombinationOrder(null);
            context.setCombinationRiskLevel(null);
            context.setIsCustomerOrder("0");
        }

        return context;
    }

    /**
     *
     * getDoubleRecordDate:(获取双录时间)
     * @param submitAppDt
     * @param submitAppTm
     * @param appDtm
     * @return
     * <AUTHOR>
     * @date 2018年7月3日 下午3:44:36
     */
    private Date getDoubleRecordDate(String submitAppDt, String submitAppTm, Date appDtm) {

        Date doubleRecordDate = appDtm;
        try {
            String submitAppDtmStr = new StringBuffer(submitAppDt).append(submitAppTm).toString();
            Date submitAppDtm = DateUtils.formatToDate(submitAppDtmStr, DateUtils.YYYYMMDDHHMMSS);

            long hours = (submitAppDtm.getTime() - appDtm.getTime()) / (1000 * 60 * 60);
            if (hours > 24) {
                hours = 24;
            }

            if (hours > 0L) {
                int randHours = new Random().nextInt((int) hours);
                doubleRecordDate = DateUtils.addHourOfDay(submitAppDtm, -1 * randHours);
            }

        } catch (Exception e) {
            logger.info("FundSubsOrPurSubmitService|getDoubleRecordDate|error:{}", e.getMessage(), e);
        }
        return doubleRecordDate;
    }
}
