/**
 * Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit.refreshnotifystatus;

import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.service.order.highdealorderdtl.HighDealOrderDtlService;
import com.howbuy.tms.common.outerservice.acccenter.queryaqsetmanagementcertificatestatus.QueryAssetManagementCertificateStatusOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryassetcertificatestatus.QueryCustAssetCertificateStatusOuterService;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.service.business.submittadtcal.SubmitTaDtCalService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;

/**
 * @description:(订单通知状态刷新服务)
 * @reason:
 * <AUTHOR>
 * @date 2018年5月29日 下午1:29:20
 * @since JDK 1.7
 */
@Service("notifyStatusRefreshService")
public class NotifyStatusRefreshService {
    private static Logger logger = LogManager.getLogger(NotifyStatusRefreshService.class);

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private HighDealOrderDtlService highDealOrderDtlService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Autowired
    private SubmitTaDtCalService submitTaDtCalService;

    @Autowired
    private QueryAssetManagementCertificateStatusOuterService queryAssetManagementCertificateStatusOuterService;

    @Autowired
    private QueryCustAssetCertificateStatusOuterService queryCustAssetCertificateStatusOuterService;

    /**
     * 最大线程数
     */
    private int maxPoolNum = 5;

    public void proecess(List<HighDealOrderDtlPo> list, String retrievePeriod, Date now) {

        // 去除重复的合并单
        list = unrepeatedMerageOrder(list);
        if (list == null) {
            return;
        }

        boolean flag = true;

        CountDownLatch latch = null;// 计数器、线程同步
        try {
            /* 将产品平均分配到maxPoolNum个线程进行处理 */
            int divs = list.size() / maxPoolNum;
            int mods = list.size() % maxPoolNum;

            if (divs > 0) {
                latch = new CountDownLatch(maxPoolNum);
                List<HighDealOrderDtlPo> subList = null;
                for (int i = 0; i < maxPoolNum; i++) {
                    if (i == (maxPoolNum - 1)) {
                        subList = list.subList(i * divs, list.size());
                    } else {
                        subList = list.subList(i * divs, ((i + 1) * divs));
                    }
                    // 异步多线程处理
                    CommonThreadPool.execute(new RefreshNotifyStatusTask(highDealOrderDtlRepository, queryHighProductOuterService,
                            queryHbOneNoOuterService, submitTaDtCalService,
                            queryAssetManagementCertificateStatusOuterService,
                            queryCustAssetCertificateStatusOuterService,
                            highDealOrderDtlService,
                            subList, retrievePeriod, now, latch));
                }
            } else if (mods > 0) {
                latch = new CountDownLatch(1);
                // 异步多线程处理
                CommonThreadPool.execute(new RefreshNotifyStatusTask(highDealOrderDtlRepository, queryHighProductOuterService,
                        queryHbOneNoOuterService, submitTaDtCalService,
                        queryAssetManagementCertificateStatusOuterService,
                        queryCustAssetCertificateStatusOuterService,
                        highDealOrderDtlService,
                        list, retrievePeriod, now, latch));
            }
        } catch (Exception e) {
            flag = false;
            logger.warn("NotifyStatusRefreshService|proecess|error, msg:{}", e.getMessage(), e);
        } finally {
            if (flag) {
                try {
                    if (latch != null) {
                        latch.await();
                    }
                } catch (InterruptedException e) {
                    logger.warn("NotifyStatusRefreshService|proecess|latch.await error, msg:{}", e.getMessage(), e);
                    Thread.currentThread().interrupt();
                }
            }
        }

    }

    /**
     * 合并订单仅保留一条，避免并发处理。由于符合条件的可能不是主订单，sql不好过滤
     * @param list
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @author: huaqiang.liu
     * @date: 2021/6/10 11:34
     * @since JDK 1.8
     */
    private List<HighDealOrderDtlPo> unrepeatedMerageOrder(List<HighDealOrderDtlPo> list) {
        if (list == null || list.isEmpty()) {
            return list;
        }
        Set<String> mainDealNo = new HashSet<>();
        List<HighDealOrderDtlPo> newList = new ArrayList<>(list.size());
        for (HighDealOrderDtlPo po : list) {
            if (StringUtils.isNotEmpty(po.getMainDealOrderNo())) {
                if (mainDealNo.contains(po.getMainDealOrderNo())) {
                    continue;
                } else {
                    mainDealNo.add(po.getMainDealOrderNo());
                }
            }
            newList.add(po);
        }
        return newList;
    }

}
