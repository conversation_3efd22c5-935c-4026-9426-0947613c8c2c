/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.batch.service.business.pmtprocess;


import com.howbuy.paycommon.model.enums.TradeStateEnum;
import com.howbuy.payonline.notify.PayResultNotifyRes;
import com.howbuy.tms.common.enums.TxPmtFlagEnum;
import com.howbuy.tms.common.enums.database.PaymentTypeEnum;
import com.howbuy.tms.high.batch.dao.po.order.DealOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.PaymentOrderPo;
import com.howbuy.tms.high.batch.service.repository.DealOrderRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.PaymentOrderRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * @description:(异步支付结果处理) 
 * @reason:
 * <AUTHOR>
 * @date 2018年5月22日 上午10:35:21
 * @since JDK 1.6
 */
@Service("notifyAsyncPmtResultService")
public class NotifyAsyncPmtResultService {
    
    private Logger logger = LoggerFactory.getLogger(NotifyAsyncPmtResultService.class);
    
    @Autowired
    private PaymentOrderRepository paymentOrderRepository;
    
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    
    @Autowired
    private DealOrderRepository dealOrderRepository;
    
    @Autowired
    private ProcessPmtResultService processPmtResultService;
    
    /**
     * notify:(异步支付结果处理)
     *
     * @param payResultNotifyRes
     * <AUTHOR>
     * @date 2018年5月22日 上午10:37:53
     */
    public void procss(PayResultNotifyRes payResultNotifyRes){
        String pmtDealNo = payResultNotifyRes.getDealNo();
        String pmtState = payResultNotifyRes.getTradeState();
        String tPmtDealNo = payResultNotifyRes.getTradeNo();
        String retCode = payResultNotifyRes.getReturnCode();
        String retDesc = payResultNotifyRes.getDescription();
        String pmtOrgCode = payResultNotifyRes.getPmtInstCode();
        Date pmtCompleteDtm = payResultNotifyRes.getTradeEndDate();// 支付完成时间
        String pmtCheckDt = payResultNotifyRes.getProdDt();// 支付对账日期
        
        PaymentOrderPo paymentOrder = null;
        HighDealOrderDtlPo highDealOrderDtl = null;
        DealOrderPo dealOrder = null;
        paymentOrder = paymentOrderRepository.getByPmtDealNo(pmtDealNo);
        if(paymentOrder == null){
            logger.error("NotifyAsyncPmtResultService|notify|PmtDealNo:{}, paymentOrder is null", pmtDealNo);
            return;
        }

        List<HighDealOrderDtlPo> highDealOrderDtlList =  highDealOrderDtlRepository.getByDealNo(paymentOrder.getDealNo());
        if(CollectionUtils.isEmpty(highDealOrderDtlList)){
            logger.error("NotifyAsyncPmtResultService|notify|dealNo:{}, highDealOrderDtl is null", paymentOrder.getDealNo());
            return;
        }
        highDealOrderDtl = highDealOrderDtlList.get(0);

        dealOrder = dealOrderRepository.getByDealNo(paymentOrder.getDealNo());
        if(dealOrder == null){
            logger.error("NotifyAsyncPmtResultService|notify|dealNo:{}, dealOrder is null", paymentOrder.getDealNo());
            return;
        }

        // 支付状态“付款中”，更新
        if(TxPmtFlagEnum.PAYING.getKey().equals(paymentOrder.getTxPmtFlag())){
            String txPmtFlag = convertPaymentPayStat(pmtState);
            if(StringUtils.isEmpty(txPmtFlag)){
                logger.error("NotifyAsyncPmtResultService|notify|pmtDealNo:{},txPmtFlag is null, pmtState:{}",pmtDealNo, pmtState);
                return;
            }

            // 回填支付结果信息
            paymentOrder.settPmtDealNo(tPmtDealNo);
            paymentOrder.setRetCode(retCode);
            paymentOrder.setRetDesc(subReturnMsg(retDesc));
            paymentOrder.setPmtOrgCode(pmtOrgCode);

            // 支付完成时间
            if(pmtCompleteDtm == null){
                paymentOrder.setPmtCompleteDtm(new Date());
            }else{
                paymentOrder.setPmtCompleteDtm(pmtCompleteDtm);
            }

            // 自划款更新支付对账日期
            if(PaymentTypeEnum.SELF_DRAWING.getCode().equals(paymentOrder.getPaymentType())){
             // 支付对账日期
                paymentOrder.setPmtCheckDt(pmtCheckDt);
            }

            // 支付结果处理
            processPmtResultService.process(dealOrder, paymentOrder, highDealOrderDtl, txPmtFlag, false);
        }
    }
    
    /**
     * subReturnMsg:转化为UTF-8编码后如果超过128位，返回截取128位字节长度的字符串
     * 
     * @param retMsg
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年9月28日 下午9:41:31
     */
    private String subReturnMsg(String retMsg) {
        if (retMsg == null) {
            return retMsg;
        }
        StringBuilder subMsg = new StringBuilder(retMsg);
        try {
            if (retMsg.getBytes("UTF-8").length > 1024) {
                while (subMsg.toString().getBytes("UTF-8").length > 100) {
                    subMsg.deleteCharAt(subMsg.length() - 1);
                }
            }
        } catch (Exception e) {
            logger.error("返回信息大于100位且截取失败:", e);
            subMsg = new StringBuilder("返回信息大于100位且截取失败");
        }
        return subMsg.toString();
    }
    
    /**
     * convertPaymentPayStat:将支付系统返回的支付状态转换成中台支付状态
     * 
     * @param paymentPayStat
     *            支付系统支付状态
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年9月26日 下午4:23:12
     */
    private String convertPaymentPayStat(String paymentPayStat) {
        if (TradeStateEnum.TRADE_SUCESS.toString().equals(paymentPayStat)) {
            return TxPmtFlagEnum.SUCCESSFUL.getKey();
        }
        if (TradeStateEnum.TRADE_FAILURE.toString().equals(paymentPayStat)) {
            return TxPmtFlagEnum.FAILED.getKey();
        }

        return null;
    }
}

