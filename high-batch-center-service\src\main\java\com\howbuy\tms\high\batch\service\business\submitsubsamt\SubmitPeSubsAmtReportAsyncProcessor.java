/**
 * Copyright (c) 2020, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.submitsubsamt;

import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 异步上报股权认缴金额报表
 * <AUTHOR>
 * @date 2020/10/13 9:41
 * @since JDK 1.8
 */
@Service
public class SubmitPeSubsAmtReportAsyncProcessor {

    private static Logger logger = LogManager.getLogger(SubmitPeSubsAmtReportAsyncProcessor.class);

    @Autowired
    private SubmitPeSubsAmtReportFileProcessor submitPeSubsAmtReportFileProcessor;

    public void execute() {
        logger.info("SubmitPeSubsAmtReportAsyncProcessor|create SubmitThread");
        new Thread(ThreadTraceHelper.decorate(new SubmitTask(submitPeSubsAmtReportFileProcessor))).start();
    }

    /**
     * 异步执行线程类
     * @author: huaqiang.liu
     * @date: 2020/10/13 9:56
     * @since JDK 1.8
     */
    private class SubmitTask implements Runnable {

        private SubmitPeSubsAmtReportFileProcessor submitPeSubsAmtReportFileProcessor;

        public SubmitTask(SubmitPeSubsAmtReportFileProcessor submitPeSubsAmtReportFileProcessor) {
            this.submitPeSubsAmtReportFileProcessor = submitPeSubsAmtReportFileProcessor;
        }

        @Override
        public void run() {
            try {
                BaseResponse response = new BaseResponse();
                response.setReturnCode(ExceptionCodes.SUCCESS);
                response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
                submitPeSubsAmtReportFileProcessor.process(response);
            } catch (Throwable e) {
                logger.error("SubmitThread|SubmitThread|run 异步上报股权认缴金额报表失败，error:{}",e.getMessage(), e);
            }
        }
    }
}