package com.howbuy.tms.high.batch.service.business.pushassertcenteronehour;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.PartListUtil;
import com.howbuy.tms.high.batch.dao.po.order.CmCusttradeDirectPo;
import com.howbuy.tms.high.batch.service.business.pushassertcenteronehour.bean.PushAssetCenterMessage;
import com.howbuy.tms.high.batch.service.business.pushassertcenteronehour.bean.PushAssetCenterOrderDtlInfo;
import com.howbuy.tms.high.batch.service.common.PushAssetCenterMessageTypeEnum;
import com.howbuy.tms.high.batch.service.repository.CmCusttradeDirectRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:交易记录推送资产中心
 * @Author: yun.lu
 * Date: 2023/8/8 11:31
 */
@Service
@Order(3)
@Slf4j
public class PushAssetCenterOrderDtlServiceImpl implements PushAssetCenter {
    @Autowired
    private CmCusttradeDirectRepository cmCusttradeDirectRepository;
    private static final String HOW_BUY_CM = "HOWBUYCM";


    @Override
    public List<PushAssetCenterMessage> buildPushAssetInfo(Date lastDate, Date currentDate) {
        log.info("PushAssetCenterOrderDtlServiceImpl-交易记录推送资产中心,currentDate={},lastDate={}", currentDate, lastDate);
        // 1.查询时间段内推送资产中心的信息集合
        List<PushAssetCenterOrderDtlInfo> pushAssetInfoList = queryPushAssetInfo(lastDate, currentDate);
        // 2.构建消息发送实体
        if (CollectionUtils.isEmpty(pushAssetInfoList)) {
            log.info("PushAssetCenterOrderDtlServiceImpl-直销交易记录推送资产中心,没有新增数据,不需要推送,currentDate");
            return null;
        }
        List<PushAssetCenterMessage> pushAssetCenterMessageList = new ArrayList<>();
        List<List<PushAssetCenterOrderDtlInfo>> partList = PartListUtil.splitList(pushAssetInfoList, 50);
        int number = 1;
        for (List<PushAssetCenterOrderDtlInfo> subList : partList) {
            PushAssetCenterMessage pushAssetCenterMessage = new PushAssetCenterMessage();
            pushAssetCenterMessage.setDataList(JSON.toJSONString(subList));
            pushAssetCenterMessage.setMsgType(PushAssetCenterMessageTypeEnum.TRADE_ACK.getType());
            pushAssetCenterMessage.setNotifyDt(DateUtils.formatToString(currentDate, DateUtils.YYYYMMDD));
            pushAssetCenterMessage.setVersionId(DateUtils.formatToString(currentDate, DateUtils.YYYYMMDDHHMMSS) + String.format("%04d", number));
            pushAssetCenterMessageList.add(pushAssetCenterMessage);
            number++;
        }
        return pushAssetCenterMessageList;
    }


    private List<PushAssetCenterOrderDtlInfo> queryPushAssetInfo(Date lastDate, Date currentDate) {
        // 1.查询这段时间内的更新份额
        List<CmCusttradeDirectPo> poList = cmCusttradeDirectRepository.queryAckAssertByUpdateTime(lastDate, currentDate);
        // 2.构建推送资产中心订单明细
        if (CollectionUtils.isEmpty(poList)) {
            log.info("没查到需要推送资产中心的订单明细,lastDate={},currentDate={}", lastDate, currentDate);
            return new ArrayList<>();
        }
        List<PushAssetCenterOrderDtlInfo> dtlInfoList = new ArrayList<>();
        for (CmCusttradeDirectPo cmCusttradeDirectPo : poList) {
            PushAssetCenterOrderDtlInfo dtlInfo = new PushAssetCenterOrderDtlInfo();
            dtlInfo.setTxAcctNo(cmCusttradeDirectPo.getTxAcctNo());
            dtlInfo.setDisCode(cmCusttradeDirectPo.getDiscode());
            dtlInfo.setFundCode(cmCusttradeDirectPo.getFundcode());
            dtlInfo.setTaCode(HOW_BUY_CM);
            dtlInfo.setAckDt(cmCusttradeDirectPo.getTradedt());
            dtlInfo.setAckAmt(cmCusttradeDirectPo.getAckamt());
            dtlInfo.setTaTradeDt(cmCusttradeDirectPo.getTradedt());
            dtlInfo.setAppAmt(cmCusttradeDirectPo.getAppamt());
            dtlInfo.setNav(cmCusttradeDirectPo.getNav());
            dtlInfo.setFee(cmCusttradeDirectPo.getFee());
            dtlInfo.setmBusiCode(getMBusiCodeByCrmCode(cmCusttradeDirectPo.getBusicode(), cmCusttradeDirectPo.getAckvol()));
            dtlInfo.setAppVol(getVol(cmCusttradeDirectPo.getAppvol(),dtlInfo.getmBusiCode()));
            dtlInfo.setAckVol(getVol(cmCusttradeDirectPo.getAckvol(),dtlInfo.getmBusiCode()));
            dtlInfo.setDealNo(cmCusttradeDirectPo.getAppserialno());
            dtlInfo.setDealDtlNo(cmCusttradeDirectPo.getAppserialno());
            dtlInfo.setDisTransSeq(cmCusttradeDirectPo.getAppserialno());
            dtlInfo.setFundDivMode(cmCusttradeDirectPo.getDivmode());
            dtlInfo.setTxAckFlag(cmCusttradeDirectPo.getOrderstate());
            dtlInfo.setSalesModel("1");
            dtlInfo.setCurrencyUnit(cmCusttradeDirectPo.getCurrency());
            dtlInfo.setSubmitTaDt(cmCusttradeDirectPo.getNewTradeDt());
            dtlInfo.setHboneNo(cmCusttradeDirectPo.getHboneno());
            dtlInfo.setRecstat(getRecStat(cmCusttradeDirectPo));
            dtlInfo.setTransferPrice(cmCusttradeDirectPo.getTransferPrice());
            dtlInfo.setIsNoTradeTransfer(cmCusttradeDirectPo.getIsNoTradeTransfer());
            dtlInfo.setCurrentDate(currentDate);
            dtlInfo.setBalanceFactor(cmCusttradeDirectPo.getBalanceFactor());
            dtlInfo.setCpAcctNo("");
            dtlInfo.setAppDt("");
            dtlInfo.setBusiCode("");
            dtlInfo.setProtocolType("");
            dtlInfo.setProductChannel("");
            dtlInfo.setJoinDt("");
            dtlInfoList.add(dtlInfo);
            // 如果是基金转换,需要拆出转入的交易记录,并转义成申购业务类型数据
            if (BusinessCodeEnum.FUND_EXCHANGE.getCrmCode().equals(cmCusttradeDirectPo.getBusicode())) {
                // 转出的交易记录,订单明细号修改
                dtlInfo.setDealDtlNo(cmCusttradeDirectPo.getAppserialno() + "1");
                dtlInfo.setDealNo(cmCusttradeDirectPo.getAppserialno() + "1");
                dtlInfo.setDisTransSeq(cmCusttradeDirectPo.getAppserialno() + "1");
                // 转入交易记录拆分
                PushAssetCenterOrderDtlInfo dtlInfoNew = new PushAssetCenterOrderDtlInfo();
                BeanUtils.copyProperties(dtlInfo, dtlInfoNew);
                dtlInfoNew.setFundCode(cmCusttradeDirectPo.getTransferInFundCode());
                dtlInfoNew.setAckVol(cmCusttradeDirectPo.getTransferInAckVol());
                dtlInfoNew.setAckAmt(cmCusttradeDirectPo.getTransferInAckAmt());
                dtlInfoNew.setDealDtlNo(cmCusttradeDirectPo.getAppserialno() + "2");
                dtlInfoNew.setDealNo(cmCusttradeDirectPo.getAppserialno() + "2");
                dtlInfoNew.setBalanceFactor(cmCusttradeDirectPo.getTransferInBalanceFactor());
                dtlInfoNew.setDisTransSeq(cmCusttradeDirectPo.getAppserialno() + "2");
                dtlInfoNew.setNav(cmCusttradeDirectPo.getTransferInAckNav());
                dtlInfoNew.setmBusiCode(BusinessCodeEnum.PURCHASE.getMCode());
                dtlInfoList.add(dtlInfoNew);
            }
        }
        return dtlInfoList;
    }

    /**
     * 部分类型,因为海外会给负值,给资产中心需要特殊处理
     */
    public BigDecimal getVol(BigDecimal vol, String mBusiCode) {
        if (vol == null) {
            return vol;
        }
        if (BusinessCodeEnum.FORCE_SUBTRACT.getMCode().equals(mBusiCode)) {
            return vol.abs();
        }
        return vol;
    }

    private String getMBusiCodeByCrmCode(String crmCode, BigDecimal vol) {
        switch (crmCode) {
            case "122":
            case "13C":
            case "12B":
                return BusinessCodeEnum.PURCHASE.getMCode();
            case "124":
            case "136":
                // 注意,之所以136转为赎回,是因为表中通用字段对应的是转出方数据,转入数据需要拆出成为一条新交易记录,其在数据库中的字段也是特定字段
                return BusinessCodeEnum.REDEEM.getMCode();
            case "120":
                return BusinessCodeEnum.SUBS_RESULT.getMCode();
            case "142":
            case "13B":
                return BusinessCodeEnum.FORCE_REDEEM.getMCode();
            case "143":
                return BusinessCodeEnum.DIV.getMCode();
            case "144":
                return BusinessCodeEnum.FORCE_ADD.getMCode();
            case "145":
                return BusinessCodeEnum.FORCE_SUBTRACT.getMCode();
            case "151":
                return "1151";
            case "134":
                return BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode();
            case "135":
                return BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode();
            case "150":
                return BusinessCodeEnum.CFM_FUNDCLR.getMCode();
            case "19E":
                if (vol != null && vol.compareTo(BigDecimal.ZERO) > 0) {
                    return BusinessCodeEnum.FORCE_ADD.getMCode();
                } else {
                    return BusinessCodeEnum.FORCE_SUBTRACT.getMCode();
                }
            default:
                return BusinessCodeEnum.getMCodeByCrmCode(crmCode);
        }
    }


    private String getRecStat(CmCusttradeDirectPo cmCusttradeDirectPo) {
        // 如果是失效状态,直接返回失效状态
        if (YesOrNoEnum.YES.getCode().equals(cmCusttradeDirectPo.getRecStat())) {
            return YesOrNoEnum.YES.getCode();
        }
        if (cmCusttradeDirectPo.getModdt() != null && cmCusttradeDirectPo.getImportDt() != null && cmCusttradeDirectPo.getModdt().equals(cmCusttradeDirectPo.getImportDt())) {
            return "2";
        } else {
            return cmCusttradeDirectPo.getRecStat();
        }
    }


}
