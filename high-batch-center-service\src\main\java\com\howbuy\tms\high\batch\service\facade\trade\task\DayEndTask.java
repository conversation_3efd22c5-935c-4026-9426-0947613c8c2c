/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.trade.task;

import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.service.order.dayend.HighDayEndService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.CountDownLatch;

/**
 *
 * @description:确认日终
 * <AUTHOR>
 * @date 2018年12月10日 下午3:31:46
 * @since JDK 1.6
 */
public class DayEndTask implements Runnable {
    private static Logger logger = LogManager.getLogger(DayEndTask.class);

    private String taCode;
    private String tradeDt;
    private String sysCode;
    private HighDayEndService highDayEndService;
    private CountDownLatch latch;
    private ThreadExceptionStatus exStatus;

    public DayEndTask(String taCode, String tradeDt, String sysCode, HighDayEndService highDayEndService,
                      ThreadExceptionStatus exStatus, CountDownLatch latch) {
        this.taCode = taCode;
        this.tradeDt = tradeDt;
        this.sysCode = sysCode;
        this.highDayEndService = highDayEndService;
        this.latch = latch;
        this.exStatus = exStatus;
    }

    @Override
    public void run() {
        try {
            process(tradeDt, taCode);
        } catch (Throwable e) {
            logger.error("Error ", e);
            exStatus.setExsitException(true);
            exStatus.setException(e);
        } finally {
            latch.countDown();
        }
    }

    public void process(String taTradeDt, String taCode) {
        highDayEndService.execDayEnd(taTradeDt, taCode, sysCode);
    }
}
