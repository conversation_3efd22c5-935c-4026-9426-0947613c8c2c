package com.howbuy.tms.high.batch.service.business.doublewrite;

import com.howbuy.tms.high.batch.service.service.doublewrite.DoubleWriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/6/14 14:24
 */
@Slf4j
public class DoubleWriteSynchronization extends TransactionSynchronizationAdapter {

    private DoubleWriteHolder doubleWriteHolder;
    private DoubleWriteService doubleWriteService;
    public DoubleWriteSynchronization(DoubleWriteHolder doubleWriteHolder, DoubleWriteService doubleWriteService) {
        this.doubleWriteHolder = doubleWriteHolder;
        this.doubleWriteService = doubleWriteService;
    }

    @Override
    public void afterCompletion(int status) {
        if(status == STATUS_COMMITTED) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        doubleWriteService.updateData(doubleWriteHolder);
                    } catch (Throwable e) {
                        log.error("updateData error:", e);
                    }
                }
            }).start();
        }
    }
}
