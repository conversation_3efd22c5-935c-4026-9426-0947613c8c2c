/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.finafilemonitor;

import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.enums.busi.FinaFileTypeEnum;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.business.message.FinaFileMessageBean;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @api {MQ} fina_cxg_file_import_manual_queue
 * @apiGroup schedule
 * @apiName 文件手工导入
 * @apiDescription 文件手工导入
 */
@Service("finaCxgFileManualImportProcessor")
public class FinaCxgFileManualImportProcessor extends BatchMessageProcessor {
    private static final Logger logger = LogManager.getLogger(FinaCxgFileManualImportProcessor.class);

    @Value("${queue.fina.cxg.file.import.manual}")
    private String queueName;
    @Value("${cxg.file.manual.import.ackdt}")
    private String ackDt;
    @Value("${cxg.file.manual.import.mqkey}")
    private String mqKey;
    @Value("${cxg.file.manual.import.fileName}")
    private String fileName;

    private Map<String, FinaAbstractFileService> finaAbstractFileServices;

    @Override
    protected String getQuartMessageChannel() {
        return queueName;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        logger.info("FinaMsgNotifyService|doProcessMessage start.");

        logger.info("finaCxgFileManualProcessor|ackDt:{},mqKey:{},fileName:{}", ackDt, mqKey, fileName);
        if (StringUtils.isEmpty(ackDt) || StringUtils.isEmpty(mqKey) || StringUtils.isEmpty(fileName)) {
            return;
        }

        FinaFileMessageBean bean = new FinaFileMessageBean();
        bean.setExportDt(ackDt);
        bean.setDisFileType(mqKey);
        bean.setFileName(fileName);

        String fileType = FinaFileTypeEnum.getValue(bean.getDisFileType());
        if (StringUtils.isEmpty(fileType)) {
            return;
        }

        FinaAbstractFileService finaAbstractFileService = finaAbstractFileServices.get(bean.getDisFileType());
        if (finaAbstractFileService == null) {
            return;
        }
        finaAbstractFileService.process(bean);
        logger.info("FinaDirectionFileNotifyProcessor|doProcessMessage end.");
    }

    public void setFinaAbstractFileServices(Map<String, FinaAbstractFileService> finaAbstractFileServices) {
        this.finaAbstractFileServices = finaAbstractFileServices;
    }

}
