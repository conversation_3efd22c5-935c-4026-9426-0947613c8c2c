/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit;

import com.alibaba.fastjson.JSON;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.database.TxAppFlagEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankacctsensitiveinfo.QueryCustBankAcctSensitiveInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankacctsensitiveinfo.QueryCustBankCardSensitiveInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankacctsensitiveinfo.QueryCustBankCardSensitiveInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.order.DealOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend.FundShareMergeSubmitService;
import com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend.FundWithdrawSubmitServiceAbstract;
import com.howbuy.tms.high.batch.service.repository.DealOrderRepository;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @api {MQ} simuFundCheckOrderJobQueue
 * @apiGroup schedule
 * @apiName 对账订单批量上报调度消息接收处理器
 * @apiDescription 对账订单批量上报调度消息接收处理器
 */
public class CheckOrderSubmitMessageProcessor extends BatchMessageProcessor {
    private static Logger logger = LogManager.getLogger(CheckOrderSubmitMessageProcessor.class);

    @Value("${queue.simuFundCheckOrderJob}")
    private String simuFundCheckOrderJobQueue;

    @Value("${submit.retrieve.ctr}")
    private String submitRetrieveCtr;

    /**
     * 撤单上报服务
     */
    @Autowired
    private FundWithdrawSubmitServiceAbstract fundWithdrawSubmitService;
    @SuppressWarnings("unused")
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @SuppressWarnings("unused")
    @Autowired
    private WorkdayService workdayService;

    @Autowired
    private FundShareMergeSubmitService fundShareMergeSubmitService;

    @Autowired
    private DealOrderRepository dealOrderRepository;
    @Autowired
    private QueryCustBankAcctSensitiveInfoOuterService queryCustBankAcctSensitiveInfoOuterService;
    @Autowired
    private CheckOrderSubmitCommonService checkOrderSubmitCommonService;

    @Autowired
    private LockService lockService;


    @Override
    public void doProcessMessage(SimpleMessage message) {
        String lockKey = CacheKeyPrefix.IDEMPOTENT_KEY_LOCK_PREFIX + "CheckOrderSubmitMessageProcessor";
        // 运行锁，只有在获取了锁之后才准许执行
        boolean lock = lockService.getLock(lockKey, 600);
        if (!lock) {
            logger.info("CheckOrderSubmitMessageProcessor|doProcessMessage|get lock fail.");
            return;
        }
        logger.info("CheckOrderSubmitMessageProcessor|doProcessMessage|start! submitRetrieveCtr:{} -> 0-不控制 1-控制", submitRetrieveCtr);

        try {
            // 份额迁移上报
            processTransferSubmit();
        } catch (Exception e) {
            logger.error("CheckOrderSubmitMessageProcessor|err", e);
        } finally {
            lockService.releaseLock(lockKey);
        }
    }

    private void processTransferSubmit() {
        Date startDtm = DateUtils.addDay(new Date(), -10);
        List<SimuFundCheckOrderPo> shareTransferList = this.fundWithdrawSubmitService.getShareTransferCheckOrdersToSubmit(startDtm);
        if (CollectionUtils.isEmpty(shareTransferList)) {
            return;
        }

        // 数据库脱敏，重设证件号和卡号
        resetIdNoAndBankAcct(shareTransferList);
        List<SimuFundCheckOrderPo> subList = null;
        List<SimuFundCheckOrderManualSubmitVo> cancelList = new ArrayList<>();
        Map<String, List<SimuFundCheckOrderPo>> outMap = new HashMap<>();
        Map<String, List<SimuFundCheckOrderPo>> inMap = new HashMap<>();
        Map<String, List<SimuFundCheckOrderPo>> map = null;
        // 拆分份额转出outMap，份额转入inMap
        for (SimuFundCheckOrderPo vo : shareTransferList) {
            logger.info("SimuFundCheckOrderPo:{}", JSON.toJSONString(vo));
            // 是撤单
            if (TxAppFlagEnum.SELF_REVOCATION.getCode().equals(vo.getTxAppFlag())
                    || TxAppFlagEnum.FORCE_REVOCATION.getCode().equals(vo.getTxAppFlag())) {
                SimuFundCheckOrderManualSubmitVo submitVo = new SimuFundCheckOrderManualSubmitVo();
                BeanUtils.copyProperties(vo, submitVo);
                cancelList.add(submitVo);
                continue;
            }
            String key = vo.getTxAcctNo() + "" + vo.getCpAcctNo() + "" + vo.getmBusiCode() + "" + vo.getProtocolNo();
            if (BusinessCodeEnum.FUND_SHARE_TRANSFER_OUT.getMCode().equals(vo.getmBusiCode())) {
                map = outMap;
            } else {
                map = inMap;
            }

            if (map.containsKey(key)) {
                map.get(key).add(vo);
            } else {
                subList = new ArrayList<>();
                subList.add(vo);
                map.put(key, subList);
            }
        }
        SimuFundCheckOrderPo po = null;
        List<SimuFundCheckOrderPo> outList = null;
        List<SimuFundCheckOrderPo> inList = null;
        logger.info("outMap:{}", JSON.toJSONString(outMap));
        logger.info("inMap:{}", JSON.toJSONString(inMap));
        for(Map.Entry<String, List<SimuFundCheckOrderPo>> entry : outMap.entrySet()){
            outList = entry.getValue();
            po = outList.get(0);
            DealOrderPo dealOrder = getDealOrder(po.getDealNo());
            // 数据库脱敏，重设证件号和卡号
            resetIdNoAndBankAcct(dealOrder);
            String inKey = null;
            inKey = dealOrder.getTxAcctNo() + "" + dealOrder.getCpAcctNo() + "" + BusinessCodeEnum.FUND_SHARE_TRANSFER_IN.getMCode() + "" + dealOrder.getProtocolNo();
            inList = inMap.get(inKey);
            if (CollectionUtils.isEmpty(outList) || CollectionUtils.isEmpty(inList)) {
                logger.error("CheckOrderSubmitMessageProcessor|outList or inList is empty,outList,inList,outkey:{},inkey:{}", entry.getKey(), inKey);
                continue;
            }
            fundShareMergeSubmitService.submit(outList, inList, dealOrder);
        }

        for (SimuFundCheckOrderManualSubmitVo checkOrderPo : cancelList) {
            HighDealOrderDtlPo highDealOrderDtlPo = checkOrderSubmitCommonService.getHighOrderDtl(checkOrderPo.getDealDtlNo());
            if (highDealOrderDtlPo == null || StringUtils.isEmpty(highDealOrderDtlPo.getRefundDt())) {
                logger.error("CheckOrderSubmitMessageProcessor|dealDtlNo:{}, reFundDt is null", checkOrderPo.getDealDtlNo());
                continue;
            }
            // 设置上报申请时间
            checkOrderSubmitCommonService.setDefaultAppDtm(checkOrderPo, highDealOrderDtlPo.getRefundDt());
            // 上报处理
            checkOrderSubmitCommonService.process(checkOrderPo, fundWithdrawSubmitService);
        }

        logger.info("CheckOrderSubmitMessageProcessor|doProcessMessage|end.");
    }

    private DealOrderPo getDealOrder(String dealNo) {
        return dealOrderRepository.getByDealNo(dealNo);
    }

    /**
     * @description:数据库数据脱敏，需要实时查询相关信息
     * @param fundCheckOrderList
     * @return void
     * @author: chuanguang.tang
     * @date: 2020/12/8 10:17
     * @since JDK 1.8
     */
    private void resetIdNoAndBankAcct(List<SimuFundCheckOrderPo> fundCheckOrderList) {
        Map<String, QueryCustBankCardSensitiveInfoResult> map = new HashMap<>(16);
        QueryCustBankCardSensitiveInfoResult custInfo;
        for (SimuFundCheckOrderPo po : fundCheckOrderList) {
            String key = po.getTxAcctNo() + po.getCpAcctNo();
            custInfo = map.get(key);
            if (custInfo == null) {
                custInfo = getCustInfo(po.getTxAcctNo(), po.getCpAcctNo(), po.getDisCode());
                if (custInfo == null) {
                    logger.error("custInfo is null,txAcctNo:{},cpAcctNo:{}", po.getTxAcctNo(), po.getCpAcctNo());
                    continue;
                }
                map.put(key, custInfo);
            }
            po.setCustName(custInfo.getCustName());
        }
    }

    /**
     * @description:数据库数据脱敏，需要实时查询相关信息
     * @param po
     * @return void
     * @author: chuanguang.tang
     * @date: 2021/1/15 17:10
     * @since JDK 1.8
     */
    private void resetIdNoAndBankAcct(DealOrderPo po) {
        QueryCustBankCardSensitiveInfoResult result = getCustInfo(po.getTxAcctNo(), po.getCpAcctNo(), po.getDisCode());
        if (result == null) {
            return;
        }
        //po.setIdNo(result.getIdNo());
        po.setCustName(result.getCustName());
        //po.setBankAcct(result.getBankAcct());
    }

    /**
     * @description:数据库数据脱敏，需要实时查询相关信息
     * @param txAcctNo
     * @param cpAcctNo
     * @param disCode
     * @return com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult
     * @author: chuanguang.tang
     * @date: 2021/1/15 17:10
     * @since JDK 1.8
     */
    private QueryCustBankCardSensitiveInfoResult getCustInfo(String txAcctNo, String cpAcctNo, String disCode) {
        QueryCustBankCardSensitiveInfoContext ctx = new QueryCustBankCardSensitiveInfoContext();
        ctx.setTxAcctNo(txAcctNo);
        ctx.setCpAcctNo(cpAcctNo);
        ctx.setDisCode(disCode);
        return queryCustBankAcctSensitiveInfoOuterService.queryCustBankAcctSensitiveInfo(ctx);
    }

    @Override
    protected String getQuartMessageChannel() {
        return simuFundCheckOrderJobQueue;
    }
}
