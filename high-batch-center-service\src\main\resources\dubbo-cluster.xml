<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans  
        http://www.springframework.org/schema/beans/spring-beans.xsd  
        http://code.alibabatech.com/schema/dubbo  
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!-- 提供方应用信息，用于计算依赖关系 -->
<!--	<dubbo:application name="high-batch-center-service" />-->

	<!-- 注册中心 -->
	<!-- 产品中心 -->
	<dubbo:registry id="acc-center-server" protocol="zookeeper" address="${dubbo.registries.acc-center-server.address}"  file="${user.home}/output/dubboacccenterserver.cache"/>
	<dubbo:registry id="asset-center-remote" protocol="zookeeper" address="${dubbo.registries.asset-center-remote.address}"  file="${user.home}/output/dubboassetcenterremote.cache"/>
	<dubbo:registry id="center-member-service" protocol="zookeeper" address="${dubbo.registries.center-member-service.address}"  file="${user.home}/output/dubbocentermemberservice.cache"/>
	<dubbo:registry id="crm-core-server" protocol="zookeeper" address="${dubbo.registries.crm-core-server.address}"  file="${user.home}/output/dubbocrmcoreserver.cache"/>
	<dubbo:registry id="crm-nt-server" protocol="zookeeper" address="${dubbo.registries.crm-nt-server.address}"  file="${user.home}/output/dubbocrmntserver.cache"/>
	<dubbo:registry id="crm-td-server" protocol="zookeeper" address="${dubbo.registries.crm-td-server.address}"  file="${user.home}/output/dubbocrmtdserver.cache"/>
	<dubbo:registry id="elasticsearch-center-remote" protocol="zookeeper" address="${dubbo.registries.elasticsearch-center-remote.address}"  file="${user.home}/output/dubboelasticsearchcenterremote.cache"/>
	<dubbo:registry id="es-web-console" protocol="zookeeper" address="${dubbo.registries.es-web-console.address}"  file="${user.home}/output/dubboeswebconsole.cache"/>
	<dubbo:registry id="fbs-online-search-service" protocol="zookeeper" address="${dubbo.registries.fbs-online-search-service.address}"  file="${user.home}/output/dubbofbsonlinesearchservice.cache"/>
	<dubbo:registry id="fbs-online-service" protocol="zookeeper" address="${dubbo.registries.fbs-online-service.address}"  file="${user.home}/output/dubbofbsonlineservice.cache"/>
	<dubbo:registry id="fin-online-service" protocol="zookeeper" address="${dubbo.registries.fin-online-service.address}"  file="${user.home}/output/dubbofinonlineservice.cache"/>
	<dubbo:registry id="ftx-batch-remote" protocol="zookeeper" address="${dubbo.registries.ftx-batch-remote.address}"  file="${user.home}/output/dubboftxbatchremote.cache"/>
	<dubbo:registry id="ftx-order-remote" protocol="zookeeper" address="${dubbo.registries.ftx-order-remote.address}"  file="${user.home}/output/dubboftxorderremote.cache"/>
	<dubbo:registry id="high-order-search-remote" protocol="zookeeper" address="${dubbo.registries.high-order-search-remote.address}"  file="${user.home}/output/dubbohighordersearchremote.cache"/>
	<dubbo:registry id="high-order-trade-remote" protocol="zookeeper" address="${dubbo.registries.high-order-trade-remote.address}"  file="${user.home}/output/dubbohighordertraderemote.cache"/>
	<dubbo:registry id="howbuy-auth-service" protocol="zookeeper" address="${dubbo.registries.howbuy-auth-service.address}"  file="${user.home}/output/dubbohowbuyauthservice.cache"/>
	<dubbo:registry id="howbuy-fund-server" protocol="zookeeper" address="${dubbo.registries.howbuy-fund-server.address}"  file="${user.home}/output/dubbohowbuyfundserver.cache"/>
	<dubbo:registry id="howbuy-simu-server" protocol="zookeeper" address="${dubbo.registries.howbuy-simu-server.address}"  file="${user.home}/output/dubbohowbuysimuserver.cache"/>
	<dubbo:registry id="lct-online" protocol="zookeeper" address="${dubbo.registries.lct-online.address}"  file="${user.home}/output/dubbolctonline.cache"/>
	<dubbo:registry id="message-remote" protocol="zookeeper" address="${dubbo.registries.message-remote.address}"  file="${user.home}/output/dubbomessageremote.cache"/>
	<dubbo:registry id="otc-center-search-remote" protocol="zookeeper" address="${dubbo.registries.otc-center-search-remote.address}"  file="${user.home}/output/dubbootccentersearchremote.cache"/>
	<dubbo:registry id="param-server" protocol="zookeeper" address="${dubbo.registries.param-server.address}"  file="${user.home}/output/dubboparamserver.cache"/>
	<dubbo:registry id="pay-online-server" protocol="zookeeper" address="${dubbo.registries.pay-online-server.address}"  file="${user.home}/output/dubbopayonlineserver.cache"/>
	<dubbo:registry id="product-center-remote" protocol="zookeeper" address="${dubbo.registries.product-center-remote.address}"  file="${user.home}/output/dubboproductcenterremote.cache"/>
	<dubbo:registry id="high-batch-center" protocol="zookeeper" address="${dubbo.registries.high-batch-center-remote.address}"  file="${user.home}/output/dubbohighbatchcenter.cache"/>
	<dubbo:registry id="howbuy-cms-server" protocol="zookeeper" address="${dubbo.registries.howbuy-cms-server.address}" file="${user.home}/output/dubbohowbuycmsserver.cache"  />
	<dubbo:registry id="acc-console-web" protocol="zookeeper" address="${dubbo.registries.acc-console-web.address}"  file="${user.home}/output/dubboaccconsoleweb.cache"/>
	<dubbo:registry id="order-center-remote" protocol="zookeeper" address="${dubbo.registries.order-center-remote.address}"  file="${user.home}/output/dubboordercenterremote.cache"/>

	<!-- 多协议在配置 -->
	<dubbo:protocol name="${dubbo.protocol.name}" port="${dubbo.protocol.port}" threads="${dubbo.thread.num}" threadpool="fixed" dispatcher="all" queues="0" />

	<!-- 服务端超时时间 -->
	<dubbo:provider  timeout="${dubbo.provider.timeout}" retries = "0"/>

	<!-- 消费者超时时间 -->
	<dubbo:consumer timeout="${dubbo.consumer.timeout}" retries = "0"/>
</beans>