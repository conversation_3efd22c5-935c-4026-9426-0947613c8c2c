/**
 * Copyright (c) 2020, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.autobatch;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.high.batch.facade.trade.dayinit.HighDayInitResponse;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.service.batch.dayinit.DayInitService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * 请在此添加描述
 *
 * <AUTHOR>
 * @date 2020/11/4 17:48
 * @since JDK 1.8
 */
@Service("dayInitProcessorService")
public class DayInitProcessorService {

    private static final Logger logger = LogManager.getLogger(DayInitProcessorService.class);
    private static final String LOCK_PREFIX = CacheKeyPrefix.HIGH_LOCK_CONCURRENT_PREFIX + "DAY_INIT";

    @Autowired
    private DayInitService dayInitService;
    @Qualifier("cache.lockService")
    @Autowired
    private LockService lockService;
    public HighDayInitResponse execute() {
        logger.info("开始日初始化");
        HighDayInitResponse resp = new HighDayInitResponse();
        if (!lockService.getLock(LOCK_PREFIX)) {
            logger.info("DayInitProcessorService|execute|getLock false lockKey:{}", LOCK_PREFIX);
            resp.setReturnCode(ExceptionCodes.ORDER_CENTER_SYSTEM_ERROR);
            resp.setDescription("获取锁失败");
            return resp;
        }
        try {
            // 流程检查
            dayInitService.flowCheck(SysCodeEnum.BATCH_HIGH.getCode());
            // 执行日初始化
            dayInitService.execDayInit(SysCodeEnum.BATCH_HIGH.getCode());
            resp.setReturnCode(ExceptionCodes.SUCCESS);
            resp.setDescription("日初始化成功");
        } catch (BatchException e) {
            logger.error("code = " + e.getCode() + "\t" + "value = " + e.getValue() + "\t" + e);
            resp.setReturnCode(e.getCode());
            resp.setDescription(e.getValue());
        } catch (Exception e) {
            logger.error("系统异常,", e);
            resp.setReturnCode(ExceptionCodes.ORDER_CENTER_SYSTEM_ERROR);
            resp.setDescription(e.getMessage());
        } finally {
            lockService.releaseLock(LOCK_PREFIX);
        }

        logger.info("日初始化结束:{}", JSON.toJSONString(resp));
        return resp;

    }
}