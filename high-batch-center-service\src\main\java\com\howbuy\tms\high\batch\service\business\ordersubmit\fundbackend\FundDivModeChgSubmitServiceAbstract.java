/**
 *Copyright (c) 2016, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.outerservice.fbsonline.funddivmodechg.FundDivModeChgContext;
import com.howbuy.tms.common.outerservice.fbsonline.funddivmodechg.FundDivModeChgOuterService;
import com.howbuy.tms.common.outerservice.fbsonline.funddivmodechg.FundDivModeChgResult;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.service.business.ordersubmit.BaseResponse;
import com.howbuy.tms.high.batch.service.business.ordersubmit.AbstractCheckOrderSubmitService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:修改分红方式接口（接口GN_FDS_NEW_02）服务
 * @reason:
 * <AUTHOR>
 * @date 2016年9月14日 下午1:59:49
 * @since JDK 1.6
 */
@Service("fundDivModeChgSubmitService")
public class FundDivModeChgSubmitServiceAbstract extends AbstractCheckOrderSubmitService {
    private static Logger logger = LogManager.getLogger(FundDivModeChgSubmitServiceAbstract.class);
    @Autowired
    private FundDivModeChgOuterService fundDivModeChgOuterService;

    @Override
    public boolean userThis(String businessCode) {
        return BusinessCodeEnum.DIV_MODE_CHANGE.getMCode().equals(businessCode);
    }

    @Override
    protected BaseResponse doSubmit(SimuFundCheckOrderManualSubmitVo fundCheckOrderPo) {
        FundDivModeChgContext context = generateRequest(fundCheckOrderPo);
        FundDivModeChgResult result = fundDivModeChgOuterService.doDivModeChg(context);
        BaseResponse baseResponse = new BaseResponse();
        logger.info("FundWithdrawSubmitService|result:" + JSON.toJSONString(result));
        BeanUtils.copyProperties(result, baseResponse);
        return baseResponse;
    }

    @Override
    protected void submitSuccess(SimuFundCheckOrderPo fundCheckOrderPo) {
    }

    @Override
    protected void submitFail(SimuFundCheckOrderPo fundCheckOrderPo) {
    }

    private FundDivModeChgContext generateRequest(SimuFundCheckOrderPo fundCheckOrderPo) {
        FundDivModeChgContext context = new FundDivModeChgContext();

        // 交易渠道 TO 交易渠道
        context.setTradeChannel(fundCheckOrderPo.getTxChannel());
        // 分销机构 TO 分销机构代码
        context.setDisCode(fundCheckOrderPo.getDisCode());
        // 网点号 TO 网点代码
        context.setOutletCode(fundCheckOrderPo.getOutletCode());
        // 申请日期 TO 申请日期
        context.setAppDt(fundCheckOrderPo.getAppDate());
        // 申请时间 TO 申请时间
        context.setAppTm(fundCheckOrderPo.getAppTime());
        // IP地址 TO 交易IP
        context.setIpAddress(fundCheckOrderPo.getIpAddress());
        // 上报订单号 TO 中台订单号
        context.setSubmitDealNo(fundCheckOrderPo.getSubmitDealNo());
        // 风险确认标志
        context.setRiskFlag(fundCheckOrderPo.getRiskFlag());
        // 交易账号
        context.setTxAcctNo(fundCheckOrderPo.getTxAcctNo());
        // 投资者类型
        context.setInvstType(fundCheckOrderPo.getInvstType());
        // 客户姓名
        context.setCustName(fundCheckOrderPo.getCustName());
        // 证件类型
        //context.setIdType(fundCheckOrderPo.getIdType());
        // 证件号码
        //context.setIdNo(fundCheckOrderPo.getIdNo());
        // 基金代码
        context.setFundCode(fundCheckOrderPo.getFundCode());
        // 份额类型
        context.setShareClass(fundCheckOrderPo.getFundShareClass());
        // 基金分红方式 TO 目标分红方式
        context.setDivMode(fundCheckOrderPo.getFundDivMode());
        // 备注
        context.setMemo(fundCheckOrderPo.getMemo());
        // 经办人姓名
        context.setTransactorName(fundCheckOrderPo.getTransactorName());
        // 经办人证件号码
        context.setTransactorIdNo(fundCheckOrderPo.getTransactorIdNo());
        // 经办人证件类型
        context.setTransactorIdType(fundCheckOrderPo.getTransactorIdType());
        context.setProtocolNo(fundCheckOrderPo.getProtocolNo());
        context.setCpAcctNo(fundCheckOrderPo.getCpAcctNo());

        return context;
    }
}
