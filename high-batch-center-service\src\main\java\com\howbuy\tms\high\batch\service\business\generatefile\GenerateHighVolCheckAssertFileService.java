/**
 * Copyright (c) 2020, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.generatefile;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.BusinessProcessingStepEnum;
import com.howbuy.tms.common.enums.database.FileOptionStatus;
import com.howbuy.tms.common.enums.database.FileTypeEnum;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessRecPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaBusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.order.ExportHeVolCheckFileRecPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.facade.enums.HeExportFileTypeEnum;
import com.howbuy.tms.high.batch.service.business.generatefile.bean.CreateAssetTxtFileParam;
import com.howbuy.tms.high.batch.service.common.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.FileSdkUtil;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.CmFileProcessRecRepository;
import com.howbuy.tms.high.batch.service.repository.ExportHeVolCheckFileRecRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.bean.TxtFileExportContext;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.impl.ExportHighVolAssertFileService;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description: 生成份额对账文件
 * @date 2020/1/15 16:11
 * @since JDK 1.7
 */
@Service
public class GenerateHighVolCheckAssertFileService extends AbstractGenerateAssertFileService {
    private static Logger logger = LogManager.getLogger(GenerateHighVolCheckAssertFileService.class);


    @Value("${export.high.volCheckFile.fileFileds}")
    private String fileFileds;

    @Value("${export.high.volCheckFile.fileFiledsName}")
    private String fileFiledsName;

    @Autowired
    private CmFileProcessRecRepository cmFileProcessRecRepository;

    @Autowired
    private ExportHeVolCheckFileRecRepository exportHeVolCheckFileRecRepository;

    @Autowired
    private ExportHighVolAssertFileService exportHighVolAssertFileService;

    public void generate() throws Exception {
        String tradeDt = workdayService.getSaleSysCurrWorkay();

        // 1.判断当前日期是否是工作日
        String nowDay = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        if (!StringUtils.equals(nowDay, tradeDt)) {
            logger.info("GenerateHighVolCheckFileService|generate|nowDay:{} is not work day :{}", nowDay, tradeDt);
            return;
        }

        // 2.判断ok文件是否已经生成
        FundFileProcessRecPo processPo = fundFileProcessRecRepository.selectByUniqueKey(tradeDt, FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode(), Constant.ALL_TA_CODE);
        if (processPo != null && ProcessStatusEnum.NOTICE_SUCCESS.getCode().equals(processPo.getProcessStatus())) {
            logger.info("GenerateHighCmBlackFileService|generate|ok文件{}已生成", processPo.getFileName());
            return;
        }

        //3.查询确认处理日终各TA节点信息，确认日终处理没有跑结束就算份额对账文件到了也不出
        List<TaBusinessBatchFlowPo> flowList = taBusinessBatchFlowRepository.selectTaBusinessBatchFlowByTaskId(tradeDt, BusinessProcessingStepEnum.BPS_ACK_DAY_END_PROCESS.getCode(), SysCodeEnum.BATCH_HIGH.getCode());
        if (CollectionUtils.isEmpty(flowList)) {
            logger.info("flowList is empty,tradeDt:{},taskId:{},sysCode:{}", tradeDt, BusinessProcessingStepEnum.BPS_ACK_DAY_END_PROCESS.getCode(), SysCodeEnum.BATCH_HIGH.getCode());
            return;
        }

        //4.查询交易确认文件已出文件信息
        Map<String, FundFileProcessRecPo> recPoMap = fundFileProcessRecRepository.getMapByFileTypeAndTaTradeDt(FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode(), tradeDt);
        CountDownLatch latch = new CountDownLatch(flowList.size());
        //5.针对未出文件的TA,生成文件
        for (TaBusinessBatchFlowPo po : flowList) {
            //如果节点没有成功直接跳过
            if (!BatchStatEnum.PROCESS_SUCCESS.getKey().equals(po.getFlowStat())) {
                latch.countDown();
                continue;
            }
            FundFileProcessRecPo recPo = recPoMap.get(po.getTaCode());
            //判断文件记录是否生成，如果没有则生成文件记录
            if (recPo == null) {
                FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
                fileSdkPathInfo.setFileName(getCheckFileName(po.getTradeDt(), po.getTaCode(), null));
                fileSdkPathInfo.setMiddlePath(getPath(tradeDt));
                fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.VOL_CHECK_FILE_PATH);
                String absolutePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
                recPo = addFundFileProcessRec(po.getTradeDt(), po.getTaCode(), FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode(), absolutePath);
            }

            //判断文件是否已经成功
            if (FileOpStatusEnum.MAKE_SUCCESSFUL.getKey().equals(recPo.getFileOpStatus())) {
                latch.countDown();
                continue;
            }
            THREAD_POOL.execute(ThreadTraceHelper.decorate(new GenerateHighVolCheckAssertFileService.VolCheckFileTask(recPo, latch)));
        }

        try {
            //等待处理
            latch.await();
        } catch (InterruptedException e) {
            logger.error("", e);
        }
        // 直销处理
        cmProcess(tradeDt, recPoMap);
        //发送文件消息&OK文件消息
        sendFileMessage(flowList, tradeDt);
    }

    private void cmProcess(String tradeDt, Map<String, FundFileProcessRecPo> recPoMap) throws Exception {
        // 处理直销文件
        boolean fileProcess = crmFileHasProcess(tradeDt, FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode());
        if (!fileProcess) {
            logger.info("份额销文件还没有处理完成,tradeDt={}", tradeDt);
            return;
        }
        BigDecimal latestVersion = cmFileProcessRecRepository.selectLatestDayVersion();
        FundFileProcessRecPo recPo = null;
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setFileName(getCheckFileName(tradeDt, Constant.HOWBUY_CM_TA, latestVersion + ""));
        fileSdkPathInfo.setMiddlePath(getPath(tradeDt));
        fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.VOL_CHECK_FILE_PATH);
        String absolutePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
        if (recPoMap.isEmpty()) {
            recPo = addFundFileProcessRec(tradeDt, Constant.HOWBUY_CM_TA, FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode(), absolutePath);
        } else {
            recPo = recPoMap.get(Constant.HOWBUY_CM_TA);
            if (recPo == null) {
                //判断文件记录是否生成，如果没有则生成文件记录
                recPo = addFundFileProcessRec(tradeDt, Constant.HOWBUY_CM_TA, FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode(), absolutePath);
            }
        }

        //判断文件是否已经成功
        if (FileOpStatusEnum.MAKE_SUCCESSFUL.getKey().equals(recPo.getFileOpStatus())) {
            logger.info("cmFileProcess ack file has process ");
            return;
        } else {
            CountDownLatch latch = new CountDownLatch(1);
            THREAD_POOL.execute(ThreadTraceHelper.decorate(new GenerateHighVolCheckAssertFileService.VolCheckFileTask(recPo, latch)));
            try {
                //等待处理
                latch.await();
            } catch (InterruptedException e) {
                logger.error("", e);
            }
        }

    }

    /**
     * 中间路径
     *
     * @param tradeDt 交易日
     * @return 中间路径
     */
    @Override
    public String getPath(String tradeDt) {
        return tradeDt + File.separator;
    }

    /**
     * ok文件文件名
     *
     * @return 中间路径
     */
    @Override
    public String getOkFileName(String tradeDt) {
        return tradeDt + "_he_vol_check.txt.ok";
    }


    @Override
    public boolean addNewFile(FundFileProcessRecPo recPo, String lastWorkDay) throws Exception {
        TxtFileExportContext txtFileExportContext = new TxtFileExportContext();
        txtFileExportContext.setRelationPath(getPath(recPo.getTaTradeDt()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("taCode", recPo.getTaCode());
        paramMap.put("taTradeDt", recPo.getTaTradeDt());
        paramMap.put("lastWorkDay", lastWorkDay);
        txtFileExportContext.setParams(paramMap);
        txtFileExportContext.setFileName(getFileNameByAbsolutePath(recPo.getFileName()));
        exportHighVolAssertFileService.process(txtFileExportContext);
        return true;
    }

    /**
     * 份额对账文件名
     *
     * @param tradeDt       交易日历
     * @param latestVersion 上个版本号
     * @param taCode        taCode
     * @return 份额对账文件名
     */
    @Override
    public String getCheckFileName(String tradeDt, String taCode, String latestVersion) {
        String fileName = "";
        if (latestVersion == null) {
            fileName += tradeDt + "_he_vol_check_" + taCode + ".txt";
        } else {
            fileName += tradeDt + "_v" + latestVersion + "_he_vol_check_" + taCode + ".txt";
        }
        return fileName;
    }


    /**
     * 根路径
     *
     * @return 根路径Key
     */
    @Override
    public String getBusinessCode() {
        return FilePathStoreBusinessCodeConfig.VOL_CHECK_FILE_PATH;
    }

    /**
     * @param flowList TA批处理节点列表
     * @param tradeDt  交易日期
     * @return void
     * @description: 发送文件消息
     * @author: hongdong.xie
     * @date: 2020/1/15 15:19
     * @since JDK 1.7
     */
    private void sendFileMessage(List<TaBusinessBatchFlowPo> flowList, String tradeDt) throws Exception {
        //5.检查所有TA是否生成成功
        Map<String, FundFileProcessRecPo> recMap = fundFileProcessRecRepository.getMapByFileTypeAndTaTradeDt(FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode(), tradeDt);
        boolean isAllSuccess = true;
        // 处理代销
        for (TaBusinessBatchFlowPo po : flowList) {
            FundFileProcessRecPo recPo = recMap.get(po.getTaCode());
            //确认处理日终执行成功且文件成功成功
            if (recPo == null || !FileOptionStatus.CREATE_SUCCESS.getCode().equals(recPo.getFileOpStatus()) || !BatchStatEnum.PROCESS_SUCCESS.getKey().equals(po.getFlowStat())) {
                isAllSuccess = false;
                continue;
            }
            //发送消息,只有未处理的才发送消息
            if (!ProcessStatusEnum.NOT_PROCESS.getCode().equals(recPo.getProcessStatus())) {
                continue;
            }
            //消息发送
            sendMessage(recPo.getTaTradeDt(), recPo.getTaCode(), recPo.getFileName(), HeExportFileTypeEnum.EXPORT_VOL.getKey());
            //设置处理状态
            updateFileStatus(recPo);
        }

        // 处理直销
        FundFileProcessRecPo recPo = recMap.get(Constant.HOWBUY_CM_TA);
        if (recPo == null || !FileOptionStatus.CREATE_SUCCESS.getCode().equals(recPo.getFileOpStatus())) {
            isAllSuccess = false;
        } else {
            //发送消息,只有未处理的才发送消息
            if (ProcessStatusEnum.NOT_PROCESS.getCode().equals(recPo.getProcessStatus())) {
                //消息发送
                sendMessage(recPo.getTaTradeDt(), recPo.getTaCode(), recPo.getFileName(), HeExportFileTypeEnum.EXPORT_VOL.getKey());
                //设置处理状态
                updateFileStatus(recPo);
            }
        }

        if (isAllSuccess) {
            //生成OK文件&发送
            FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
            fileSdkPathInfo.setFileName(getOkFileName(tradeDt));
            fileSdkPathInfo.setMiddlePath(getPath(tradeDt));
            fileSdkPathInfo.setBusinessCode(getBusinessCode());
            String absolutePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
            //校验OK文件是否已经生成
            FundFileProcessRecPo processPo = fundFileProcessRecRepository.selectByUniqueKey(tradeDt, FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode(), Constant.ALL_TA_CODE);
            if (processPo == null) {
                processPo = addFundFileProcessRec(tradeDt, Constant.ALL_TA_CODE, FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode(), absolutePath);
            }
            if (!FileOpStatusEnum.MAKE_SUCCESSFUL.getKey().equals(processPo.getFileOpStatus())) {
                fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, processPo.getFileType(), processPo.getTaCode(), FileOpStatusEnum.PROCESSING.getKey(), processPo.getFileOpStatus());
                writeOkFile(String.valueOf(flowList.size() + 1), tradeDt);
                fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, processPo.getFileType(), processPo.getTaCode(), FileOpStatusEnum.MAKE_SUCCESSFUL.getKey(), FileOpStatusEnum.PROCESSING.getKey());
            }

            if (ProcessStatusEnum.NOTICE_SUCCESS.getCode().equals(processPo.getProcessStatus())) {
                return;
            }

            //消息发送
            sendMessage(processPo.getTaTradeDt(), processPo.getTaCode(), processPo.getFileName(), HeExportFileTypeEnum.EXPORT_VOL.getKey());
            //设置处理状态
            FundFileProcessRecPo rPo2 = new FundFileProcessRecPo();
            rPo2.setRecordNo(processPo.getRecordNo());
            rPo2.setProcessStatus(ProcessStatusEnum.NOTICE_SUCCESS.getCode());
            fundFileProcessRecRepository.updateByRecordNo(rPo2);
            OpsMonitor.warn(tradeDt + "份额资产中心文件已推送", OpsMonitor.INFO);
        }
    }


    private void updateFileStatus(FundFileProcessRecPo recPo) {
        FundFileProcessRecPo rPo = new FundFileProcessRecPo();
        rPo.setRecordNo(recPo.getRecordNo());
        rPo.setFileOpStatus(FileOptionStatus.CREATE_SUCCESS.getCode());
        rPo.setProcessStatus(ProcessStatusEnum.NOTICE_SUCCESS.getCode());
        fundFileProcessRecRepository.updateByRecordNo(rPo);
    }

    /**
     * @description: 份额对账文件处理Task
     * @author: hongdong.xie
     * @date: 2020/1/15 18:09
     * @since JDK 1.7
     */
    private class VolCheckFileTask implements Runnable {

        private FundFileProcessRecPo recPo;

        private CountDownLatch latch;

        public VolCheckFileTask(FundFileProcessRecPo recPo, CountDownLatch latch) {
            this.recPo = recPo;
            this.latch = latch;
        }

        @Override
        public void run() {
            String tradeDt = recPo.getTaTradeDt();
            String fileType = recPo.getFileType();
            String taCode = recPo.getTaCode();
            String fileOpStatus = recPo.getFileOpStatus();

            try {
                //1.更新文件处理记录为处理中
                int count = fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, fileType, taCode, FileOpStatusEnum.PROCESSING.getKey(), fileOpStatus);
                if (count != 1) {
                    logger.info("updateFileOpStatusByPrimaryKeyWithVersion fail,tradeDt:{},fileType:{},taCode:{},newFileStatus:{},oldFileOpStatus:{}", tradeDt, fileType, taCode, FileOpStatusEnum.PROCESSING.getKey(), fileOpStatus);
                    return;
                }
                //2.文件生成
                addNewFile(recPo, null);
                //3.更新文件处理记录为生成成功
                count = fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_SUCCESSFUL.getKey(), FileOpStatusEnum.PROCESSING.getKey());
                if (count != 1) {
                    logger.info("updateFileOpStatusByPrimaryKeyWithVersion fail,tradeDt:{},fileType:{},taCode:{},newFileStatus:{},oldFileOpStatus:{}", tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_SUCCESSFUL.getKey(), FileOpStatusEnum.PROCESSING.getKey());
                    return;
                }
            } catch (Exception e) {
                logger.error("", e);
                //更新文件处理记录为生成失败
                fundFileProcessRecRepository.updateFileOpStatusByPrimaryKey(tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_FAILED.getKey());
                logger.info("updateFileOpStatusByPrimaryKey,tradeDt:{},fileType:{},taCode:{},fileStatus:{}", tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_FAILED.getKey());
            } finally {
                latch.countDown();
            }
        }
    }

    /**
     * @description: 生成文件
     * @author: hongdong.xie
     * @date: 2020/1/15 14:06
     * @since JDK 1.7
     */
    private void generateFile(FundFileProcessRecPo recPo) {
        logger.info("generate vol check file start,recPo:{}", JSON.toJSONString(recPo));
        //生成数据

        int totalCount = 0;
        if (Constant.HOWBUY_CM_TA.equals(recPo.getTaCode())) {
            exportHeVolCheckFileRecRepository.deleteCmBatchVolFile(recPo.getTaCode());
            totalCount = exportHeVolCheckFileRecRepository.addCmBatchVolFile(recPo.getTaCode());
        } else {
            exportHeVolCheckFileRecRepository.deleteBatchVolFile(recPo.getTaCode());
            totalCount = exportHeVolCheckFileRecRepository.addBatchVolFile(recPo.getTaCode());
        }

        CreateAssetTxtFileParam createAssetTxtFileParam = new CreateAssetTxtFileParam();
        createAssetTxtFileParam.setTradeDt(recPo.getTaTradeDt());
        createAssetTxtFileParam.setFileFieldsName(fileFiledsName);
        createAssetTxtFileParam.setFileFields(fileFileds);
        createAssetTxtFileParam.setBusinessCode(getBusinessCode());
        createAssetTxtFileParam.setFilePath(getPath(recPo.getTaTradeDt()));
        createAssetTxtFileParam.setFileName(getFileNameByAbsolutePath(recPo.getFileName()));
        createAssetTxtFileParam.setTaCode(recPo.getTaCode());
        createAssetTxtFileParam.setTotalCount(totalCount);
        //生成文件
        boolean flag = createTXTFile(createAssetTxtFileParam);
        if (!flag) {
            logger.error("createTXTFile>>failed>>>>>createAssetTxtFileParam:{}", JSON.toJSONString(createAssetTxtFileParam));
        }
        logger.info("generate vol check file end,createAssetTxtFileParam:{}", JSON.toJSONString(createAssetTxtFileParam));
    }

    /**
     * @param tradeDt  交易日
     * @param taCode   TA代码
     * @param pageNo   页码
     * @param pageSize 每页大小
     * @return com.github.pagehelper.Page<com.howbuy.tms.batch.dao.po.order.ExportVolCheckFileRecPo>
     * @description: 分页查询份额对账数据
     * @author: hongdong.xie
     * @date: 2020/1/14 18:14
     * @since JDK 1.7
     */
    @Override
    protected Page<ExportHeVolCheckFileRecPo> getData(final String tradeDt, final String taCode, final int pageNo, final int pageSize) {
        Page<ExportHeVolCheckFileRecPo> page = exportHeVolCheckFileRecRepository.getByTradeDtTaCode(taCode, pageNo, pageSize);
        if (CollectionUtils.isEmpty(page)) {
            return null;
        }
        return page;
    }

}
