/**
 * Copyright (c) 2020, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.generatefile;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.howbuy.dfile.HTextFileWriter;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.enums.database.FileOptionEnum;
import com.howbuy.tms.common.enums.database.FileTypeEnum;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.common.utils.BeanConvertUtil;
import com.howbuy.tms.common.utils.FileUtils;
import com.howbuy.tms.high.batch.dao.po.batch.CmFileProcessRecPo;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessRecPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.business.generatefile.bean.CreateAssetTxtFileParam;
import com.howbuy.tms.high.batch.service.business.message.ExportFileMessageBean;
import com.howbuy.tms.high.batch.service.common.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.FileSdkUtil;
import com.howbuy.tms.high.batch.service.repository.CmFileProcessRecRepository;
import com.howbuy.tms.high.batch.service.repository.FundFileProcessRecRepository;
import com.howbuy.tms.high.batch.service.repository.TaBusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import com.howbuy.tms.high.batch.service.service.file.fileexport.CommonConfService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @description: 文件生成抽象类
 * @date 2020/1/7 16:33
 * @since JDK 1.7
 */
@Service
public abstract class AbstractGenerateAssertFileService {

    private static Logger logger = LogManager.getLogger(AbstractGenerateAssertFileService.class);
    protected static final String SEPARATOR = "|";

    protected static final String ALL_TA_CODE = "**";
    /**
     * 队列容量
     */
    private static final int POOL_CAPACITY = 102400;
    @Value("${tms.high.Fund.File.MessageQueue}")
    private String tmsFundFileQueue;

    @Autowired
    protected FundFileProcessRecRepository fundFileProcessRecRepository;
    @Autowired
    private CmFileProcessRecRepository cmFileProcessRecRepository;
    @Autowired
    protected WorkdayService workdayService;
    @Autowired
    protected TaBusinessBatchFlowRepository taBusinessBatchFlowRepository;
    @Autowired
    protected CommonConfService commonConfService;
    /**
     * 线程池
     */
    protected static final ExecutorService THREAD_POOL = createFixedThreadPool("AbstractGenerateFileService", 10);

    /**
     * 获取绝对路径中的文件名
     *
     * @param absolutePath 绝对路径
     * @return 文件名
     */
    public String getFileNameByAbsolutePath(String absolutePath) {
        if (StringUtils.isBlank(absolutePath)) {
            return null;
        }
        String[] segments = absolutePath.split(File.separator);
        return File.separator + segments[segments.length - 1];
    }

    public abstract boolean addNewFile(FundFileProcessRecPo recPo, String lastWorkDay) throws Exception;

    /**
     * ok文件写入
     */
    public void writeOkFile(String value, String tradeDt) throws Exception {
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setFileName(getOkFileName(tradeDt));
        fileSdkPathInfo.setMiddlePath(getPath(tradeDt));
        fileSdkPathInfo.setBusinessCode(getBusinessCode());
        HTextFileWriter hFileWriter = FileSdkUtil.buildHTextFileWriter(fileSdkPathInfo);
        try {

            hFileWriter.write(value);
        } finally {
            hFileWriter.flush();
            hFileWriter.close();
        }
    }

    /**
     * @param fileds 文件内容字段
     * @param list   数据列表
     * @return java.util.List<java.lang.String>
     * @description: 文件内容组装
     * @author: hongdong.xie
     * @date: 2020/1/14 18:45
     * @since JDK 1.7
     */
    public List<String> convertContent(String fileds, List<?> list) throws Exception {
        List<String> fileContentList = new ArrayList<String>();
        if (CollectionUtils.isEmpty(list)) {
            return fileContentList;
        }
        String[] fields = fileds.split(",");
        for (Object obj : list) {
            StringBuilder fileContents = new StringBuilder();
            Map<String, Object> map = BeanConvertUtil.objectToMap(obj);
            int i = 0;
            int length = fields.length;
            for (String field : fields) {
                String mapValue = convertMapValue(map.get(field.trim()));
                fileContents.append(mapValue);
                if (i < length - 1) {
                    fileContents.append(SEPARATOR);
                }
                i++;
            }
            fileContentList.add(fileContents.toString());
        }

        return fileContentList;
    }

    public static ExecutorService createFixedThreadPool(String threadName, int poolSize) {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat(threadName).build();
        return new ThreadPoolExecutor(poolSize, poolSize,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(POOL_CAPACITY), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
    }

    /**
     * 直销文件是否处理
     */
    public boolean crmFileHasProcess(String tradeDt, String fileType) {
        // 海外文件是否处理成功
        List<String> hkFileTypeList = new ArrayList<>();
        hkFileTypeList.add(FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode());
        hkFileTypeList.add(FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode());
        hkFileTypeList.add(FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode());
        if (hkFileTypeList.contains(fileType) && !crmHkHasProcess(tradeDt)) {
            return false;
        }
        // 非海外文件是否处理成功
        List<String> unHkFileTypeList = new ArrayList<>();
        unHkFileTypeList.add(FileTypeEnum.EX_HIGH_FUND_ACK_FILE_TYPE.getCode());
        unHkFileTypeList.add(FileTypeEnum.EX_HIGH_VOL_CHECK_FILE_TYPE.getCode());
        unHkFileTypeList.add(FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode());
        unHkFileTypeList.add(FileTypeEnum.EX_HIGH_FUND_CM_YJYZ_FILE_TYPE.getCode());
        unHkFileTypeList.add(FileTypeEnum.EX_HIGH_FUND_CM_BLACKLIST_FILE_TYPE.getCode());
        if (unHkFileTypeList.contains(fileType) && !crmUnHkHasProcess(tradeDt)) {
            return false;
        }
        return true;
    }

    /**
     * 非海外文件是否处理
     */
    public boolean crmUnHkHasProcess(String tradeDt) {
        logger.info("校验非海外直销文件是否处理,tradeDt={}", tradeDt);
        // 处理直销文件
        BigDecimal latestVersion = cmFileProcessRecRepository.getLatestVersionEveryDayUnHk();
        CmFileProcessRecPo cmFileProcessRecPo = cmFileProcessRecRepository.selectByVersionNoUnHk(latestVersion);
        if (cmFileProcessRecPo == null) {
            logger.warn("校验非海外直销文件是否处理-cmFileProcessRecPo is null , latestVersion:{}", latestVersion);
            return false;
        }

        if (!ObjectUtils.nullSafeEquals(tradeDt, cmFileProcessRecPo.getDealDt())) {
            logger.warn("校验非海外直销文件是否处理-cmFileProcess tradeDt not  eq dealDt tradeDt:{} , latestVersion:{}, dealDt:{}", tradeDt, latestVersion, cmFileProcessRecPo.getDealDt());
            return false;
        }

        if (!ProcessStatusEnum.PROCESS_SUCCESS.getCode().equals(cmFileProcessRecPo.getProcessStatus())) {
            logger.warn("校验非海外直销文件是否处理-cmFileProcess tradeDt:{} , latestVersion:{} not process succ ProcessStatus:{}", tradeDt, latestVersion, cmFileProcessRecPo.getProcessStatus());
            return false;
        }
        return true;
    }

    /**
     * 海外文件是否处理
     */
    public boolean crmHkHasProcess(String tradeDt) {
        logger.info("校验海外直销文件是否处理,tradeDt={}", tradeDt);
        if (StringUtils.isNotBlank(commonConfService.getExportAssertWaitHk()) && YesOrNoEnum.NO.getCode().equals(commonConfService.getExportAssertWaitHk())) {
            logger.info("给资产中心直销文件不需要等待海外文件,exportAssertWaitHk={}", commonConfService.getExportAssertWaitHk());
            return true;
        }
        // 处理直销文件
        BigDecimal latestVersion = cmFileProcessRecRepository.getLatestVersionEveryDayHk();
        CmFileProcessRecPo cmFileProcessRecPo = cmFileProcessRecRepository.selectByVersionNoHk(latestVersion);
        if (cmFileProcessRecPo == null) {
            logger.warn("校验海外直销文件是否处理-cmFileProcessRecPo is null , latestVersion:{}", latestVersion);
            return false;
        }

        if (!ObjectUtils.nullSafeEquals(tradeDt, cmFileProcessRecPo.getDealDt())) {
            logger.warn("校验海外直销文件是否处理-cmFileProcess tradeDt not  eq dealDt tradeDt:{} , latestVersion:{}, dealDt:{}", tradeDt, latestVersion, cmFileProcessRecPo.getDealDt());
            return false;
        }

        if (!ProcessStatusEnum.PROCESS_SUCCESS.getCode().equals(cmFileProcessRecPo.getProcessStatus())) {
            logger.warn("校验海外直销文件是否处理-cmFileProcess tradeDt:{} , latestVersion:{} not process succ ProcessStatus:{}", tradeDt, latestVersion, cmFileProcessRecPo.getProcessStatus());
            return false;
        }
        return true;
    }

    /**
     * @param mapValue
     * @return java.lang.String
     * @description: 数据转换
     * @author: hongdong.xie
     * @date: 2020/1/7 17:40
     * @since JDK 1.7
     */
    private String convertMapValue(Object mapValue) {
        if (org.springframework.util.StringUtils.isEmpty(mapValue)) {
            return "";
        }
        return mapValue.toString();
    }


    /**
     * @param tradeDt  交易日
     * @param taCode   TA代码
     * @param fileType 文件类型
     * @param fileName 文件名称
     * @return void
     * @description: 创建文件处理记录
     * @author: hongdong.xie
     * @date: 2020/1/15 14:37
     * @since JDK 1.7
     */
    protected FundFileProcessRecPo addFundFileProcessRec(String tradeDt, String taCode, String fileType, String fileName) {
        FundFileProcessRecPo po = new FundFileProcessRecPo();
        po.setTaCode(taCode);
        po.setTaTradeDt(tradeDt);
        po.setFileType(fileType);
        po.setFileName(fileName);
        po.setFileOption(FileOptionEnum.CREATE.getCode());
        po.setFileOpStatus(FileOpStatusEnum.Not.getKey());
        po.setProcessStatus(ProcessStatusEnum.NOT_PROCESS.getCode());
        Date now = new Date();
        po.setCreateDtm(now);
        po.setUpdateDtm(now);
        po.setOperator("system");

        fundFileProcessRecRepository.insert(po);

        return po;
    }


    /**
     * @param tradeDt  日期
     * @param taCode   TA代码
     * @param fileName 文件名称
     * @param fileType 文件类型
     * @return void
     * @description: 文件生成消息发送
     * @author: hongdong.xie
     * @date: 2020/1/8 14:25
     * @since JDK 1.7
     */
    protected boolean sendMessage(String tradeDt, String taCode, String fileName, String fileType) {
        try {
            ExportFileMessageBean vo = new ExportFileMessageBean();
            vo.setTradeDt(tradeDt);
            vo.setFileType(fileType);
            vo.setFileName(fileName);
            if (Constant.ALL_TA_CODE.equals(taCode)) {
                vo.setTaCode(ALL_TA_CODE);
            } else {
                vo.setTaCode(taCode);
            }

            //具体消息对象
            SimpleMessage message = new SimpleMessage();
            message.setContent(JSON.toJSONString(vo));

            MessageService.getInstance().send(tmsFundFileQueue, message);
            logger.info("message send success.");
        } catch (Exception e) {
            logger.error("message send err {}", e);
            return false;
        }

        return true;
    }

    public abstract String getCheckFileName(String tradeDt, String taCode, String latestVersion);

    /**
     * 文件SDK key
     *
     * @return SDK key
     */
    public abstract String getBusinessCode();

    /**
     * 获取中间路径
     *
     * @param tradeDt 交易日
     * @return 中间路径
     */
    public abstract String getPath(String tradeDt);

    /**
     * ok文件名
     *
     * @param tradeDt 交易日
     * @return ok文件名
     */
    public abstract String getOkFileName(String tradeDt);

    /**
     * 创建文件
     *
     * @param createAssetTxtFileParam 入参
     * @return 创建是否成功
     */
    public boolean createTXTFile(CreateAssetTxtFileParam createAssetTxtFileParam) {
        logger.info("AbstractGenerateFileService-createTXTFile,给资产中心文件写入-start,createAssetTxtFileParam={}", JSON.toJSONString(createAssetTxtFileParam));
        HTextFileWriter writer = null;
        try {
            FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
            fileSdkPathInfo.setFileName(createAssetTxtFileParam.getFileName());
            fileSdkPathInfo.setMiddlePath(createAssetTxtFileParam.getFilePath());
            fileSdkPathInfo.setBusinessCode(createAssetTxtFileParam.getBusinessCode());
            writer = FileSdkUtil.buildHTextFileWriter(fileSdkPathInfo);
            String heards = getHeader(createAssetTxtFileParam.getTotalCount(), createAssetTxtFileParam.getFileFieldsName());
            //文件头 标题之类的
            if (StringUtils.isNotEmpty(heards)) {
                String[] headStrs = heards.split(",");
                for (String heard : headStrs) {
                    writer.write(heard + FileUtils.CR + FileUtils.LF);
                    writer.flush();
                }
            }

            int pageSize = 5000;
            int pageNo = 1;
            long totalSize = 0;
            long alreadyProcessSize = -1;
            //文件内容
            while (alreadyProcessSize < totalSize) {
                Page<?> pageResult = getData(createAssetTxtFileParam.getTradeDt(), createAssetTxtFileParam.getTaCode(), pageNo, pageSize);
                totalSize = (totalSize == 0) ? pageResult.getTotal() : totalSize;
                pageNo++;
                List<?> orderList = pageResult.getResult();
                int singleSize = orderList.size();
                alreadyProcessSize = (alreadyProcessSize < 0) ? singleSize : (alreadyProcessSize + singleSize);
                List<String> contentList = convertContent(createAssetTxtFileParam.getFileFields(), orderList);
                //文件内容
                for (String content : contentList) {
                    writer.write(content + FileUtils.CR + FileUtils.LF);
                    writer.flush();
                }
            }
        } catch (Exception e) {
            logger.error("AbstractGenerateFileService>>>createTXTFile>>is error", e);
            return false;
        } finally {
            try {
                if (writer != null) {
                    writer.flush();
                    writer.close();
                }
            } catch (Exception e) {
                logger.error("AbstractGenerateFileService>>>createTXTFile>>is error", e);
            }
        }
        return true;
    }

    /**
     * @param totalCount     总记录数
     * @param fileFiledsName 文件字段名称
     * @return java.lang.StringBuffer
     * @description: 获取文件头信息，如果文件头有多行，则用","隔开
     * @author: hongdong.xie
     * @date: 2020/1/8 11:25
     * @since JDK 1.7
     */
    protected String getHeader(long totalCount, String fileFiledsName) {

        return totalCount + "," +
                fileFiledsName;
    }

    /**
     * @param tradeDt  交易日
     * @param taCode   TA代码
     * @param pageNo   页码
     * @param pageSize 每页大小
     * @return com.github.pagehelper.Page<?>
     * @description: 获取文件制定页码的数据集合
     * @author: hongdong.xie
     * @date: 2020/3/14 16:52
     * @since JDK 1.7
     */
    protected abstract Page<?> getData(String tradeDt, String taCode, int pageNo, int pageSize);


}
