///**
// *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
// *All right reserved.
// *
// *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
// *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
// *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
// *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
// * CO., LTD.
// */
//
//package com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend;
//
//import com.howbuy.tms.common.constant.ExceptionCodes;
//import com.howbuy.tms.common.exception.BusinessException;
//import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
//import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
//import com.howbuy.tms.high.batch.service.business.ordersubmit.BaseResponse;
//import com.howbuy.tms.high.batch.service.business.ordersubmit.CheckOrderSubmitService;
//import com.howbuy.tms.high.batch.service.common.MessageSource;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.springframework.stereotype.Service;
//
///**
// * @description:赎回接口（联机）
// * @reason: 已废弃  20200420
// * <AUTHOR>
// * @date 2016年9月14日 下午1:59:49
// * @since JDK 1.6
// */
//@Service("redeemToCXGSubmitService")
//public class RedeemToCXGSubmitService extends CheckOrderSubmitService {
//
//    private static Logger logger = LogManager.getLogger(RedeemToCXGSubmitService.class);
////
////    @Autowired
////    private RedeemOuterService redeemOuterService;
//
//    @Override
//    public boolean userThis(String businessCode) {
//        return false;
//    }
//
//    @Override
//    protected BaseResponse doSubmit(SimuFundCheckOrderManualSubmitVo fundCheckOrderPo) {
//
//        BaseResponse baseResponse = new BaseResponse();
//        try {
////            RedeemContext context = generateContext(fundCheckOrderPo);
////            RedeemResult result = redeemOuterService.doRedeem(context);
////
////            baseResponse.setReturnCode(result.getReturnCode());
////            baseResponse.setDescription(result.getDescription());
////            baseResponse.setDealNo(result.getDealNo());
////            baseResponse.setTaTradeDt(result.getTaTradeDt());
//        } catch (Throwable e) {
//            logger.error("RedeemSubmitService|doSubmit|call outer service error.", e);
//            throw new BusinessException(ExceptionCodes.BATCH_CENTER_ACCESS_DUBBO_INTERFACE_FAILED,
//                    MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_ACCESS_DUBBO_INTERFACE_FAILED));
//        }
//
//        return baseResponse;
//    }
//
//    @Override
//    protected void submitSuccess(SimuFundCheckOrderPo fundCheckOrderPo) {
//    }
//
//    @Override
//    protected void submitFail(SimuFundCheckOrderPo fundCheckOrderPo) {
//    }
//
////    private RedeemContext generateContext(SimuFundCheckOrderPo fundCheckOrderPo) {
////        RedeemContext context = new RedeemContext();
////
////        // 交易渠道
////        context.setTradeChannel(fundCheckOrderPo.getTxChannel());
////        // 地区代码
////        // 网点代码
////        context.setOutletCode(fundCheckOrderPo.getOutletCode());
////        // 上报Ta日 TO 申请日期
////        context.setAppDt(fundCheckOrderPo.getSubmitTaDt());
////        // 12点 TO 申请时间
////        context.setAppTm("120000");
////        // 备注
////        context.setMemo(fundCheckOrderPo.getMemo());
////        // 交易IP
////        context.setIpAddress(fundCheckOrderPo.getIpAddress());
////        // 分销机构代码
////        context.setDisCode(fundCheckOrderPo.getDisCode());
////        // 客户号
////        context.setTxAcctNo(fundCheckOrderPo.getTxAcctNo());
////        // 申请时间
////        context.setAppDtm(fundCheckOrderPo.getCreateDtm());
////        // 赎回方式
////        context.setLargeRedmFlag(fundCheckOrderPo.getLargeRedmFlag());
////        // 客户类型
////        context.setInvstType(fundCheckOrderPo.getInvstType());
////        // 客户姓名
////        context.setCustName(fundCheckOrderPo.getCustName());
////        // 证件类型
////        context.setIdType(fundCheckOrderPo.getIdType());
////        // 证件号码
////        context.setIdNo(fundCheckOrderPo.getIdNo());
////        context.setNoPwd(true);
////        context.setIsRedeemPigg("1");
////
////        // 设置100%赎回时, 后台联机将忽略"赎回低于最低持有下线"错误码
////        BigDecimal appRatio = fundCheckOrderPo.getAppRatio();
////        boolean ignoreMinHold = false;
////        if (appRatio != null && appRatio.doubleValue() == 1) {
////            ignoreMinHold = true;
////        }
////        context.setIgnoreMinHold(ignoreMinHold);
////
////        context.setFundCode(fundCheckOrderPo.getFundCode());
////        context.setShareClass(fundCheckOrderPo.getFundShareClass());
////        context.setAppVol(fundCheckOrderPo.getAppVol());
////        context.setCpAcctNo(fundCheckOrderPo.getCpAcctNo());
////        context.setSubmitDealNo(fundCheckOrderPo.getSubmitDealNo());
////        // 经办人姓名
////        context.setTransactorName(fundCheckOrderPo.getTransactorName());
////        // 经办人证件号码
////        context.setTransactorIdNo(fundCheckOrderPo.getTransactorIdNo());
////        // 经办人证件类型
////        context.setTransactorIdType(fundCheckOrderPo.getTransactorIdType());
////        context.setProtocolNo(fundCheckOrderPo.getProtocolNo());
////        context.setUnusualTransType(fundCheckOrderPo.getUnusualTransType());
////        return context;
////    }
//}
