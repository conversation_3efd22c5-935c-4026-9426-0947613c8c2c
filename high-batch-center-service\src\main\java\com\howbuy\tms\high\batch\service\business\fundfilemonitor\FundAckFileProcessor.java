/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.fundfilemonitor;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.FileOptionEnum;
import com.howbuy.tms.common.enums.database.FileOptionStatus;
import com.howbuy.tms.common.enums.database.FileTypeEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.QueryTaInfoOuterService;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessDtlRecPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.common.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.FileSdkUtil;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.repository.FundFileProcessDtlRecRepository;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.FundAckFileImportService;
import com.howbuy.tms.high.batch.service.service.sequence.SequenceService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description:(高端公募确认文件消息处理)
 * @reason:TODO ADD REASON(可选)
 * @date 2016年9月19日 下午10:55:58
 * @since JDK 1.6
 */
@Service("fundAckFileProcessor")
public class FundAckFileProcessor implements FundFileProcessor {

    private static Logger logger = LogManager.getLogger(FundAckFileProcessor.class);

    @Autowired
    private FundFileProcessDtlRecRepository fundFileProcessDtlRecRepository;

    @Autowired
    private QueryTaInfoOuterService queryTaInfoOuterService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private FundAckFileImportService fundAckFileImportService;

    @Autowired
    private SequenceService sequenceService;

    @Override
    public void process(FundFileMessage fundFileMessage) throws Exception {
        logger.info("FundAckFileProcessor-开始处理交易记录确认文件消息, fundFileMessage:{}", JSON.toJSONString(fundFileMessage));
        String taCode = fundFileMessage.getTaCode();
        String fundCode = fundFileMessage.getFundCode();
        String taTradeDt = fundFileMessage.getExportDt();
        String fileType = FileTypeEnum.H_TRADE_ACK.getCode();
        // 查询所高端(专户/私募)TA
        Set<String> taCodeList = queryTaInfoOuterService.getAllHighTaCode();
        // 判断当天TA文件是否是需要处理，如果是，保存文件消息；否则忽略该消息
        if (taCodeList.contains(taCode)) {
            // 查询该TA下所有高端产品
            List<String> productList = new ArrayList<>();
            List<HighProductBaseInfoModel> beanList = queryHighProductOuterService.getHighProductBaseInfoByTaCode(taCode);
            if (CollectionUtils.isNotEmpty(beanList)) {
                for (HighProductBaseInfoModel model : beanList) {
                    productList.add(model.getFundCode());
                }
            }
            logger.info("FundAckFileProcessor-查询产品结果,taCode:{},productList:{}", taCode, JSON.toJSONString(productList));
            FundFileProcessDtlRecPo file = fundFileProcessDtlRecRepository.selectFundFileProcessDtlRec(taTradeDt, fileType, taCode, fundCode);
            if (file != null) {
                if (FileOptionStatus.PROCESSING.getCode().equals(file.getFileOpStatus())) {
                    logger.info("FundAckFileProcessor.FundFileProcessDtlRecPo is processing|file:{}", JSON.toJSONString(file));
                    throw new BatchException(ExceptionCodes.HIGH_BATCH_CENETER_FUND_VOL_IMPORT_ERROR, "文件正在处理中,不能导入");
                }
                logger.info("FundAckFileProcessor-查询到文件明细,但是不是处理中,file:{}", JSON.toJSONString(file));
            }
            // 保存文件消息
            FundFileProcessDtlRecPo po = new FundFileProcessDtlRecPo();
            po.setTaTradeDt(taTradeDt);
            po.setFileType(fileType);
            po.setTaCode(taCode);
            po.setFundCode(fundCode);
            po.setFileOption(FileOptionEnum.IMPORT.getCode());
            // 直接默认处理中
            po.setFileOpStatus(FileOpStatusEnum.PROCESSING.getKey());
            po.setOperator(Constant.OPERATOR_SYS);
            FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
            fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.ASSET_ACK);
            fileSdkPathInfo.setMiddlePath( fundFileMessage.getExportDt() + File.separator + taCode + File.separator);
            fileSdkPathInfo.setFileName(fundFileMessage.getFileName());
            po.setFileName(FileSdkUtil.getAbsolutePath(fileSdkPathInfo));
            Date date = new Date();
            po.setCreateDtm(date);
            po.setUpdateDtm(date);
            po.setRecordNo(sequenceService.getRecordNo(CacheKeyPrefix.HIGH_FILE_RECORDNO_PREFIX,null));
            fundFileProcessDtlRecRepository.deleteByUniqueKey(po);
            fundFileProcessDtlRecRepository.insert(po);
            FundFileProcessDtlRecPo updatePo = null;
            try {
                if (productList.contains(fundCode)) {
                    // 解析并保存文件内容到全量库
                    logger.info("开始导入高端公募确认文件-fileSdkPathInfo:{},taCode:{}", JSON.toJSONString(fileSdkPathInfo), taCode);
                    FileImportContext context = new FileImportContext();
                    context.setFileName(fundFileMessage.getFileName());
                    context.setRelationPath(fundFileMessage.getExportDt() + File.separator + taCode + File.separator);
                    context.setTaTradeDt(taTradeDt);
                    context.getParams().put("taCode", taCode);
                    context.getParams().put("fundCode", fundCode);
                    context.getParams().put("taTradeDt", taTradeDt);
                    context.getParams().put("sysCode", SysCodeEnum.BATCH_HIGH.getCode());
                    fundAckFileImportService.process(context);
                } else {
                    logger.info("FundAckFileProcessor-process-fundCode:{} is not highproduct", fundCode);
                }
                // 更新文件处理状态
                updatePo = new FundFileProcessDtlRecPo();
                updatePo.setRecordNo(po.getRecordNo());
                // 生成成功
                updatePo.setFileOpStatus(Constant.FILE_OP_STATUS_GEN_SUCC);
                updatePo.setUpdateDtm(date);
                fundFileProcessDtlRecRepository.updateByRecordNo(updatePo);
            } catch (Exception e) {
                updatePo = new FundFileProcessDtlRecPo();
                // 生成失败
                updatePo.setFileOpStatus(Constant.FILE_OP_STATUS_GEN_FAIL);
                if (e instanceof BusinessException) {
                    updatePo.setMemo(MessageSource.getMessageByCode(((BusinessException) e).getErrorCode()));
                } else {
                    updatePo.setMemo("文件处理发生异常");
                }
                updatePo.setRecordNo(po.getRecordNo());
                updatePo.setUpdateDtm(date);
                fundFileProcessDtlRecRepository.updateByRecordNo(updatePo);
                logger.error("FundAckFileProcessor-文件处理发生异常,e:", e);
            }
        }

    }


}
