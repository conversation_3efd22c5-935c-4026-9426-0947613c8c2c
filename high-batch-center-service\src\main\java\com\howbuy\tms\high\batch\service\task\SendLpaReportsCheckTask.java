package com.howbuy.tms.high.batch.service.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.outerservice.ccmessage.queryemailsendstatus.QueryEmailSendStatusOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.queryemailsendstatus.bean.QueryEmailSendStatusResultBean;
import com.howbuy.tms.high.batch.service.business.autosendnotifyemail.SendLPAReportsService;
import com.howbuy.tms.high.batch.service.common.HowbuyBaseTask;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:日终发送报表任务
 * @Author: yun.lu
 * Date: 2024/7/9 14:38
 */
public class SendLpaReportsCheckTask extends HowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(SendLpaReportsCheckTask.class);
    private QueryEmailSendStatusOuterService queryEmailSendStatusOuterService;
    private SendLPAReportsService sendLpaReportsService;
    /**
     * 收件人
     */
    private String emails;
    /**
     * 邮件唯一标识
     */
    private String traceId;
    /**
     * 尝试次数
     */
    private int tryNumber;
    /**
     * 交易日期
     */
    private String tradeDt;

    public SendLpaReportsCheckTask() {
    }

    @Override
    protected void callTask() {
        // 1.先等3分钟
        sleep();
        try {
            // 2.查询邮件发送状态
            QueryEmailSendStatusResultBean resultBean = queryEmailSendStatusOuterService.execute(traceId, emails);
            List<String> sendingList = new ArrayList<>(2);
            sendingList.add("1");
            sendingList.add("2");
            List<String> failList = new ArrayList<>(2);
            failList.add("5");
            failList.add("6");
            if (sendingList.contains(resultBean.getSendStatus())) {
                logger.info("日终报表发送中,resultBean={}", JSON.toJSON(resultBean));
                if (tryNumber > 3) {
                    String msg = "高端日终结束发送邮件报表未成功,当前TA日:" + tradeDt + ",traceId:" + traceId + ",失败原因:" + resultBean.getMemo();
                    OpsMonitor.warn(msg, OpsMonitor.ERROR);
                    logger.error("{}", msg);
                    return;
                }
                // 发送中,就等3分钟再查询
                sendLpaReportsService.checkEmailResult(traceId, emails, tradeDt, ++tryNumber);
            } else if (failList.contains(resultBean.getSendStatus()) || StringUtils.isBlank(resultBean.getSendStatus())) {
                logger.info("日终报表发送失败,resultBean={}", JSON.toJSON(resultBean));
                if (tryNumber > 3) {
                    String msg = "高端日终结束发送邮件报表未成功,当前TA日:" + tradeDt + ",traceId:" + traceId + ",失败原因:" + resultBean.getMemo();
                    OpsMonitor.warn(msg, OpsMonitor.ERROR);
                    logger.error("{}", msg);
                    return;
                }
                // 发送失败,就重试发送
                sendLpaReportsService.sendReportsEmail(tradeDt, ++tryNumber);
            } else {
                logger.info("日终报表发送成功,resultBean={},tradeDt={}", JSON.toJSON(resultBean), tradeDt);
            }
        } catch (Exception e) {
            String msg = "高端日终结束发送邮件报表校验结果异常,当前TA日:" + tradeDt + ",traceId:" + traceId;
            OpsMonitor.warn(msg, OpsMonitor.ERROR);
            logger.error("{}", msg);
        }
    }

    private void sleep() {
        try {
            Thread.sleep(60 * 1000);
        } catch (InterruptedException e) {
            logger.error("等待异常,e:", e);
        }
    }

    public QueryEmailSendStatusOuterService getQueryEmailSendStatusOuterService() {
        return queryEmailSendStatusOuterService;
    }

    public void setQueryEmailSendStatusOuterService(QueryEmailSendStatusOuterService queryEmailSendStatusOuterService) {
        this.queryEmailSendStatusOuterService = queryEmailSendStatusOuterService;
    }

    public SendLPAReportsService getSendLpaReportsService() {
        return sendLpaReportsService;
    }

    public void setSendLpaReportsService(SendLPAReportsService sendLpaReportsService) {
        this.sendLpaReportsService = sendLpaReportsService;
    }

    public String getEmails() {
        return emails;
    }

    public void setEmails(String emails) {
        this.emails = emails;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public int getTryNumber() {
        return tryNumber;
    }

    public void setTryNumber(int tryNumber) {
        this.tryNumber = tryNumber;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }
}
