package com.howbuy.tms.high.batch.service.business.custesignprocess;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.busi.HighProductAgreementTemplateEnum;
import com.howbuy.tms.common.enums.busi.IdTypeIndiAndInstEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.cms.queryaddress.QueryAddressService;
import com.howbuy.tms.common.outerservice.es.digestsign.coor.DigestSignCoorOuterContext;
import com.howbuy.tms.common.outerservice.es.digestsign.coor.DigestSignCoorOuterService;
import com.howbuy.tms.high.batch.dao.po.order.CustEsignaturePo;
import com.howbuy.tms.high.batch.dao.po.order.DealOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.service.business.custesignprocess.bean.CustomPdfTextStripper;
import com.howbuy.tms.high.batch.service.business.custesignprocess.bean.PartnershipInfo;
import com.howbuy.tms.high.batch.service.common.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.FileSdkUtil;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.pdf.bean.PdfFileExportContext;
import com.howbuy.tms.high.batch.service.service.file.fileexport.pdf.impl.GeneratePartnershipAgreementService;
import com.itextpdf.text.Element;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:合伙人协议发起签名
 * @Author: yun.lu
 * Date: 2024/6/3 13:26
 */
@Service
public class PartnershipAgreementSignService extends DigestSignAbstractService {
    @Autowired
    private DigestSignCoorOuterService digestSignCoorOuterService;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private QueryAddressService queryAddressService;
    @Autowired
    private GeneratePartnershipAgreementService generatePartnershipAgreementService;
    /**
     * 签名文件生成路径
     */
    @Value("${path.esign.out.path}")
    private String signFileOutPath;

    /**
     * 风险揭示书签名文件生成路径
     */
    @Value("${path.risk.esign.out.path}")
    private String riskSignFileOutPath;


    /**
     * 楷体字体
     */
    @Value("${file.font.kaiti}")
    private String fontKaiTi;


    public static String UN_SIGN_PARTNER_SHIP_AGREEMENT = "partner_ship_withoutSign.pdf";

    @Override
    public void doSign(DigestSignContext digestSignContext) {
        try {
            DigestSignCoorOuterContext digestSignKeyOuterContext = generateContext(digestSignContext);
            digestSignCoorOuterService.DigestSignWithTemplate(digestSignKeyOuterContext);
        } catch (Exception e) {
            logger.error("合伙人协议生成并发起签名异常e:", e);
        }
    }

    private DigestSignCoorOuterContext generateContext(DigestSignContext custEsignatureContext) throws Exception {
        DigestSignCoorOuterContext context = new DigestSignCoorOuterContext();
        context.setIdNo(custEsignatureContext.getCustInfoResult().getIdNo());
        context.setIdType(custEsignatureContext.getCustInfoResult().getIdType());
        context.setCustName(custEsignatureContext.getCustInfoResult().getCustName());
        context.setChannle(custEsignatureContext.getChannel());
        String dstPdf = buildFileName(buildFilePath(getSignOutPath(custEsignatureContext.getHighProductAgreementTemplateEnum()), custEsignatureContext), buildEsignOutFileName(custEsignatureContext));
        context.setDstPdfPath(dstPdf);
        context.setSerialNo(buildSeriaNo(custEsignatureContext));
        context.setTempId(buildTemplateFileName(custEsignatureContext));
        if (custEsignatureContext.getCustEsignature() != null) {
            context.setSealId(custEsignatureContext.getCustEsignature().getSealId());
        }
        String srcPdfPath = generateNewFile(custEsignatureContext);

        logger.info("合伙人协议生成的源路径,srcPdfPath={}", srcPdfPath);

        String path = buildFileName(buildFilePath(signFileOutPath, custEsignatureContext), buildEsignFileName(custEsignatureContext.getCustEsignature(), UN_SIGN_PARTNER_SHIP_AGREEMENT));
        context.setSrcPdfPath(path);
        context.setSrcPdfPathUsed(YesOrNoEnum.YES.getCode());
        return context;
    }

    private String getSignOutPath(HighProductAgreementTemplateEnum agreementTemplateEnum) {
        if (agreementTemplateEnum != null && HighProductAgreementTemplateEnum.RISK_NOTICE.getCode().equals(agreementTemplateEnum.getCode())) {
            return riskSignFileOutPath;
        }
        return signFileOutPath;
    }

    public String generateNewFile(DigestSignContext digestSignContext) {
        try {
            QueryCustInfoResult custInfoResult = digestSignContext.getCustInfoResult();
            DealOrderPo dealOrderPo = digestSignContext.getDealOrderPo();
            PdfFileExportContext pdfFileExportContext = new PdfFileExportContext();
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("dealNo", dealOrderPo.getDealNo());
            paramMap.put("custInfoResult", custInfoResult);
            paramMap.put("digestSignContext", digestSignContext);
            pdfFileExportContext.setParams(paramMap);
            return generatePartnershipAgreementService.process(pdfFileExportContext);
        } catch (Exception e) {
            logger.info("生成合伙人协议异常:", e);
        }
        return null;
    }

    /**
     * 表格设置
     */
    public boolean agreementBuildAndCreate(FileSdkPathInfo src, FileSdkPathInfo dest, PartnershipInfo partnershipInfo) {
        logger.info("合伙人协议生成开始,src={},partnershipInfo={}", src, JSON.toJSONString(partnershipInfo));
        PdfStamper stamper = null;
        PdfReader reader = null;
        try {
            PDDocument document = PDDocument.load(FileSdkUtil.buildHInputStream(src).getInputStream());
            CustomPdfTextStripper stripper = new CustomPdfTextStripper();
            stripper.setStartPage(document.getNumberOfPages() - 1);
            stripper.setEndPage(document.getNumberOfPages() - 1);
            stripper.getText(document);
            // 1.签署日期
            // 获取签署日期的y坐标
            float lastY = stripper.getLastY();
            reader = new PdfReader(FileSdkUtil.buildHInputStream(src).getInputStream());
            stamper = new PdfStamper(reader, FileSdkUtil.buildHOutputStream(dest).getOutputStream());
            // 倒数第二页
            int lastPageNumber = reader.getNumberOfPages() - 1;

            PdfContentByte content = stamper.getOverContent(lastPageNumber);
            Rectangle rectangle = reader.getPageSizeWithRotation(lastPageNumber);
            // 设置表格字体
            BaseFont font = BaseFont.createFont("templates/front/KaiTi.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            content.beginText();
            content.setFontAndSize(font, 11);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
            String currentDay = simpleDateFormat.format(new Date());
            content.showTextAligned(Element.ALIGN_LEFT, currentDay, rectangle.getLeft() + 150, rectangle.getTop() - lastY, 0);
            // 2.表格内容设置
            // 倒数第一页
            lastPageNumber = reader.getNumberOfPages();
            content = stamper.getOverContent(lastPageNumber);
            rectangle = reader.getPageSizeWithRotation(lastPageNumber);
            // 设置表格字体
            content.beginText();
            content.setFontAndSize(font, 9);
            setTableValue(content, rectangle, font, partnershipInfo);
            content.endText();
            logger.info("合伙人协议生成成功,dest={}", dest);
            return true;
        } catch (Exception e) {
            logger.info("合伙人协议生成出现异常,e:", e);
        } finally {
            try {
                if (stamper != null) {
                    stamper.close();
                }
                if (reader != null) {
                    reader.close();
                }
            } catch (Exception e) {
                logger.info("合伙人协议生成关闭流文件异常,e:", e);
            }
        }
        return false;
    }


    /**
     * 表格内容输入
     */
    private void setTableValue(PdfContentByte content, Rectangle rectangle, BaseFont font, PartnershipInfo partnershipInfo) {
        // 第3行
        int startY = 215;
        // 第3行第1列
        int startX = 80;
        int contentPart = 8;
        addValue(content, font, rectangle, partnershipInfo.getCustomerName(), startY, startX, contentPart);
        // 第3行第3列
        int startX3 = 235;
        int contentPart3 = 15;
        startY = 212;
        addValue(content, font, rectangle, partnershipInfo.getCustomerIdName(), startY, startX3, contentPart3);

        // 第3行第3列
        int startX32 = 235;
        int contentPart32 = 20;
        startY = 225;
        addValue(content, font, rectangle, partnershipInfo.getCustomerIdNo(), startY, startX32, contentPart32);
        startY = 215;

        // 第3行第4列
        int startX4 = 370;
        int contentPart4 = 15;
        addValue(content, font, rectangle, partnershipInfo.getCustomerSubsAmt(), startY, startX4, contentPart4);

        // 第3行第5列
        int startX5 = 425;
        int contentPart5 = 8;
        addValue(content, font, rectangle, partnershipInfo.getCustomerAddress(), startY, startX5, contentPart5);

    }

    /**
     * 写入内容,固定坐标
     */
    private void addValue(PdfContentByte content, BaseFont font, Rectangle rectangle, String contentStr, int startY, int startX, int contentPart) {
        int partHigh = 13;
        if (contentStr.length() > 35) {
            content.setFontAndSize(font, 6);
            contentPart = 13;
            partHigh = 8;
        } else if (contentStr.length() > 24) {
            content.setFontAndSize(font, 8);
            contentPart = 9;
            partHigh = 10;
        } else if (contentStr.length() < 5) {
            content.setFontAndSize(font, 10);
        } else {
            startY += 3;
            content.setFontAndSize(font, 9);
        }
        if (contentStr.length() > contentPart) {
            for (int i = 0; i < contentStr.length(); i += contentPart) {
                int endIndex = Math.min(i + contentPart, contentStr.length());
                String segment = contentStr.substring(i, endIndex);
                content.showTextAligned(Element.ALIGN_LEFT, segment, rectangle.getLeft() + startX, rectangle.getTop() - startY, 0);
                startY += partHigh;
            }
        } else {
            content.showTextAligned(Element.ALIGN_LEFT, contentStr, rectangle.getLeft() + startX, rectangle.getTop() - (startY + 15), 0);
        }


    }

    /**
     * 产品合同模版
     */
    private String getFundTemplateName(DigestSignContext custEsignatureContext) {
        return custEsignatureContext.getHighProductAgreementTemplateEnum().getTemplateIdPrefix() + UNDERLINE_SPLIT + custEsignatureContext.getCustEsignature().getProductCode() + ".pdf";
    }

    /**
     * @param dealNo
     * @param custInfoResult
     * @return
     */
    public PartnershipInfo buildPartnershipInfo(String dealNo, QueryCustInfoResult custInfoResult) {

        if (custInfoResult == null) {
            logger.error("合伙人协议,查不到用户信息,dealNo={}", dealNo);
            return null;
        }
        PartnershipInfo partnershipInfo = new PartnershipInfo();
        partnershipInfo.setCustomerName(custInfoResult.getCustName());
        String idTypeIndiAndInstCode = custInfoResult.getInvstType() + custInfoResult.getIdType();
        partnershipInfo.setCustomerIdName(IdTypeIndiAndInstEnum.getValue(idTypeIndiAndInstCode));
        partnershipInfo.setCustomerIdNo(custInfoResult.getIdNo());
        // 查询认缴金额
        List<HighDealOrderDtlPo> dealOrderDtlPoList = highDealOrderDtlRepository.getByDealNo(dealNo);
        if (dealOrderDtlPoList == null) {
            logger.error("合伙人协议,根据订单号查不到订单明细,dealNo={}", dealNo);
            return null;
        }
        HighDealOrderDtlPo highDealOrderDtlPo = dealOrderDtlPoList.get(0);
        if (highDealOrderDtlPo.getSubsAmt() != null) {
            String amtStr = highDealOrderDtlPo.getSubsAmt().multiply(new BigDecimal("0.0001")).stripTrailingZeros().toPlainString();
            partnershipInfo.setCustomerSubsAmt(amtStr);
        }

        // 客户联系地址
        String address = "";
        if (!StringUtils.isEmpty(custInfoResult.getCountyCode())) {
            Map<String, String> geographyInfo = queryAddressService.getGeographyInfo(custInfoResult.getProvCode(), custInfoResult.getCityCode(), custInfoResult.getCountyCode());
            if (geographyInfo != null) {
                if (!StringUtils.isEmpty(geographyInfo.get(custInfoResult.getProvCode()))) {
                    address += geographyInfo.get(custInfoResult.getProvCode());

                }
                if (!StringUtils.isEmpty(geographyInfo.get(custInfoResult.getCityCode()))) {
                    address += geographyInfo.get(custInfoResult.getCityCode());

                }
                if (!StringUtils.isEmpty(geographyInfo.get(custInfoResult.getCountyCode()))) {
                    address += geographyInfo.get(custInfoResult.getCountyCode());
                }
            }
        }
        address += custInfoResult.getAddr();
        partnershipInfo.setCustomerAddress(address);
        return partnershipInfo;
    }


    /**
     * 签名文件名前缀
     **/
    private String buildEsignFileName(CustEsignaturePo custEsignature, String fileSufix) {
        return custEsignature.getSignatureSid() +
                UNDERLINE_SPLIT +
                custEsignature.getTxAcctNo() +
                UNDERLINE_SPLIT +
                custEsignature.getProductCode() +
                UNDERLINE_SPLIT +
                fileSufix;
    }
}
