/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.service.order.dayend;

import com.howbuy.tms.high.batch.service.common.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.repository.HighDayEndRepository;
import com.howbuy.tms.high.batch.service.task.DayEndBackBookTask;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:私募日终处理服务类
 * <AUTHOR>
 * @date 2017年7月21日 下午1:40:16
 * @since JDK 1.6
 */
@Service("highDayEndService")
public class HighDayEndService {
    private static Logger logger = LogManager.getLogger(HighDayEndService.class);

    @Autowired
    private HighDayEndRepository highDayEndRepository;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    /**
     * execDayEnd:执行日终处理
     *
     * @param tradeDt
     * @param taCode
     * <AUTHOR>
     * @date 2017年7月21日 下午1:45:24
     */
    public void execDayEnd(String tradeDt, String taCode, String sysCode) {
        logger.info("HighDayEndService|execAckDayEnd|start|taCode:{}", taCode);
        // 删除临时账本明细
        highDayEndRepository.deleteSimuTmpCustBooksDtl(tradeDt, taCode, sysCode);
        // 异步两套账本备份
        howBuyRunTaskUil.runTask(new DayEndBackBookTask(taCode, tradeDt, highDayEndRepository));
        logger.info("HighDayEndService|execAckDayEnd|start|taCode:{}", taCode);
    }


}
