package com.howbuy.tms.high.batch.service.business.ordersubmit;


import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.interlayer.product.service.FundProductService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.OrderUpdateType;
import com.howbuy.tms.common.enums.database.DiffProcessStatusEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.enums.database.TradeExcpSrcEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.high.batch.dao.po.batch.HighTradeExceptionPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.service.business.autoecontract.SpecialProductSignEcontractService;
import com.howbuy.tms.high.batch.service.business.refreshdealorderstatus.RefreshDealOrderStatusService;
import com.howbuy.tms.high.batch.service.service.order.simufundcheckorder.SimuFundCheckOrderService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 对账订单合并上报服务
 *
 * @author: huaqiang.liu
 * @date: 2021/3/12 10:32
 * @since JDK 1.8
 */
public abstract class CheckOrderMergeSubmitService {
    private Logger logger = LogManager.getLogger(this.getClass());

    /**
     * 私募对账订单表操作服务
     */
    @Autowired
    private SimuFundCheckOrderService simuFundCheckOrderService;

    @Autowired
    private RefreshDealOrderStatusService refreshDealOrderStatusService;

    @Autowired
    private FundProductService fundProductService;

    @Autowired
    private SpecialProductSignEcontractService specialProductSignEcontractService;

    @Autowired
    private CheckOrderSubmitCommonService checkOrderSubmitCommonService;

    /**
     * 上报
     *
     * @param checkOrderList
     * @return com.howbuy.tms.high.batch.service.business.ordersubmit.BaseResponse
     * @author: huaqiang.liu
     * @date: 2021/3/12 10:33
     * @since JDK 1.8
     */
    public BaseResponse submit(List<SimuFundCheckOrderManualSubmitVo> checkOrderList) {
        BaseResponse baseResponse;
        try {
            long start = System.currentTimeMillis();
            baseResponse = doSubmit(checkOrderList);
            long end = System.currentTimeMillis();
            logger.info("doSubmit|cost time:{},response:{}", (end - start), JSON.toJSONString(baseResponse));
        } catch (Throwable e) {
            logger.error("doSubmit|call out System Exception.", e);
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_ACCESS_DUBBO_INTERFACE_FAILED,
                    MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_ACCESS_DUBBO_INTERFACE_FAILED));
        }
        return baseResponse;
    }

    /**
     * 执行上报操作
     *
     * @param checkOrderList
     * @return com.howbuy.tms.high.batch.service.business.ordersubmit.BaseResponse
     * @author: huaqiang.liu
     * @date: 2021/3/12 10:34
     * @since JDK 1.8
     */
    protected abstract BaseResponse doSubmit(List<SimuFundCheckOrderManualSubmitVo> checkOrderList);

    /**
     * 上报后处理模板方法
     *
     * @param checkOrderList
     * @param response
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/3/12 10:35
     * @since JDK 1.8
     */
    public void execPostSubmit(List<SimuFundCheckOrderManualSubmitVo> checkOrderList, BaseResponse response) {
        try {
            // 回写返回码与返回信息
            checkOrderSubmitCommonService.feedShortReturnCode(response);

            checkOrderList.forEach(order -> {
                order.setRetCode(response.getReturnCode());
                order.setRetDesc(response.getDescription());
                // 客户信息重新脱敏
                order.setIdNo(PrivacyUtil.encryptIdCard(order.getIdNo()));
                order.setCustName(order.getCustName());
                order.setBankAcct(PrivacyUtil.encryptBankAcct(order.getBankAcct()));
            });

            if (checkOrderSubmitCommonService.isSuccess(response) || checkOrderSubmitCommonService.isCallSubmited(response)) {
                // 上报成功
                // 更新对账订单状态
                // 更新操作
                // 成功时，TA工作日从私募返回
                checkOrderList.forEach(order -> {
                    String submitTaDt = response.getTaTradeDt();
                    if (StringUtils.isEmpty(submitTaDt) || submitTaDt.equals(order.getSubmitTaDt())) {
                        // 返回是旧的TA工作日，或返回是空，不更新
                        submitTaDt = null;
                    }
                    order.setSubmitTaDt(submitTaDt);
                    order.setContractNo(response.getMiddleDealNoToContractNoMap().get(order.getSubmitDealNo()));

                    // set预计确认日期
                    String preActDt = checkOrderSubmitCommonService.queryPreAckDt(order);
                    order.setPreAckDt(preActDt);
                });

                // 上报完成后，更新订单状态
                simuFundCheckOrderService.updateSubmitSuccess(checkOrderList);

                // 特殊产品上报完成后，需要立即生成电子合同
                if (BusinessCodeEnum.SUBS.getMCode().equals(checkOrderList.get(0).getmBusiCode()) ||
                        BusinessCodeEnum.PURCHASE.getMCode().equals(checkOrderList.get(0).getmBusiCode())) {
                    specialProductSignEcontractService.process(checkOrderList.get(0).getMainDealOrderNo(), checkOrderList.get(0).getFundCode());
                }
            } else {
                // 上报失败
                // 业务类异常
                // 生成异常单（需要订单原始数据，优先生成）
                List<HighTradeExceptionPo> exceptionPoList = new ArrayList<>(checkOrderList.size());
                checkOrderList.forEach(order -> exceptionPoList.add(createHighTradeException(order)));
                if (!checkOrderSubmitCommonService.isCallError(response)) {
                    // 修改订单申请状态后，更新订单状态，新增账务变动明细
                    // 失败时，TA工作日从PO获取（即保持与DB中一致）
                    simuFundCheckOrderService.updateSubmitFailed(checkOrderList);
                    // 更新订单状态或订单支付状态，记录监控表(仅主订单)，发送kafka消息到es-center
                    for (SimuFundCheckOrderManualSubmitVo order : checkOrderList) {
                        if (order.getDealNo().equals(order.getMainDealOrderNo())) {
                            refreshDealOrderStatusService.refleshDealOrderStatus(order.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
                        } else {
                            // 除主订单外，仅修改状态
                            refreshDealOrderStatusService.refleshDealOrderStatusWithOutMonitor(order.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
                        }
                    }
                }
                // 记录上报异常明细
                exceptionPoList.forEach(highTradeExceptionPo -> checkOrderSubmitCommonService.addFundCheckOrderDiff(highTradeExceptionPo));
            }

        } catch (Exception ex) {
            logger.error("Error execPostSubmit mainDealOrderNo:{}", checkOrderList.get(0).getMainDealOrderNo(), ex);
        }
    }

    /**
     * creeateHighTradeException:(创建交易异常)
     *
     * @param po
     * @return
     * <AUTHOR>
     * @date 2018年7月12日 下午4:28:33
     */
    private HighTradeExceptionPo createHighTradeException(SimuFundCheckOrderPo po) {
        HighTradeExceptionPo orderDiff = new HighTradeExceptionPo();
        BeanUtils.copyProperties(po, orderDiff);
        orderDiff.setTaTradeDt(po.getTradeDt());
        orderDiff.setCheckFlag(Constant.CHECK_FLAG_YES);
        orderDiff.setOperator(Constant.OPERATOR_SYS);
        orderDiff.setChecker(Constant.OPERATOR_SYS);
        orderDiff.setProcessStatus(DiffProcessStatusEnum.UNPROCESS.getKey());
        // 交易异常来源
        orderDiff.setTradeExcepSrc(TradeExcpSrcEnum.SUBMIT_FAIL.getCode());
        orderDiff.setSysCode(SysCodeEnum.BATCH_HIGH.getCode());
        return orderDiff;
    }


}
