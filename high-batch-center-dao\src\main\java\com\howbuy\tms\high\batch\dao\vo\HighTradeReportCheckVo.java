/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.high.batch.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:(合规报表)
 * <AUTHOR>
 * @date 2018年6月19日 上午11:22:02
 * @since JDK 1.7
 */
public class HighTradeReportCheckVo implements Serializable {
    private static final long serialVersionUID = -5673023235478631590L;
    /**
     * 订单号
     */
    private String dealNo;
    /**
     * 申请日期 yyyy-MM-dd
     */
    private String appDt;
    /**
     * 申请时间 HH:mm:ss
     */
    private String appTime;
    /**
     * 客户号
     */
    private String txAcctNo;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    private String idNo;
    /**
     * 业务名称
     */
    private String busiName;
    /**
     * 产品名称
     */
    private String fundName;
    /**
     * 申请金额
     */
    private BigDecimal appAmt;
    /**
     * 申请净金额
     */
    private BigDecimal appNetAmt;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 产品管理人信息 code-name
     */
    private String fundManInfo;
    /**
     * ta信息
     */
    private String taInfo;
    /**
     * ta上报日
     */
    private String submitTaDt;
    /**
     * 投资者类型 个人 机构
     */
    private String investType;
    /**
     * 投资者类型 普通 专业
     */
    private String qualificationType;

    /**
     * 冷静期
     */
    private Date calmDtm;
    /**
     * 冷静期干预标示
     */
    private String calmFlag;
    /**
     * 回访标示
     */
    private String callBackStatus;
    /**
     * 回访干预标示
     */
    private String callBackFlag;
    /**
     * 回访完成时间
     */
    private Date callBackDtm;
    /**
     * 双录状态
     */
    private String dualEntryStatus;
    /**
     * 双录干预标示
     */
    private String dualEntryFlag;
    private Date dualEntryDtm;
    /**
     * 资产证明状态
     */
    private String assetStatus;
    /**
     * 资产证明干预状态
     */
    private String assetFlag;
    private String fundCode;
    private String mBusiCode;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTime() {
        return appTime;
    }

    public void setAppTime(String appTime) {
        this.appTime = appTime;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getBusiName() {
        return busiName;
    }

    public void setBusiName(String busiName) {
        this.busiName = busiName;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppNetAmt() {
        return appNetAmt;
    }

    public void setAppNetAmt(BigDecimal appNetAmt) {
        this.appNetAmt = appNetAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getFundManInfo() {
        return fundManInfo;
    }

    public void setFundManInfo(String fundManInfo) {
        this.fundManInfo = fundManInfo;
    }

    public String getTaInfo() {
        return taInfo;
    }

    public void setTaInfo(String taInfo) {
        this.taInfo = taInfo;
    }

    public String getSubmitTaDt() {
        return submitTaDt;
    }

    public void setSubmitTaDt(String submitTaDt) {
        this.submitTaDt = submitTaDt;
    }

    public String getInvestType() {
        return investType;
    }

    public void setInvestType(String investType) {
        this.investType = investType;
    }

    public String getQualificationType() {
        return qualificationType;
    }

    public void setQualificationType(String qualificationType) {
        this.qualificationType = qualificationType;
    }

    public String getCalmFlag() {
        return calmFlag;
    }

    public void setCalmFlag(String calmFlag) {
        this.calmFlag = calmFlag;
    }

    public String getCallBackStatus() {
        return callBackStatus;
    }

    public void setCallBackStatus(String callBackStatus) {
        this.callBackStatus = callBackStatus;
    }

    public String getCallBackFlag() {
        return callBackFlag;
    }

    public void setCallBackFlag(String callBackFlag) {
        this.callBackFlag = callBackFlag;
    }

    public String getDualEntryStatus() {
        return dualEntryStatus;
    }

    public void setDualEntryStatus(String dualEntryStatus) {
        this.dualEntryStatus = dualEntryStatus;
    }

    public String getDualEntryFlag() {
        return dualEntryFlag;
    }

    public void setDualEntryFlag(String dualEntryFlag) {
        this.dualEntryFlag = dualEntryFlag;
    }

    public String getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }

    public String getAssetFlag() {
        return assetFlag;
    }

    public void setAssetFlag(String assetFlag) {
        this.assetFlag = assetFlag;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public Date getCalmDtm() {
        return calmDtm;
    }

    public void setCalmDtm(Date calmDtm) {
        this.calmDtm = calmDtm;
    }

    public Date getCallBackDtm() {
        return callBackDtm;
    }

    public void setCallBackDtm(Date callBackDtm) {
        this.callBackDtm = callBackDtm;
    }

    public Date getDualEntryDtm() {
        return dualEntryDtm;
    }

    public void setDualEntryDtm(Date dualEntryDtm) {
        this.dualEntryDtm = dualEntryDtm;
    }

}
