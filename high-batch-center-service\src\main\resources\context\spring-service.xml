<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd

		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd

		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <!--对账订单批量上报调度消息接收处理器-->
    <bean id="checkOrderSubmitMessageProcessor"
          class="com.howbuy.tms.high.batch.service.business.ordersubmit.CheckOrderSubmitMessageProcessor">
        <property name="fundCheckOrderSubmitServices">
            <map>
                <!-- 认购 -->
                <entry key="1120" value-ref="fundSubsOrPurSubmitService"></entry>
                <!-- 申购 -->
                <entry key="1122" value-ref="fundSubsOrPurSubmitService"></entry>
                <!-- 赎回 -->
                <entry key="1124_0" value-ref="fundRedeemSubmitService"></entry>
                <!-- 赎回到储蓄罐 -->
                <entry key="1124_1" value-ref="fundRedeemSubmitService"></entry>
                <!-- 修改分红方式 -->
                <entry key="1129" value-ref="fundDivModeChgSubmitService"></entry>
            </map>
        </property>
    </bean>

    <bean id="checkOrderSubmitManualProcessor"
          class="com.howbuy.tms.high.batch.service.business.ordersubmit.CheckOrderSubmitManualProcessor">
        <property name="fundCheckOrderSubmitServices">
            <map>
                <!-- 认购 -->
                <entry key="1120" value-ref="fundSubsOrPurSubmitService"></entry>
                <!-- 申购 -->
                <entry key="1122" value-ref="fundSubsOrPurSubmitService"></entry>
                <!-- 赎回 -->
                <entry key="1124" value-ref="fundRedeemSubmitService"></entry>
                <!-- 修改分红方式 -->
                <entry key="1129" value-ref="fundDivModeChgSubmitService"></entry>
            </map>
        </property>
    </bean>

    <!-- 公募文件处理声明 -->
    <bean name="fundFileProcessorMap" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="ASS0001" value-ref="fundAckFileProcessor"></entry>
                <entry key="ASS0003" value-ref="fundVolCheckFileProcessor"></entry>
                <entry key="ASS0004" value-ref="fundVolDtlCheckFileProcessor"></entry>
                <entry key="MID0004" value-ref="fundDivModeFileProcessor"></entry>
            </map>
        </constructor-arg>
    </bean>

    <bean id="finaMsgNotifyService" class="com.howbuy.tms.high.batch.service.business.message.FinaMsgNotifyService">
        <property name="finaAbstractFileServices">
            <map>
                <!-- TA下发回款方向 -->
                <entry key="TA_SETTLE_TYPE" value-ref="finaDirectionFileProcessor"></entry>
                <!-- 修改回款方向 -->
                <entry key="UPDATE_SETTLE_TYPE" value-ref="finaDirectionFileProcessor"></entry>
                <!-- 储蓄罐下发文件-->
                <entry key="PIG_BUY" value-ref="finaCxgOrderFileProcessor"></entry>
                <!-- 储蓄罐下发完成文件 -->
                <entry key="PIG_BUY_FINISH" value-ref="finaCxgFinishFileProcessor"></entry>
            </map>
        </property>
    </bean>

    <bean id="finaCxgFileManualImportProcessor"
          class="com.howbuy.tms.high.batch.service.business.finafilemonitor.FinaCxgFileManualImportProcessor">
        <property name="finaAbstractFileServices">
            <map>
                <!-- TA下发回款方向 -->
                <entry key="TA_SETTLE_TYPE" value-ref="finaDirectionFileProcessor"></entry>
                <!-- 修改回款方向 -->
                <entry key="UPDATE_SETTLE_TYPE" value-ref="finaDirectionFileProcessor"></entry>
                <!-- 储蓄罐下发文件-->
                <entry key="PIG_BUY" value-ref="finaCxgOrderFileProcessor"></entry>
                <!-- 储蓄罐下发完成文件 -->
                <entry key="PIG_BUY_FINISH" value-ref="finaCxgFinishFileProcessor"></entry>
            </map>
        </property>
    </bean>

</beans>