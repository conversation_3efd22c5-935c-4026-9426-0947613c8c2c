package com.howbuy.tms.high.batch.service.service.file.fileexport.pdf;

import com.alibaba.fastjson.JSON;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.dfile.HOutputStream;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.batch.dao.po.batch.HighFundFileStatusPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.service.builder.HighFileStatusBuilder;
import com.howbuy.tms.high.batch.service.common.*;
import com.howbuy.tms.high.batch.service.repository.HighFundFileStatusRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.CommonConfService;
import com.howbuy.tms.high.batch.service.service.file.fileexport.pdf.bean.PdfFileExportContext;
import com.lowagie.text.pdf.BaseFont;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;
import org.xhtmlrenderer.util.XRLog;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * pdf文件导出基类
 * T 为html渲染的数据格式
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public abstract class AbstractPdfWithHtmlFileExportService<T> extends CommonConfService {

    @Autowired
    private HighFundFileStatusRepository highFundFileStatusRepository;
    @Autowired
    private HighFileStatusBuilder highFileStatusBuilder;

    protected static final CacheService cacheService = CacheServiceImpl.getInstance();


    public String process(PdfFileExportContext context) throws Exception {
        log.info("file export start, context={}", JSON.toJSONString(context));
        FileSdkPathInfo pdfFileSdkPathInfo = getPdfFileSdkPathInfo(context);
        Map<String, Object> params = context.getParams();

        if (StringUtils.isEmpty((String) params.get("taTradeDt"))) {
            params.put("taTradeDt", DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD));
        }

        // 文件绝对路径
        String absolutePath = FileSdkUtil.getAbsolutePath(pdfFileSdkPathInfo);

        // 判断文件导出记录是否存在，若为处理中，则抛出异常，否则更新为处理中;若不存在，则创建记录
        String recordNo = updateFileSatusProcessing(pdfFileSdkPathInfo.getBusinessCode(), params, absolutePath);

        // 删除文件fileName
        if (FileSdkUtil.exists(pdfFileSdkPathInfo)) {
            FileSdkUtil.deleteFile(pdfFileSdkPathInfo);
        } else {
            FileSdkUtil.mkdir(pdfFileSdkPathInfo);
        }

        try {
            // 将具体的html文件，转换pdf文件
            createPdfFileV2(context, recordNo);

            // 生成后操作
            doAfterCreate(context);
        } catch (Exception e) {
            log.error("create file{} failed.", absolutePath, e);
            // 更新文件处理状态为失败
            highFundFileStatusRepository.updateFileOpStatus(recordNo, HighFileOpStatusEnum.GENERATE_FAIL.getCode());
            // 告警
            String msg = "导出pdf文件失败,businessCode:" + pdfFileSdkPathInfo.getBusinessCode() + "middlePath:" + pdfFileSdkPathInfo.getMiddlePath() + "fileName:" + pdfFileSdkPathInfo.getFileName();
            OpsMonitor.warn(msg, OpsMonitor.ERROR);
            log.info(msg);
            throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR, "导出pdf文件失败");
        }

        // 更新文件处理状态为成功
        highFundFileStatusRepository.updateFileOpStatus(recordNo, HighFileOpStatusEnum.GENERATE_SUCCESS.getCode());
        log.info("pdf文件{}，生成成功", absolutePath);
        return absolutePath;
    }

    /**
     * 生成后操作
     */
    public void doAfterCreate(PdfFileExportContext context) throws Exception {
        log.info("生成好不需要处理");
    }

    /**
     * 获取htm渲染数据
     *
     * @param params
     * @return
     */
    public abstract T getHtmlData(Map<String, Object> params) throws Exception;


    /**
     * 获取pdf文件sdk路径信息
     */
    public abstract FileSdkPathInfo getPdfFileSdkPathInfo(PdfFileExportContext context);

    /**
     * 获取模版名
     *
     * @return
     */
    public abstract String getTemplateName(PdfFileExportContext context);

    /**
     * 获取模版渲染页面
     */
    public String getHtmlTemplateWithDataUrl(PdfFileExportContext context, String recordNo) throws Exception {
        FileSdkPathInfo pdfFileSdkPathInfo = getPdfFileSdkPathInfo(context);
        String dataKey = pdfFileSdkPathInfo.getBusinessCode() + recordNo + RandomUtils.getRamdomNumber(4);
        T htmlData = getHtmlData(context.getParams());
        cacheService.put(3600, HighCacheKeyPrefix.HIGH_CACHE_KEY_PREFIX + dataKey, htmlData);
        String htmlUrl = this.getHighBatchIp() + "/highBatch/queryTemplateHtml.htm?dataKey=" + dataKey + "&templateName=" + getTemplateName(context);
        log.info("getHtmlTemplateWithDataUrl-获取html模版映射内容url:{}", htmlUrl);
        return htmlUrl;
    }


    /**
     * 生成pdf文件
     */
    private void createPdfFileV2(PdfFileExportContext context, String recordNo) throws Exception {
        FileSdkPathInfo pdfFileSdkPathInfo = getPdfFileSdkPathInfo(context);
        HOutputStream os = null;
        try {
            os = FileSdkUtil.buildHOutputStream(pdfFileSdkPathInfo);
            ITextRenderer renderer = getHtmlConvertToPdfReader("templates/front/" + getFrontName(context));
            renderer.setDocument(getHtmlTemplateWithDataUrl(context, recordNo));
            renderer.layout();
            renderer.createPDF(os.getOutputStream());
        } catch (Exception e) {
            log.error("生成pdf文件异常 error ", e);
            throw e;
        } finally {
            if (os != null) {
                os.flush();
                os.close();
            }
        }
    }

    /**
     * 获取字体文件名
     */
    public abstract String getFrontName(PdfFileExportContext context);

    private ITextRenderer getHtmlConvertToPdfReader(String fontFile) throws Exception {
        ITextRenderer renderer;
        synchronized (XRLog.class) {
            renderer = new ITextRenderer();
        }
        try {
            log.info("PdfGeneratorService|htmlConvertToPdf|doConvert start ");
            ITextFontResolver fontResolver = renderer.getFontResolver();
            fontResolver.addFont(fontFile, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            return renderer;
        } catch (Exception e) {
            log.info("PdfGeneratorService|htmlConvertToPdf|doConvert error:{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新文件处理状态为处理中
     *
     * @param businessCode
     * @param params
     * @param absolutePath
     */
    private String updateFileSatusProcessing(String businessCode, Map<String, Object> params, String absolutePath) {
        HighFundFileStatusPo highFundFileStatusPo = highFundFileStatusRepository.selectByTradeDateAndFileType(params.get("taTradeDt").toString(), businessCode, absolutePath);
        if (highFundFileStatusPo == null) {
            highFundFileStatusPo = highFileStatusBuilder.createHighFileStatus(params.get("taTradeDt").toString(), businessCode, absolutePath, HighFileOptionEnum.GENERATE.getCode());
            highFundFileStatusRepository.insertSelective(highFundFileStatusPo);
        } else {
            if (Objects.equals(highFundFileStatusPo.getFileOpStatus(), HighFileOpStatusEnum.PROCESSING.getCode())) {
                throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR, "文件正在生成中，请稍后再试");
            }
            highFundFileStatusPo.setFileOpStatus(HighFileOpStatusEnum.PROCESSING.getCode());
            highFundFileStatusPo.setUpdateDtm(new Date());
            highFundFileStatusRepository.updateByPrimaryKeySelective(highFundFileStatusPo);
        }
        return highFundFileStatusPo.getRecordNo();
    }

}
