package com.howbuy.tms.high.batch.service.business.crmfilemonitor;

import com.alibaba.fastjson.JSON;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.business.autobatch.AutoDayInitProcessor;
import com.howbuy.tms.high.batch.service.business.crmfilemonitor.everyhour.CrmHkFileEveryHourLogicService;
import com.howbuy.tms.high.batch.service.common.CrmFileReceiveTypeEnum;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.repository.CmFileProcessRecRepository;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import com.howbuy.tms.high.batch.service.service.file.fileexport.CommonConfService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @api {MQ} DTMS_ORDER_TRADE_RECORD_SYNC_ZT
 * @apiGroup DTMS_ORDER_TRADE_RECORD_SYNC_ZT_Group
 * @apiName 海外推送文件处理
 * @apiDescription 海外推送文件处理
 */
@Service("crmFileHkNotifyProcessor")
public class CrmFileHkNotifyProcessor extends BatchMessageProcessor {
    private static final Logger logger = LogManager.getLogger(CrmFileHkNotifyProcessor.class);

    private static final String HK_EVERY_HOUR = "1";
    @Autowired
    protected WorkdayService workdayService;
    @Value("${high.crm.hk.file.queue}")
    private String queueName;
    @Autowired
    private CommonConfService commonConfService;
    @Autowired
    private CrmHkFileEveryHourLogicService crmHkFileEveryHourLogicService;
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @Autowired
    private CrmHkDayFileProcessor crmFileProcessor;
    @Autowired
    private CmFileProcessRecRepository cmFileProcessRecRepository;

    @Override
    public void doProcessMessage(SimpleMessage message) {
        // 1.参数校验
        if (message == null) {
            logger.info("CrmFileHkNotifyProcessor-doProcessMessage-消息是空的");
            return;
        }
        String content = (String) message.getContent();
        logger.info("CrmFileHkNotifyProcessor-doProcessMessage-开始,content:{}", content);
        CrmHkFileMessageVo vo = JSON.parseObject(content, CrmHkFileMessageVo.class);
        if (StringUtils.isBlank(vo.getBatchNo()) || StringUtils.isBlank(vo.getFileDt()) || StringUtils.isBlank(vo.getFileType())) {
            logger.error("CrmFileHkNotifyProcessor-doProcessMessage,缺少关键字段");
            return;
        }
        CrmFileMessageVo crmFileMessageVo = new CrmFileMessageVo();
        crmFileMessageVo.setFundcount(vo.getFundCount());
        crmFileMessageVo.setTradecount(vo.getTradeCount());
        crmFileMessageVo.setNaFeeCount(vo.getNafeeCount());
        crmFileMessageVo.setFlag(YesOrNoEnum.YES.getCode());
        crmFileMessageVo.setYjyzcount(0);
        crmFileMessageVo.setBlackcount(0);
        crmFileMessageVo.setDealdt(vo.getFileDt());
        crmFileMessageVo.setSversion(vo.getBatchNo());
        // 为了将海外的跟老直销的版本号区分开,特地加一位
        crmFileMessageVo.setVersion(new BigDecimal(vo.getFileDt() + vo.getBatchNo() + "1"));
        if (StringUtils.isNotBlank(vo.getPushTime())) {
            crmFileMessageVo.setDtTime(DateUtils.formatToDate(vo.getPushTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        } else {
            crmFileMessageVo.setDtTime(new Date());
        }
        if (HK_EVERY_HOUR.equals(vo.getFileType())) {
            crmFileMessageVo.setFileType(CrmFileReceiveTypeEnum.HK_EVERY_HOUR.getType());
            processEveryHourFile(crmFileMessageVo);
        } else {
            crmFileMessageVo.setFileType(CrmFileReceiveTypeEnum.HK_DAY.getType());
            processDayFile(crmFileMessageVo);
        }
        logger.info("CrmFileHkNotifyProcessor-doProcessMessage-结束");
    }

    /**
     * 每小时处理海外文件
     */
    private void processEveryHourFile(CrmFileMessageVo vo) {
        Date appDt = new Date();
        // 1.如果开始接收到crm的全量文件,就不处理
        String workDay = queryTradeDayOuterService.getWorkDay(appDt);
        long count = cmFileProcessRecRepository.countDayTypeByWorkdayHk(workDay);
        if (count > 0 && YesOrNoEnum.NO.getCode().equals(commonConfService.getPushAssertAllTime())) {
            logger.error("CrmFileHkNotifyProcessor-已经接受每天推送一次的crm文件,就不接受每小时增量推送的消息,vo={}", JSON.toJSONString(vo));
            return;
        }
        // 2.文件处理
        try {
            crmHkFileEveryHourLogicService.process(vo);
        } catch (Exception e) {
            logger.error("CrmFileEveryHourNotifyProcessor error:{}", e.getMessage(), e);
        }
    }

    /**
     * 每天处理的文件
     */
    private void processDayFile(CrmFileMessageVo vo) {
        String tradeDt = workdayService.getSaleSysCurrWorkay();
        // 判断当前日期是否是工作日
        String nowDay = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        if (!nowDay.equals(tradeDt)) {
            logger.info("CrmFileHkNotifyProcessor-processDayFile,nowDay:{} is not work day :{}", nowDay, tradeDt);
            return;
        }
        // 文件处理
        try {
            crmFileProcessor.process(vo);
            OpsMonitor.warn(tradeDt + "处理日终海外文件结束", OpsMonitor.INFO);
        } catch (Exception e) {
            logger.error("CrmFileMessageProcessor error:{}", e.getMessage(), e);
        }
    }

    @Override
    protected String getQuartMessageChannel() {
        return queueName;
    }


}
