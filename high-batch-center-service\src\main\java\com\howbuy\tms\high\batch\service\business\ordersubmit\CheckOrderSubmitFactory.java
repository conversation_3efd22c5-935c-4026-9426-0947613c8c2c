package com.howbuy.tms.high.batch.service.business.ordersubmit;

import com.howbuy.tms.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:上报工厂类
 * @Author: yun.lu
 * Date: 2025/4/8 10:18
 */
@Service
@Slf4j
public class CheckOrderSubmitFactory {
    @Autowired
    private List<AbstractCheckOrderSubmitService> abstractCheckOrderSubmitServiceList;


    public AbstractCheckOrderSubmitService getCheckOrderSubmitService(String businessCode) {
        if (StringUtils.isBlank(businessCode)) {
            return null;
        }
        for (AbstractCheckOrderSubmitService abstractCheckOrderSubmitService : abstractCheckOrderSubmitServiceList) {
            if (abstractCheckOrderSubmitService.userThis(businessCode)) {
                return abstractCheckOrderSubmitService;
            }
        }
        return null;
    }
}
