/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.repurchase;

import com.alibaba.fastjson.JSON;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @api {MQ} sync_add_repurchase_queue
 * @apiGroup message
 * @apiName 增加复购协议
 * @apiDescription 增加复购协议
 */
@Service
public class AddRepurchaseProcessor extends BatchMessageProcessor {
    private Logger logger = LoggerFactory.getLogger(AddRepurchaseProcessor.class);

    @Value("${sync.add.repurchase.queue}")
    private  String syncAddRepurchaseQueue;

    @Autowired
    private AddRepurchaseService addRepurchaseService;

    /**
     * 是否调度
     * @return
     */
    @Override
    public boolean isSchedule() {
        return false;
    }

    @Override
    protected String getQuartMessageChannel() {
        return syncAddRepurchaseQueue;
    }

    @Override
    public void doProcessMessage(SimpleMessage simpleMessage) {
        logger.info("AddRepurchaseProcessor|doProcessMessage|message:{}", JSON.toJSONString(simpleMessage));

        try{
            Object content =  simpleMessage.getContent();
            if(content == null){
                logger.error("AddRepurchaseProcessor|processMessage| message contet is null");
                return ;
            }

            ProductRepurchaseMessageBean productRepurchaseMessageBean = JSON.parseObject((String)content, ProductRepurchaseMessageBean.class);
            addRepurchaseService.process(productRepurchaseMessageBean);
        }catch(Throwable e) {
            logger.error("AddRepurchaseProcessor|processMessage|error:",e);
        }

    }

}
