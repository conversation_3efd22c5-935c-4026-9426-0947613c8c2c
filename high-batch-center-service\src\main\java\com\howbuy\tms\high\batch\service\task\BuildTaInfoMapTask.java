package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.common.outerservice.interlayer.querytainfo.QueryTaInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.bean.TaInfoBean;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * @Description:设置ta信息
 * @Author: yun.lu
 * Date: 2025/4/9 17:40
 */
@Data
@AllArgsConstructor
public class BuildTaInfoMapTask extends AbstractHowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(BuildTaInfoMapTask.class);

    private QueryTaInfoOuterService queryTaInfoOuterService;
    private Map<String, String> map;
    private String productChannel;

    @Override
    protected void callTask() {
        List<TaInfoBean> highFundTaList = queryTaInfoOuterService.getTaInfosByProductChannel(productChannel);
        if (!CollectionUtils.isEmpty(highFundTaList)) {
            for (TaInfoBean bean : highFundTaList) {
                map.put(bean.getTaCode(), bean.getTaName());
            }
        } else {
            logger.warn("BuildTaInfoMapTask-设置ta信息,HIGH_FUND taList is empty");
        }
    }
}
