/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.service.batch.ackdayend;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.FundTypeEnum;
import com.howbuy.tms.common.enums.busi.OwnershipRightTransferCheckFlag;
import com.howbuy.tms.common.enums.busi.ProductTypeEsSysCodeMappingEnum;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.message.topic.MessageBody;
import com.howbuy.tms.common.outerservice.auth.encryptsingle.EncryptSingleOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.sendemailwithattachment.SendEmailWithAttachmentOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.sendemailwithattachment.bean.SendEmailWithAttachmentBean;
import com.howbuy.tms.high.batch.dao.po.batch.TaBusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaskDependDefCfgPo;
import com.howbuy.tms.high.batch.dao.po.order.EsProOrderMonitorPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.OwnershipRightTransferOrderVo;
import com.howbuy.tms.high.batch.facade.enums.BusinessProcessingStepEnum;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransfer.bean.QueryOwnershipRightTransferParam;
import com.howbuy.tms.high.batch.service.common.ExcelUtiles;
import com.howbuy.tms.high.batch.service.event.HighEventPublisher;
import com.howbuy.tms.high.batch.service.event.ackend.ackDayEndEvent.AckDayEndEvent;
import com.howbuy.tms.high.batch.service.event.ackend.taAckDayEndEvent.TaAckDayEndEvent;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.logic.OwnershipTransferOrderLogicService;
import com.howbuy.tms.high.batch.service.repository.TaBusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.service.batch.base.AbstractProcessService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.RecursiveTask;

/**
 * <AUTHOR>
 * @description:确认日终处理服务类
 * @date 2017年7月10日 下午9:34:57
 * @since JDK 1.6
 */
@Service("ackDayEndProcessService")
public class AckDayEndProcessService extends AbstractProcessService {

    private static Logger logger = LogManager.getLogger(AckDayEndProcessService.class);

    @Autowired
    protected TaBusinessBatchFlowRepository taBusinessBatchFlowRepository;
    @Autowired
    private SendEmailWithAttachmentOuterService sendEmailWithAttachmentOuterService;
    @Autowired
    private EncryptSingleOuterService encryptSingleOuterService;
    @Autowired
    private OwnershipTransferOrderLogicService ownershipTransferOrderLogicService;
    @Autowired
    private HighEventPublisher highEventPublisher;


    @Value("${no.match.ownership.right.transfer.email}")
    private String notifyEmails;

    @Value("${match.ownership.right.transfer.date}")
    private String matchDate;

    /**
     * flowCheck:私募流程控制检查
     *
     * @param taTradeDt
     * <AUTHOR>
     * @date 2017年7月12日 下午8:10:58
     */
    public void flowCheck(String taTradeDt, String taCode, String sysCode) {
        String taskId = BusinessProcessingStepEnum.BPS_ACK_DAY_END_PROCESS.getCode();

        // 流程任务项检查
        List<TaskDependDefCfgPo> taskPreDependList = taskDependDefCfgRepository.selectPreUnCompletedTATask(taskId, taTradeDt, sysCode, taCode);
        if (CollectionUtils.isNotEmpty(taskPreDependList)) {
            // 前置节点状态未完成，不能执行当前操作
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_PRE_UNCOMPLETED, "批处理执行失败！确认处理未完成");
        }

        List<TaskDependDefCfgPo> taskPostDependList = taskDependDefCfgRepository.selectPostAlreadyExecTask(taskId, taTradeDt, sysCode);
        if (CollectionUtils.isNotEmpty(taskPostDependList)) {
            // 后置节点已经开始处理，不能执行当前操作
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_POST_ALREADY_START, "批处理执行失败！日终处理已开始执行或处理完成");
        }

        TaBusinessBatchFlowPo taBusinessBatchFlowPo = taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(taCode, sysCode, taTradeDt, taskId);

        if (taBusinessBatchFlowPo == null) {
            // 当前任务不存在
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_BATCH_FLOW_ERROR, "当前TA任务不存在");
        }

        if (BatchStatEnum.PROCESSING.getKey().equals(taBusinessBatchFlowPo.getFlowStat())) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_PROCESSING, "当前TA任务正在执行中");
        }

        if (BatchStatEnum.PROCESS_SUCCESS.getKey().equals(taBusinessBatchFlowPo.getFlowStat())) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_PROCESSING, "当前TA任务已处理完成");
        }

        // 更新为执行中
        String startTm = String.format("%1$tH%1$tM%1$tS", new Date());
        taBusinessBatchFlowRepository.updateBatchStat(taCode, taskId, taTradeDt, BatchStatEnum.PROCESSING.getKey(), taBusinessBatchFlowPo.getFlowStat(), sysCode, startTm, null);

        clearTaskPool(taCode);
    }

    /**
     * ta日终处理结束触发的处理流程
     */
    public void endTaOff(String tradeDt, String taCode, String sysCode, String flowStat) {
        String taskId = BusinessProcessingStepEnum.BPS_ACK_DAY_END_PROCESS.getCode();
        String endTm = String.format("%1$tH%1$tM%1$tS", new Date());

        // 1.处理中更新成处理失败/成功
        taBusinessBatchFlowRepository.updateBatchStat(taCode, taskId, tradeDt, flowStat, BatchStatEnum.PROCESSING.getKey(), sysCode, null, endTm);
        // 2.如果处理成功,触发的逻辑
        if (BatchStatEnum.PROCESS_SUCCESS.getKey().equals(flowStat)) {
            // 发送ta确认处理结束事件
            highEventPublisher.publishEvent(new TaAckDayEndEvent(taCode, tradeDt));
        }


    }

    /**
     * endoff:endoff
     *
     * @param tradeDt
     * @param sysCode
     * <AUTHOR>
     * @date 2017年7月12日 下午9:16:34
     */
    public void endAllOff(String tradeDt, String sysCode) {
        String taskId = BusinessProcessingStepEnum.BPS_ACK_DAY_END_PROCESS.getCode();
        String endTm = String.format("%1$tH%1$tM%1$tS", new Date());
        // 统计当前TA业务节点是否存在未处理成功的
        int count = taBusinessBatchFlowRepository.countTaBusinessBatchFlowNoProcessByTaskId(tradeDt, taskId, sysCode);
        if (count == 0) {
            businessBatchFlowRepository.updateBatchStatus(tradeDt, taskId, BatchStatEnum.PROCESS_SUCCESS.getKey(), endTm, endTm, sysCode);
            // 发送确认处理日终事件
            highEventPublisher.publishEvent(new AckDayEndEvent(tradeDt));
        }
    }

    /**
     * 异步发送未匹配股权订单预警邮件
     */
    public void asyncNotifyUnMatchOwnershipTransfer(String tradeDt) {
        logger.info("NotifyNoMatchOwnershipRightTransferProcessor-asyncNotifyUnMatchOwnershipTransfer,start,tradeDt={}", tradeDt);
        // 1.解析消息,获取收件人信息
        if (StringUtils.isBlank(notifyEmails) || StringUtils.isBlank(matchDate)) {
            logger.error("NotifyNoMatchOwnershipRightTransferProcessor-asyncNotifyUnMatchOwnershipTransfer,收件人信息/匹配起始日期是空的,matchDate={},notifyEmails={}", matchDate, notifyEmails);
            return;
        }
        // 2.找出确认日期大于系统上线时间,审核状态为待维护,产品为股权+业务类型=非交易过户转入,非交易过户转出,强增,强减 的订单;注意为了限制邮件大小,防止oom,限制查询出的订单数量
        QueryOwnershipRightTransferParam param = new QueryOwnershipRightTransferParam();
        param.setBeginDtm(matchDate);
        param.setCheckFlag(OwnershipRightTransferCheckFlag.WAIT_MAKE.getCode());
        param.setFundType(FundTypeEnum.SM.getCode());
        param.setFundSubType(FundTypeEnum.PE_VC.getCode());
        List<String> mBusinessCodeList = Arrays.asList(BusinessCodeEnum.FORCE_ADD.getMCode(), BusinessCodeEnum.FORCE_SUBTRACT.getMCode(), BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode(), BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode());
        param.setMBusinessCodeList(mBusinessCodeList);
        List<OwnershipRightTransferOrderVo> ownershipRightTransferOrderVoList = ownershipTransferOrderLogicService.queryOwnershipTransferOrder(param);
        // 判空
        if (CollectionUtils.isEmpty(ownershipRightTransferOrderVoList)) {
            logger.info("NotifyNoMatchOwnershipRightTransferProcessor-asyncNotifyUnMatchOwnershipTransfer,没有需要预警的未匹配股权订单,tradeDt={}", tradeDt);
            return;
        }
        try {
            String excelName = "截止到" + tradeDt + "待维护信息";
            // 3.将查出的订单信息转为excel
            byte[] bytes = buildExcelByData(excelName, ownershipRightTransferOrderVoList);
            // 4.发送邮件
            sendNoMatchOwnershipRightNotifyEmail(bytes, tradeDt);
        } catch (IOException e) {
            logger.info("NotifyNoMatchOwnershipRightTransferProcessor-asyncNotifyUnMatchOwnershipTransfer,发送需要预警的未匹配股权订单告警邮件失败,tradeDt={},e={}", tradeDt, e);
        }
    }

    /**
     * 发送未匹配股权订单告警邮件
     */
    private void sendNoMatchOwnershipRightNotifyEmail(byte[] emailExcel, String tradeDt) {
        logger.info("sendNoMatchOwnershipRightNotifyEmail-发送未匹配股权订单告警邮件,tradeDt={},notifyEmails={}", tradeDt, notifyEmails);
        //1.构建发送邮件入参
        SendEmailWithAttachmentBean emailBean = new SendEmailWithAttachmentBean();
        emailBean.setBusinessId("60429");
        // 邮件内容
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("content", "以上是系统未匹配到非交易过户申请的数据，请smop及时人工维护，以免影响客户收益计算");
        emailBean.setTemplateVar(JSON.toJSONString(contentMap));
        // 标题
        emailBean.setTitle("未匹配到申请的股权强增强减&非交易过户记录" + tradeDt);
        // 邮件附件
        SendEmailWithAttachmentBean.AttachmentBean attachmentBean = new SendEmailWithAttachmentBean.AttachmentBean();
        attachmentBean.setFile(emailExcel);
        attachmentBean.setDisplayFileName("未匹配到申请的股权强增强减&非交易过户记录");
        attachmentBean.setFileFormat(".xls");
        emailBean.setAttachmentList(Collections.singletonList(attachmentBean));
        // 2.发送邮件,需要按照遍历收件人,一个个的发
        for (String email : notifyEmails.split(",")) {
            if (StringUtils.isNotBlank(email)) {
                String encryptEmail = encryptSingleOuterService.encryptSingle(email);
                emailBean.setEmail(encryptEmail);
                Integer result = sendEmailWithAttachmentOuterService.execute(emailBean);
                if (result == null || result != 0) {
                    logger.error("NotifyNoMatchOwnershipRightTransferProcessor-sendNoMatchOwnershipRightNotifyEmail,发送邮件出现失败,tradeDt={},email={},encryptEmail={}", tradeDt, email, encryptEmail);
                }
            }
        }
    }

    /**
     * 将数据转换为excel数据流
     */
    public byte[] buildExcelByData(String excelName, List<OwnershipRightTransferOrderVo> orderVoList) throws IOException {
        String[] excelHeader = {"客户号", "客户姓名", "业务名称", "基金代码", "基金名称", "基金类型", "基金二级类型", "确认份额", "确认金额", "确认日期"};
        String[] ds_titles = {"custNo", "custName", "busiName", "fundCode", "fundName", "fundType", "fundSubType", "ackVol", "ackAmt", "ackDt"};
        int[] ds_format = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
        List<Map<String, Object>> dataList = new LinkedList<Map<String, Object>>();
        for (OwnershipRightTransferOrderVo vo : orderVoList) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("custNo", vo.getTxAcctNo());
            map.put("custName", vo.getCustName());
            map.put("busiName", BusinessCodeEnum.getName(vo.getMBusinessCode()));
            map.put("fundCode", vo.getFundCode());
            map.put("fundName", vo.getFundName());
            map.put("fundType", vo.getFundType());
            map.put("fundSubType", vo.getFundSubType());
            map.put("ackVol", vo.getAckVol());
            map.put("ackAmt", vo.getAckAmt());
            map.put("ackDt", vo.getAckDtm());
            dataList.add(map);
        }
        return ExcelUtiles.export(excelName, "待处理数据", excelHeader, ds_titles, ds_format, null, dataList);

    }



    private class OrderCollectTask extends RecursiveTask<List<MessageBody>> {
        private static final long serialVersionUID = -2875372117406912561L;
        private List<SimuFundCheckOrderPo> orderList;

        public OrderCollectTask(List<SimuFundCheckOrderPo> orderList) {
            this.orderList = orderList;
        }

        @Override
        protected List<MessageBody> compute() {
            List<MessageBody> result = new ArrayList<MessageBody>();
            if (CollectionUtils.isEmpty(orderList)) {
                return result;
            }

            int count = orderList.size();
            if (count == 1) {
                SimuFundCheckOrderPo vo = orderList.get(0);
                if (!BusinessCodeEnum.FUND_SHARE_TRANSFER_IN.getMCode().equals(vo.getmBusiCode())
                        && !BusinessCodeEnum.FUND_SHARE_TRANSFER_OUT.getMCode().equals(vo.getmBusiCode())) {
                    String sysCode = ProductTypeEsSysCodeMappingEnum.getSysCode(ProductClassEnum.HIGH.getCode(), vo.getFundType());
                    MessageBody messageBody = new MessageBody();
                    messageBody.setDealNo(vo.getDealNo());
                    messageBody.setSysCode(sysCode);
                    messageBody.setTxAcctNo(vo.getTxAcctNo());
                    result.add(messageBody);
                    return result;
                }
            }

            int mid = count / 2;

            List<SimuFundCheckOrderPo> taskAOne = new ArrayList<SimuFundCheckOrderPo>();
            List<SimuFundCheckOrderPo> taskBTwo = new ArrayList<SimuFundCheckOrderPo>();
            taskAOne.addAll(orderList.subList(0, mid));
            taskBTwo.addAll(orderList.subList(mid, count));

            OrderCollectTask taskOne = new OrderCollectTask(taskAOne);
            OrderCollectTask taskTwo = new OrderCollectTask(taskBTwo);
            taskOne.fork();
            taskTwo.fork();

            List<MessageBody> resultOne = taskOne.join();
            List<MessageBody> resultTwo = taskTwo.join();
            if (CollectionUtils.isNotEmpty(resultOne)) {
                result.addAll(resultOne);
            }
            if (CollectionUtils.isNotEmpty(resultTwo)) {
                result.addAll(resultTwo);
            }

            return result;
        }
    }


    private class EsProOrderTask extends RecursiveTask<List<EsProOrderMonitorPo>> {
        private static final long serialVersionUID = -2875372117406912561L;
        private List<SimuFundCheckOrderPo> orderList;

        public EsProOrderTask(List<SimuFundCheckOrderPo> orderList) {
            this.orderList = orderList;
        }

        @Override
        protected List<EsProOrderMonitorPo> compute() {
            List<EsProOrderMonitorPo> result = new ArrayList<EsProOrderMonitorPo>();
            if (CollectionUtils.isEmpty(orderList)) {
                return result;
            }

            int count = orderList.size();
            if (count == 1) {
                SimuFundCheckOrderPo vo = orderList.get(0);
                if (!BusinessCodeEnum.FUND_SHARE_TRANSFER_IN.getMCode().equals(vo.getmBusiCode())
                        && !BusinessCodeEnum.FUND_SHARE_TRANSFER_OUT.getMCode().equals(vo.getmBusiCode())) {
                    String sysCode = ProductTypeEsSysCodeMappingEnum.getSysCode(ProductClassEnum.HIGH.getCode(), vo.getFundType());
                    EsProOrderMonitorPo monitor = new EsProOrderMonitorPo();
                    monitor.setDealNo(vo.getDealNo());
                    monitor.setSysCode(sysCode);
                    monitor.setTxAcctNo(vo.getTxAcctNo());
                    result.add(monitor);
                    return result;
                }
            }

            int mid = count / 2;

            List<SimuFundCheckOrderPo> tasksOne = new ArrayList<SimuFundCheckOrderPo>();
            List<SimuFundCheckOrderPo> tasksTwo = new ArrayList<SimuFundCheckOrderPo>();
            tasksOne.addAll(orderList.subList(0, mid));
            tasksTwo.addAll(orderList.subList(mid, count));

            EsProOrderTask taskOne = new EsProOrderTask(tasksOne);
            EsProOrderTask taskTwo = new EsProOrderTask(tasksTwo);
            taskOne.fork();
            taskTwo.fork();

            List<EsProOrderMonitorPo> resultOne = taskOne.join();
            List<EsProOrderMonitorPo> resultTwo = taskTwo.join();
            if (CollectionUtils.isNotEmpty(resultOne)) {
                result.addAll(resultOne);
            }
            if (CollectionUtils.isNotEmpty(resultTwo)) {
                result.addAll(resultTwo);
            }

            return result;

        }
    }


}
