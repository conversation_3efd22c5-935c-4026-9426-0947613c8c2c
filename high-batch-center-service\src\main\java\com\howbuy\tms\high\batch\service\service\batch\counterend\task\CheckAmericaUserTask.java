package com.howbuy.tms.high.batch.service.service.batch.counterend.task;

import com.howbuy.acccenter.facade.query.queryacctrecheckforcounter.bean.AcCustIndiTaxQueryBean;
import com.howbuy.acccenter.facade.query.queryacctrecheckforcounter.bean.AcCustInstTaxQueryBean;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoResponse;
import com.howbuy.acccenter.facade.query.querycustinfo.bean.CustInfoBean;
import com.howbuy.acccenter.facade.query.sensitive.custinfo.QueryCustSensitiveInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.custinfo.bean.CustSensitiveInfo;
import com.howbuy.acccenter.facade.trade.opentxacctindiwithcounter.bean.TaxesInfo;
import com.howbuy.tms.common.enums.database.InvstTypeEnum;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfoanddiscustinfo.CustInfoBeanResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfoanddiscustinfo.DisCustInfoBeanResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.service.batch.counterend.CheckAmericaDealInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:校验美国用户
 * @Author: yun.lu
 * Date: 2024/7/5 10:56
 */
public class CheckAmericaUserTask extends AbstractHowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(CheckAmericaUserTask.class);
    private static List<String> codeList;
    private static List<String> nameList;

    static {
        codeList = new ArrayList<>();
        codeList.add("US");
        codeList.add("VI");
        codeList.add("UM");
        codeList.add("AS");
        nameList = new ArrayList<>();
        nameList.add("美国");
        nameList.add("美国本土外小岛屿");
        nameList.add("美属维尔京群岛");
        nameList.add("美属萨摩亚");
    }

    /**
     * 校验订单信息
     */
    private List<CheckAmericaDealInfo> checkAmericaDealInfoList;
    /**
     * 提示信息
     */
    private List<String> noticeMsgList;

    private QueryCustInfoOuterService queryCustInfoOuterService;

    private QueryAllCustInfoAndDisCustInfoOuterService queryAllCustInfoAndDisCustInfoOuterService;


    public CheckAmericaUserTask() {

    }

    public QueryAllCustInfoAndDisCustInfoOuterService getQueryAllCustInfoAndDisCustInfoOuterService() {
        return queryAllCustInfoAndDisCustInfoOuterService;
    }

    public void setQueryAllCustInfoAndDisCustInfoOuterService(QueryAllCustInfoAndDisCustInfoOuterService queryAllCustInfoAndDisCustInfoOuterService) {
        this.queryAllCustInfoAndDisCustInfoOuterService = queryAllCustInfoAndDisCustInfoOuterService;
    }

    public List<CheckAmericaDealInfo> getCheckAmericaDealInfoList() {
        return checkAmericaDealInfoList;
    }

    public void setCheckAmericaDealInfoList(List<CheckAmericaDealInfo> checkAmericaDealInfoList) {
        this.checkAmericaDealInfoList = checkAmericaDealInfoList;
    }

    @Override
    protected void callTask() {
        for (CheckAmericaDealInfo checkAmericaDealInfo : checkAmericaDealInfoList) {
            boolean isAmericaUser = getIsAmericaUser(checkAmericaDealInfo);
            if (isAmericaUser) {
                QueryCustSensitiveInfoResponse queryCustSensitiveInfoResponse = queryCustInfoOuterService.queryCustSensitiveInfo(checkAmericaDealInfo.getTxAcctNo());
                if (queryCustSensitiveInfoResponse == null || queryCustSensitiveInfoResponse.getCustSensitiveInfo() == null) {
                    log.error("根据交易账号查不到用户敏感信息,dealNo={},txAcctNo={}", checkAmericaDealInfo.getNoticeDealNo(), checkAmericaDealInfo.getTxAcctNo());
                    return;
                }
                CustSensitiveInfo custSensitiveInfo = queryCustSensitiveInfoResponse.getCustSensitiveInfo();
                String noticeMsg = "有美国客户订单，请确认，客户姓名【+" + custSensitiveInfo.getCustName() + "】、证件号码【" + custSensitiveInfo.getIdNo() + "】、购买产品名称【" + checkAmericaDealInfo.getFundName() + "】、购买产品代码【" + checkAmericaDealInfo.getFundCode() + "】、上报TA日期【" + checkAmericaDealInfo.getNoticeDt() + "】";
                noticeMsgList.add(noticeMsg);
            }
        }

    }

    private boolean getIsAmericaUser(CheckAmericaDealInfo checkAmericaDealInfo) {
        String txAcctNo = checkAmericaDealInfo.getTxAcctNo();
        String disCode = checkAmericaDealInfo.getDisCode();
        String noticeDealNo = checkAmericaDealInfo.getNoticeDealNo();
        QueryCustInfoResponse queryCustInfoResponse = queryCustInfoOuterService.queryCustomerInfo(txAcctNo, disCode);
        if (queryCustInfoResponse == null) {
            log.error("根据交易账号查不到用户信息,dealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
            return false;
        }
        // 1.用户税收信息
        CustInfoBean custInfoBean = queryCustInfoResponse.getCustInfoBean();
        if (custInfoBean == null) {
            log.error("根据交易账号查不到用户信息,dealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
            return false;
        }
        // 用户所有基础信息
        QueryAllCustInfoAndDisCustInfoResult queryAllCustInfoAndDisCustInfoResult = queryAllCustInfoAndDisCustInfoOuterService.queryAllCustInfoAndDisCustInfo(txAcctNo, disCode);
        if (queryAllCustInfoAndDisCustInfoResult == null) {
            log.error("根据交易账号查不到用户所有基本信息,dealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
            return false;
        }
        List<DisCustInfoBeanResult> disCustInfoList = queryAllCustInfoAndDisCustInfoResult.getDisCustInfoList();
        if (CollectionUtils.isEmpty(disCustInfoList)) {
            log.error("用户分销用户信息没有,dealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
            return false;
        }
        DisCustInfoBeanResult disCustInfoBeanResult = null;
        for (DisCustInfoBeanResult custInfoBeanResult : disCustInfoList) {
            if (disCode.equals(custInfoBeanResult.getDisCode())) {
                disCustInfoBeanResult = custInfoBeanResult;
            }
        }
        if (disCustInfoBeanResult == null) {
            log.error("用户没有对应分销用户信息,dealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
            return false;
        }

        CustInfoBeanResult custInfo = queryAllCustInfoAndDisCustInfoResult.getCustInfo();
        if (custInfo == null) {
            log.error("根据交易账号查不到用户所有基本信息,dealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
            return false;
        }
        if (InvstTypeEnum.INST.getCode().equals(custInfoBean.getInvstType()) || InvstTypeEnum.PRODUCT.getCode().equals(custInfoBean.getInvstType())) {
            // 2.机构/产品户
            // 2.1.机构客户的注册国家地区或现居国家地区；
            if (codeList.contains(custInfo.getRegCountry())) {
                log.info("机构/产品用户,注册国家属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            if (codeList.contains(disCustInfoBeanResult.getResidenceCountry())) {
                log.info("机构/产品用户,现居国家地区属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            // 2.2.机构涉税信息，居住地址或出生地址的中英文下的国家（包含控制人），或纳税居民国的国家（包含控制人）；
            AcCustInstTaxQueryBean acCustInstTaxQueryBean = queryCustInfoResponse.getAcCustInstTaxQueryBean();
            if (acCustInstTaxQueryBean == null) {
                log.info("机构/产品用户,查不到用户税收信息,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return false;
            }
            if (codeList.contains(acCustInstTaxQueryBean.getCtrlResidForeigCountry())) {
                log.info("机构/产品用户,控制人现居住地(英文)属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            if (nameList.contains(acCustInstTaxQueryBean.getCtrlTaxChinCountry()) || codeList.contains(acCustInstTaxQueryBean.getCtrlTaxChinCountry())) {
                log.info("机构/产品用户,控制人现居住地(中文)属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }

            if (codeList.contains(acCustInstTaxQueryBean.getCtrlForeigBrithCountry())) {
                log.info("机构/产品用户,控制人出生地-国家（英文地址）属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            if (nameList.contains(acCustInstTaxQueryBean.getCtrlChinBrithCountry()) || codeList.contains(acCustInstTaxQueryBean.getCtrlChinBrithCountry())) {
                log.info("机构/产品用户,控制人出生地-国家(中文)属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            if (nameList.contains(acCustInstTaxQueryBean.getInstTaxOrgCountry()) || codeList.contains(acCustInstTaxQueryBean.getInstTaxOrgCountry())) {
                log.info("机构/产品用户,机构地址-国家（中文）属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            if (codeList.contains(acCustInstTaxQueryBean.getInstForeigOrgCountry())) {
                log.info("机构/产品用户,机构地址-国家(英文地址)属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }

            List<TaxesInfo> instTaxesInfoList = acCustInstTaxQueryBean.getInstTaxesInfoList();
            if (CollectionUtils.isEmpty(instTaxesInfoList)) {
                log.info("机构/产品用户,查不到机构税收国信息,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return false;
            }
            for (TaxesInfo taxesInfo : instTaxesInfoList) {
                if (codeList.contains(taxesInfo.getTaxCountry())) {
                    log.info("机构/产品用户,机构纳税国别是美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                    return true;
                }
            }
            List<TaxesInfo> ctrlTaxesInfoList = acCustInstTaxQueryBean.getCtrlTaxesInfoList();
            if (CollectionUtils.isEmpty(ctrlTaxesInfoList)) {
                log.info("机构/产品用户,查不到控制人税收国信息,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return false;
            }
            for (TaxesInfo taxesInfo : ctrlTaxesInfoList) {
                if (codeList.contains(taxesInfo.getTaxCountry())) {
                    log.info("机构/产品用户,机构控制人纳税国别是美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                    return true;
                }
            }
        } else {
            // 3.个人用户
            // 3.1.个人客户的国籍或现居国家或地区；
            if (codeList.contains(custInfo.getNationality())) {
                log.info("是美国国籍用户,dealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            if (codeList.contains(disCustInfoBeanResult.getResidenceCountry())) {
                log.info("分销用户信息,现居国家或地区属于美国,dealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            // 3.2.个人涉税信息，居住地址或出生地址的中英文下的国家，或纳税居民国的国家
            AcCustIndiTaxQueryBean acCustIndiTaxQueryBean = queryCustInfoResponse.getAcCustIndiTaxQueryBean();
            if (acCustIndiTaxQueryBean == null) {
                log.info("个人用户,查不到用户税收信息,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return false;
            }
            if (codeList.contains(acCustIndiTaxQueryBean.getIndiTaxForeigCountry())) {
                log.info("个人用户,居住地国家(英文)属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            if (codeList.contains(acCustIndiTaxQueryBean.getIndiTaxChinCountry())) {
                log.info("个人用户,居住地国家(中文)属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            if (codeList.contains(acCustIndiTaxQueryBean.getIndiForeigBrithCountry())) {
                log.info("个人用户,出生地-国家（英文地址）属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            if (nameList.contains(acCustIndiTaxQueryBean.getIndiChinBrithCountry()) || codeList.contains(acCustIndiTaxQueryBean.getIndiChinBrithCountry())) {
                log.info("个人用户,出生地-国家(中文)属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return true;
            }
            List<TaxesInfo> indiTaxesInfoList = acCustIndiTaxQueryBean.getIndiTaxesInfoList();
            if (CollectionUtils.isEmpty(indiTaxesInfoList)) {
                log.info("个人用户,查不到用户税收国信息,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                return false;
            }
            for (TaxesInfo taxesInfo : indiTaxesInfoList) {
                if (codeList.contains(taxesInfo.getTaxCountry())) {
                    log.info("个人用户,纳税国别属于美国,noticeDealNo={},txAcctNo={}", noticeDealNo, txAcctNo);
                    return true;
                }
            }
        }
        return false;
    }

    public List<String> getNoticeMsgList() {
        return noticeMsgList;
    }

    public void setNoticeMsgList(List<String> noticeMsgList) {
        this.noticeMsgList = noticeMsgList;
    }

    public QueryCustInfoOuterService getQueryCustInfoOuterService() {
        return queryCustInfoOuterService;
    }

    public void setQueryCustInfoOuterService(QueryCustInfoOuterService queryCustInfoOuterService) {
        this.queryCustInfoOuterService = queryCustInfoOuterService;
    }
}
