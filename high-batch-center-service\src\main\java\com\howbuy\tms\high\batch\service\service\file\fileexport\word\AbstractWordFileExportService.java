package com.howbuy.tms.high.batch.service.service.file.fileexport.word;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.dfile.HOutputStream;
import com.howbuy.dfile.HTextFileWriter;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.batch.dao.po.batch.HighFundFileStatusPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.service.builder.HighFileStatusBuilder;
import com.howbuy.tms.high.batch.service.common.*;
import com.howbuy.tms.high.batch.service.repository.HighFundFileStatusRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.CommonConfService;
import com.howbuy.tms.high.batch.service.service.file.fileexport.word.bean.WordFileExportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * pdf文件导出基类
 * T 为html渲染的数据格式
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public abstract class AbstractWordFileExportService<T> extends CommonConfService {

    @Autowired
    private HighFundFileStatusRepository highFundFileStatusRepository;
    @Autowired
    private HighFileStatusBuilder highFileStatusBuilder;
    protected static final CacheService cacheService = CacheServiceImpl.getInstance();

    private static Pattern NUMBER_PATTERN = Pattern.compile("\\$\\{\\w+\\}");

    public String process(WordFileExportContext context) throws Exception {
        log.info("file export start, context={}", context);
        FileSdkPathInfo pdfFileSdkPathInfo = getWordFileSdkPathInfo(context);
        Map<String, Object> params = context.getParams();

        if (StringUtils.isEmpty((String) params.get(HighBatchConstants.TA_TRADE_DT))) {
            params.put(HighBatchConstants.TA_TRADE_DT, DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD));
        }

        // 文件绝对路径
        String absolutePath = FileSdkUtil.getAbsolutePath(pdfFileSdkPathInfo);


        // 判断文件导出记录是否存在，若为处理中，则抛出异常，否则更新为处理中;若不存在，则创建记录
        String recordNo = updateFileSatusProcessing(pdfFileSdkPathInfo.getBusinessCode(), params, absolutePath);

        // 删除文件fileName
        if (FileSdkUtil.exists(pdfFileSdkPathInfo)) {
            FileSdkUtil.deleteFile(pdfFileSdkPathInfo);
        } else {
            FileSdkUtil.mkdir(pdfFileSdkPathInfo);
        }
        try {
            // 将具体的html文件，转换word文件
            createWordFileV2(context, recordNo);
            // 生成文件后处理
            doAfterCreate(context);
        } catch (Exception e) {
            log.error("create file{} failed.", absolutePath, e);
            // 更新文件处理状态为失败
            highFundFileStatusRepository.updateFileOpStatus(recordNo, HighFileOpStatusEnum.GENERATE_FAIL.getCode());
            // 告警
            String msg = "导出word文件失败,businessCode:" + pdfFileSdkPathInfo.getBusinessCode() + "middlePath:" + pdfFileSdkPathInfo.getMiddlePath() + "fileName:" + pdfFileSdkPathInfo.getFileName();
            OpsMonitor.warn(msg, OpsMonitor.ERROR);
            throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR, "导出word文件失败");
        }

        // 更新文件处理状态为成功
        highFundFileStatusRepository.updateFileOpStatus(recordNo, HighFileOpStatusEnum.GENERATE_SUCCESS.getCode());
        log.info("word文件{}，生成成功", absolutePath);
        return absolutePath;
    }

    public void doAfterCreate(WordFileExportContext context) throws Exception {
        log.info("生成好不需要处理");
    }

    /**
     * 获取htm渲染数据
     *
     * @param params
     * @return
     */
    public abstract Map<String, String> getHtmlData(Map<String, Object> params);


    /**
     * 获取pdf文件sdk路径信息
     *
     * @return
     */
    public abstract FileSdkPathInfo getWordFileSdkPathInfo(WordFileExportContext context);


    /**
     * 生成word文件
     */
    private void createWordFileV2(WordFileExportContext context, String recordNo) throws Exception {
        FileSdkPathInfo wordFileSdkPathInfo = getWordFileSdkPathInfo(context);
        HOutputStream os = null;
        ByteArrayInputStream baas = null;
        try {
            os = FileSdkUtil.buildHOutputStream(wordFileSdkPathInfo);
            byte[] b = getFileData(context, recordNo);
            baas = new ByteArrayInputStream(b);
            //生成word
            POIFSFileSystem poifs = new POIFSFileSystem();
            DirectoryEntry directory = poifs.getRoot();
            directory.createDocument("exportWord", baas);
            //输出文件
            poifs.writeFilesystem(os.getOutputStream());
        } catch (Exception e) {
            log.error("生成html error ", e);
            throw e;
        } finally {
            if (baas != null) {
                baas.close();
            }
            if (os != null) {
                os.flush();
                os.close();
            }
        }
    }


    /**
     * 正则表达式生成html文件
     *
     * @param context
     * @return
     * @throws Exception
     */
    private byte[] createByHtmlFile(WordFileExportContext context) throws Exception {
        InputStreamReader reader = null;
        HTextFileWriter bWriter = null;
        String templateName = getTemplateName(context);
        Map<String, String> htmlData = getHtmlData(context.getParams());
        FileSdkPathInfo htmlFileSdkPathInfo = getWordFileSdkPathInfo(context);
        htmlFileSdkPathInfo.setFileName(templateName + "_word.html");
        try {
            InputStream inputStream = AbstractWordFileExportService.class.getClassLoader().getResourceAsStream("templates/" + templateName + ".ftl");
            if (inputStream == null) {
                log.error("找不到模板文件：{}", templateName);
                return null;
            }
            reader = new InputStreamReader(inputStream, context.getEncode() == null ? "UTF-8" : context.getEncode());
            bWriter = FileSdkUtil.buildHTextFileWriter(htmlFileSdkPathInfo);
            String temp = "";
            StringBuffer sb = new StringBuffer();
            BufferedReader bReader = new BufferedReader(reader);
            while ((temp = bReader.readLine()) != null) {
                Matcher m = NUMBER_PATTERN.matcher(temp);
                sb.setLength(0);
                while (m.find()) {
                    String paramTmp = m.group();
                    String param = paramTmp.substring(2, paramTmp.length() - 1);
                    String value = htmlData.get(param);
                    m.appendReplacement(sb, value == null ? "" : value);
                }
                m.appendTail(sb);
                bWriter.write(sb.toString());
                bWriter.newLine();
            }
        } catch (Exception e) {
            log.error("生成html error ", e);
            throw e;
        } finally {
            if (bWriter != null) {
                bWriter.flush();
                bWriter.close();
            }
            if (reader != null) {
                reader.close();
            }
        }
        if (FileSdkUtil.exists(htmlFileSdkPathInfo)) {
            byte[] bytes = FileSdkUtil.read2Bytes(htmlFileSdkPathInfo);
            FileSdkUtil.deleteFile(htmlFileSdkPathInfo);
            return bytes;
        }
        return null;
    }


    /**
     * 获取模版数据
     */
    private byte[] getFileData(WordFileExportContext context, String recordNo) throws Exception {
        byte[] htmlBytes;
        if (YesOrNoEnum.YES.getCode().equals(context.getNeedCreateTemplateHtml())) {
            htmlBytes = createByHtmlFile(context);
        } else {
            htmlBytes = this.getHtmlPageAsByteArray(context, recordNo);
        }
        if (htmlBytes != null) {
            System.out.println("成功获取 HTML 页面的字节数组，长度: " + htmlBytes.length);
        } else {
            System.out.println("获取 HTML 页面的字节数组失败");
        }
        return htmlBytes;
    }

    public byte[] getHtmlPageAsByteArray(WordFileExportContext context, String recordNo) throws URISyntaxException {
        FileSdkPathInfo wordFileSdkPathInfo = getWordFileSdkPathInfo(context);
        String dataKey = wordFileSdkPathInfo.getBusinessCode() + recordNo + RandomUtils.getRamdomNumber(4);
        Map<String, String> htmlData = getHtmlData(context.getParams());
        cacheService.put(3600, HighCacheKeyPrefix.HIGH_CACHE_KEY_PREFIX + dataKey, htmlData);
        HttpClient httpClient = HttpClients.createDefault();
        URIBuilder uriBuilder = new URIBuilder( this.getHighBatchIp() + "/highBatch/queryTemplateHtml.htm");
        uriBuilder.addParameter("dataKey", dataKey);
        uriBuilder.addParameter("templateName", getTemplateName(context));
        HttpGet httpget = new HttpGet(uriBuilder.build().toString());
        log.info("word-getHtmlTemplateWithDataUrl-获取html模版映射内容url:{}", "http://" + this.getHighBatchIp() + ":8080" + "/highBatch/queryTemplateHtml.htm?dataKey=" + dataKey + "&templateName=" + getTemplateName(context));
        try {
            // 执行请求
            HttpResponse response = httpClient.execute(httpget);
            // 获取响应实体
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // 将实体内容转换为字节数组
                return EntityUtils.toByteArray(entity);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 获取模版名
     */
    public abstract String getTemplateName(WordFileExportContext context);

    /**
     * 生成word文件
     *
     * @param htmlFile
     * @return
     */
    private void createWordFileV1(WordFileExportContext context, FileSdkPathInfo htmlFile) throws Exception {
        FileSdkPathInfo pdfFileSdkPathInfo = getWordFileSdkPathInfo(context);
        HOutputStream os = null;
        ByteArrayInputStream baas = null;
        try {
            os = FileSdkUtil.buildHOutputStream(pdfFileSdkPathInfo);
            byte[] b = FileUtil.readBytes(FileSdkUtil.getAbsolutePath(htmlFile));
            baas = new ByteArrayInputStream(b);
            //生成word
            POIFSFileSystem poifs = new POIFSFileSystem();
            DirectoryEntry directory = poifs.getRoot();
            directory.createDocument("exportWord", baas);
            //输出文件
            poifs.writeFilesystem(os.getOutputStream());
        } catch (Exception e) {
            log.error("生成html error ", e);
            throw e;
        } finally {
            if (baas != null) {
                baas.close();
            }
            if (os != null) {
                os.flush();
                os.close();
            }
        }
    }


    /**
     * 更新文件处理状态为处理中
     *
     * @param businessCode
     * @param params
     * @param absolutePath
     */
    private String updateFileSatusProcessing(String businessCode, Map<String, Object> params, String absolutePath) {
        HighFundFileStatusPo highFundFileStatusPo = highFundFileStatusRepository.selectByTradeDateAndFileType(params.get("taTradeDt").toString(), businessCode, absolutePath);
        if (highFundFileStatusPo == null) {
            highFundFileStatusPo = highFileStatusBuilder.createHighFileStatus(params.get("taTradeDt").toString(), businessCode, absolutePath, HighFileOptionEnum.GENERATE.getCode());
            highFundFileStatusRepository.insertSelective(highFundFileStatusPo);
        } else {
            if (Objects.equals(highFundFileStatusPo.getFileOpStatus(), HighFileOpStatusEnum.PROCESSING.getCode())) {
                throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR, "文件正在生成中，请稍后再试");
            }
            highFundFileStatusPo.setFileOpStatus(HighFileOpStatusEnum.PROCESSING.getCode());
            highFundFileStatusPo.setUpdateDtm(new Date());
            highFundFileStatusRepository.updateByPrimaryKeySelective(highFundFileStatusPo);
        }
        return highFundFileStatusPo.getRecordNo();
    }

}
