/**
 * Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.batch.service.business.syncmodifylockdt;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.common.enums.database.WorkdayTypeEnum;
import com.howbuy.tms.high.batch.dao.po.batch.HighLockDtChgLogPo;
import com.howbuy.tms.high.batch.dao.po.batch.WorkdayPo;
import com.howbuy.tms.high.batch.dao.po.order.SubCustBooksPo;
import com.howbuy.tms.high.batch.service.business.calmodifyvol.CalModifyVolService;
import com.howbuy.tms.high.batch.service.repository.HighLockDtChgLogRepository;
import com.howbuy.tms.high.batch.service.repository.SubCustBooksRepository;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 *
 * @description:份额锁定期修改记录到表中
 *
 * @reason:
 * <AUTHOR>
 * @date 2018年6月11日 上午10:12:04
 * @since JDK 1.6
 */
@Service("syncModifyLockDtChgLogService")
public class SyncModifyLockDtChgLogService {

    Logger logger = LoggerFactory.getLogger(SyncModifyLockDtChgLogService.class);

    @Autowired
    private HighLockDtChgLogRepository highLockDtChgLogRepository;

    @Autowired
    private SyncModifyLockDtService syncModifyLockDtService;

    @Autowired
    private SubCustBooksRepository subCustBooksRepository;

    @Autowired
    private CalModifyVolService calModifyVolService;

    @Autowired
    private WorkdayService workdayService;

    /**
     *
     * process:修改锁定期处理
     *
     * @param bean
     * <AUTHOR>
     * @date 2018年6月11日 上午10:13:06
     */
    public void process(SyncModifyLockDtBean bean) {
        logger.info("SyncModifyLockDtService|process|start");
        logger.info("SyncModifyLockDtService|process|SyncModifyLockDtBean:{}", JSON.toJSONString(bean));
        // 消息体校验
        if (StringUtils.isEmpty(bean.getBlotterNo()) || StringUtils.isEmpty(bean.getOldLockEndDt())
                || StringUtils.isEmpty(bean.getNewLockEndDt()) || StringUtils.isEmpty(bean.getIsBalDtlCheckFileSync())
                || StringUtils.isEmpty(bean.getCustNo())) {
            logger.info("SyncModifyLockDtService|process|parmas exsits null|SyncModifyLockDtBean:{}", JSON.toJSONString(bean));
            return;
        }

        // 份额明细文件未同步到中台
        if (YesOrNoEnum.NO.getCode().equals(bean.getIsBalDtlCheckFileSync())) {
            // 需要更新子账本
            processLockDt(bean, true);
        } else {
            if (isTaAckEnd(bean.getBlotterNo())) {
                // 需要更新子账本
                processLockDt(bean, true);
            } else {
                // 只记录到表中，暂不更新子账本
                processLockDt(bean, false);
            }
        }
        logger.info("SyncModifyLockDtService|process|end");
    }

    /**
     * TA确认处理日终执行完
     * @param extVolDtlNo
     * @return
     */
    private boolean isTaAckEnd(String extVolDtlNo) {

        List<SubCustBooksPo> subCutBooksPos = subCustBooksRepository.getSubCustBooksByExtVolDtlNos(Lists.newArrayList(extVolDtlNo));
        if (CollectionUtils.isEmpty(subCutBooksPos)) {
            return false;
        }

        SubCustBooksPo subCustBooksPo = subCutBooksPos.get(0);
        if (subCustBooksPo == null) {
            return false;
        }

        WorkdayPo workday = workdayService.getWorkay(WorkdayTypeEnum.SYS_TYPE);
        if (workday == null) {
            return false;
        }

        boolean taAckEnd = calModifyVolService.isTaAckEnd(subCustBooksPo.getTaCode(), workday.getWorkday());
        return taAckEnd;
    }


    public void processLockDt(SyncModifyLockDtBean bean, boolean updateLockDtFlag) {
        logger.info("SyncModifyLockDtService|processLockDt|bean:{},updateLockDtFlag:{}", JSON.toJSONString(bean), updateLockDtFlag);
        HighLockDtChgLogPo po = buildHighLockDtChgLog(bean, updateLockDtFlag);
        highLockDtChgLogRepository.insertSelective(po);
        if (!updateLockDtFlag) {
            return;
        }
        // 更新份额锁定期sub_cust_books
        syncModifyLockDtService.process(po);
    }

    /**
     *
     * buildHighLockDtChgLog:构建锁定期记录
     *
     * @param bean
     * @param updateLockDtFlag
     * @return
     * <AUTHOR>
     * @date 2018年6月11日 上午11:13:29
     */
    private HighLockDtChgLogPo buildHighLockDtChgLog(SyncModifyLockDtBean bean, boolean updateLockDtFlag) {
        Date date = new Date();
        HighLockDtChgLogPo po = new HighLockDtChgLogPo();
        po.setExtVolDtlNo(bean.getBlotterNo());
        po.setTxAcctNo(bean.getCustNo());
        po.setOldOpenRedeDt(bean.getOldLockEndDt());
        po.setNewOpenRedeDt(bean.getNewLockEndDt());
        po.setIsBalDtlCheckFileSync(bean.getIsBalDtlCheckFileSync());
        if (updateLockDtFlag) {
            po.setProcessStatus(ProcessStatusEnum.PROCESSING.getCode());
        } else {
            po.setProcessStatus(ProcessStatusEnum.NOT_PROCESS.getCode());
        }
        po.setCreateDate(date);
        po.setUpdateDate(date);
        return po;
    }
}

