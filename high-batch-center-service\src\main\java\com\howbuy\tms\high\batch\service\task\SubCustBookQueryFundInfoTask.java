package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * @Description:querySubCustBooksFacade中查询产品信息
 * @Author: yun.lu
 * Date: 2025/4/9 17:54
 */
@Data
public class SubCustBookQueryFundInfoTask extends AbstractHowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(SubCustBookQueryFundInfoTask.class);

    /**
     * 每次查询量
     */
    private static int maxQrySize = 50;
    private Map<String, String> fundMap;
    private String productChannel;
    private QueryHighProductOuterService queryHighProductOuterService;

    @Override
    protected void callTask() {
        List<HighProductBaseInfoBean> list = null;
        for (int k = 1; ; k++) {
            list = queryHighProductOuterService.getHighProductBaseInfoList(productChannel, k, maxQrySize);
            logger.info("querySubCustBooksFacade中查询产品信息,pageNum:{}, pageSize:{}", k, list == null ? 0 : list.size());
            if (CollectionUtils.isEmpty(list)) {
                logger.warn("querySubCustBooksFacade中查询产品信息,highProductBaseInfoBeanList is empty, k:{}", k);
                break;
            }
            for (HighProductBaseInfoBean bean : list) {
                fundMap.put(bean.getFundCode(), bean.getFundAttr());
            }
        }
    }

    public SubCustBookQueryFundInfoTask(Map<String, String> fundMap, String productChannel, QueryHighProductOuterService queryHighProductOuterService) {
        this.fundMap = fundMap;
        this.productChannel = productChannel;
        this.queryHighProductOuterService = queryHighProductOuterService;
    }
}
