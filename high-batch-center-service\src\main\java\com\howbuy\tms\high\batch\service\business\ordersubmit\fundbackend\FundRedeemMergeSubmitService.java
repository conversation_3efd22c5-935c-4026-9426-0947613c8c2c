/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.outerservice.fbsonline.fundredeem.FundRedeemContext;
import com.howbuy.tms.common.outerservice.fbsonline.fundredeemmerge.FundRedeemMergeContext;
import com.howbuy.tms.common.outerservice.fbsonline.fundredeemmerge.FundRedeemMergeOuterService;
import com.howbuy.tms.common.outerservice.fbsonline.fundredeemmerge.FundRedeemMergeResult;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.service.business.ordersubmit.BaseResponse;
import com.howbuy.tms.high.batch.service.business.ordersubmit.CheckOrderMergeSubmitService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 赎回合并上报服务
 * <AUTHOR>
 * @date 2021/3/12 10:42
 * @since JDK 1.8
 */
@Service("fundRedeemMergeSubmitService")
public class FundRedeemMergeSubmitService extends CheckOrderMergeSubmitService {

    private static Logger logger = LogManager.getLogger(FundRedeemMergeSubmitService.class);

    @Autowired
    private FundRedeemMergeOuterService fundRedeemMergeOuterService;

    @Override
    protected BaseResponse doSubmit(List<SimuFundCheckOrderManualSubmitVo> checkOrderList) {
        FundRedeemMergeContext context = generateContext(checkOrderList);
        FundRedeemMergeResult result = fundRedeemMergeOuterService.doFundRedeem(context);
        logger.info("FundRedeemMergeSubmitService|result:" + JSON.toJSONString(result));
        BaseResponse baseResponse = new BaseResponse();
        BeanUtils.copyProperties(result, baseResponse);
        baseResponse.setMiddleDealNoToContractNoMap(result.getDealNoMapping());
        return baseResponse;
    }

    private FundRedeemMergeContext generateContext(List<SimuFundCheckOrderManualSubmitVo> checkOrderList) {
        FundRedeemMergeContext mergeContext = new FundRedeemMergeContext();
        // 合并赎回主订单号
        mergeContext.setMainDealOrderNo(checkOrderList.get(0).getMainDealOrderNo());

        // 合并赎回订单列表
        List<FundRedeemContext> orderList = new ArrayList<>(checkOrderList.size());
        for (SimuFundCheckOrderManualSubmitVo order : checkOrderList) {
            FundRedeemContext context = FundRedeemSubmitServiceAbstract.generateContext(order);
            orderList.add(context);
        }
        mergeContext.setOrderList(orderList);

        return mergeContext;
    }

}