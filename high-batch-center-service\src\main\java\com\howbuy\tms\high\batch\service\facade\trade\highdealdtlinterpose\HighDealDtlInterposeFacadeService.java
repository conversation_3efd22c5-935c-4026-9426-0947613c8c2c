/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.trade.highdealdtlinterpose;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.constant.OutReturnCodes;
import com.howbuy.tms.common.enums.SysCodeToProductChannelEnum;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.InterposeFlagEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.enums.database.PaymentTypeEnum;
import com.howbuy.tms.common.enums.database.PmtdtInterposeFlagEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;
import com.howbuy.tms.common.enums.database.TxPmtFlagEnum;
import com.howbuy.tms.common.outerservice.finonline.updatesubmittadt.UpdateFinaSubmitTaDtContext;
import com.howbuy.tms.common.outerservice.finonline.updatesubmittadt.UpdateFinaSubmitTaDtOuterService;
import com.howbuy.tms.common.outerservice.finonline.updatesubmittadt.UpdateFinaSubmitTaDtResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductAppointmentInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.outerservice.payonline.proddt.ChangeSelfTransProdDtContext;
import com.howbuy.tms.common.outerservice.payonline.proddt.ChangeSelfTransProdDtResult;
import com.howbuy.tms.common.outerservice.payonline.proddt.ChangeSelfTransProdDtService;
import com.howbuy.tms.common.utils.ConvertCodeUtils;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.batch.BusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.PaymentOrderPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.BusinessProcessingStepEnum;
import com.howbuy.tms.high.batch.facade.trade.highdealdtlinterpose.HighDealDtlInterposeFacade;
import com.howbuy.tms.high.batch.facade.trade.highdealdtlinterpose.HighDealDtlInterposeRequest;
import com.howbuy.tms.high.batch.facade.trade.highdealdtlinterpose.HighDealDtlInterposeResponse;
import com.howbuy.tms.high.batch.service.business.message.MsgNotifySendService;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.repository.BusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.PaymentOrderRepository;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import com.howbuy.tms.high.batch.service.service.order.highdealorderdtl.HighDealOrderDtlService;
import com.howbuy.tms.high.orders.facade.trade.validCustomerInfoStrongly.ValidCustomerInfoStronglyFacade;
import com.howbuy.tms.high.orders.facade.trade.validCustomerInfoStrongly.ValidCustomerInfoStronglyRequest;
import com.howbuy.tms.high.orders.facade.trade.validCustomerInfoStrongly.ValidCustomerInfoStronglyResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:人工干预
 * @reason:
 * @date 2018年6月8日 上午9:31:06
 * @since JDK 1.6
 */
@DubboService
@Service("highDealDtlInterposeFacade")
public class HighDealDtlInterposeFacadeService implements HighDealDtlInterposeFacade {
    private static Logger logger = LogManager.getLogger(HighDealDtlInterposeFacadeService.class);
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private PaymentOrderRepository paymentOrderRepository;
    @Autowired
    private WorkdayService workdayService;
    @Autowired
    private HighDealOrderDtlService highDealOrderDtlService;
    @Autowired
    private BusinessBatchFlowRepository businessBatchFlowRepository;
    @Autowired
    private UpdateFinaSubmitTaDtOuterService updateFinaSubmitTaDtOuterService;
    @Autowired
    private ChangeSelfTransProdDtService changeSelfTransProdDtService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private ValidCustomerInfoStronglyFacade validCustomerInfoStronglyFacade;

    @Value("${retrieve.period.Interpose}")
    private String retrievePeriodInterpose;

    /**
     * 网点代码
     */
    private static final String OUTLET_CODE = "W20170215";

    @Autowired
    private MsgNotifySendService msgNotifySendService;
    @Autowired
    private TradeDayService tradeDayService;

    @Override
    public HighDealDtlInterposeResponse execute(HighDealDtlInterposeRequest request) {
        logger.info("干预上报-start,request:{}", JSON.toJSONString(request));
        HighDealDtlInterposeResponse resp = new HighDealDtlInterposeResponse();
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        resp.setDescription("成功");

        List<String> dealNos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getDealNos())) {
            dealNos.addAll(request.getDealNos());
        } else {
            dealNos.add(request.getDealNo());
        }

        for (String dealNo : dealNos) {
            HighDealOrderDtlPo po = getHighDealOrderDtlPo(dealNo);
            List<HighDealOrderDtlPo> orderList;
            if (YesOrNoEnum.YES.getCode().equals(po.getMergeSubmitFlag())) {
                orderList = getMergeSubmitOrders(po.getMainDealOrderNo());
            } else {
                orderList = new ArrayList<>(1);
                orderList.add(po);
            }
            logger.info("干预上报相关订单,orderList:{}", JSON.toJSONString(orderList));
            // 上报日期如果是空则只修改合规，不为空则认为是修改上报日
            if (StringUtils.isEmpty(request.getSubmitTaDt())) {
                processCompliance(orderList, request);
            } else {
                processSubmitDt(orderList, request, resp);

                // 通知外部系统，订单状态变化信息
                msgNotifySendService.sendHighActualMessage(dealNo);
            }
        }

        logger.info("HighDealDtlInterposeFacadeService|execute|end");
        return resp;
    }

    /**
     * 处理合规
     *
     * @param orderList
     * @param request
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/4/9 9:41
     * @since JDK 1.8
     */
    private void processCompliance(List<HighDealOrderDtlPo> orderList, HighDealDtlInterposeRequest request) {
        List<HighDealOrderDtlPo> newPoList = new ArrayList<>(orderList.size());
        orderList.forEach(order -> newPoList.add(createNewCompliancePo(order, request)));
        int count = updateDtl(newPoList, new Date(), request.getDealNo());
        if (count < 1) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_INTERPOSE_ERROR, "中台干预合规失败");
        }
    }

    /**
     * 创建新合规订单对象
     *
     * @param po
     * @param request
     * @return com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo
     * @author: huaqiang.liu
     * @date: 2021/6/2 10:21
     * @since JDK 1.8
     */
    private HighDealOrderDtlPo createNewCompliancePo(HighDealOrderDtlPo po, HighDealDtlInterposeRequest request) {
        HighDealOrderDtlPo newPo = new HighDealOrderDtlPo();
        newPo.setDealNo(po.getDealNo());
        newPo.setDealDtlNo(po.getDealDtlNo());
        newPo.setNotifySubmitFlag(po.getNotifySubmitFlag());
        if (BusinessCodeEnum.SUBS.getMCode().equals(po.getmBusiCode()) || BusinessCodeEnum.PURCHASE.getMCode().equals(po.getmBusiCode())) {
            if (StringUtils.isNotEmpty(request.getDualentryInterposeFlag()) && StringUtils.isNotEmpty(po.getDualentryInterposeFlag())
                    && !po.getDualentryInterposeFlag().equals(request.getDualentryInterposeFlag())) {
                newPo.setDualentryInterposeFlag(request.getDualentryInterposeFlag());
            }
            if (StringUtils.isNotEmpty(request.getCallbackInterposeFlag()) && StringUtils.isNotEmpty(po.getCallbackInterposeFlag())
                    && !po.getCallbackInterposeFlag().equals(request.getCallbackInterposeFlag())) {
                newPo.setCallbackInterposeFlag(request.getCallbackInterposeFlag());
            }
            if (StringUtils.isNotEmpty(request.getCalmdtmInterposeFlag()) && StringUtils.isNotEmpty(po.getCalmdtmInterposeFlag())
                    && !po.getCalmdtmInterposeFlag().equals(request.getCalmdtmInterposeFlag())) {
                newPo.setCalmdtmInterposeFlag(request.getCalmdtmInterposeFlag());
            }
            if (StringUtils.isNotEmpty(request.getAssetInterposeFlag()) && StringUtils.isNotEmpty(po.getAssetInterposeFlag())
                    && !po.getAssetInterposeFlag().equals(request.getAssetInterposeFlag())) {
                newPo.setAssetInterposeFlag(request.getAssetInterposeFlag());
            }
            // 设置检索时间
            newPo.setRetrieveDtm(DateUtils.addDateByType(new Date(), Calendar.MINUTE, Integer.parseInt(retrievePeriodInterpose)));
        }
        return newPo;
    }

    /**
     * 处理上报日
     *
     * @param orderList
     * @param request
     * @param resp
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/4/9 9:41
     * @since JDK 1.8
     */
    private void processSubmitDt(List<HighDealOrderDtlPo> orderList, HighDealDtlInterposeRequest request, HighDealDtlInterposeResponse resp) {
        logger.info("processSubmitDt-request={}", request);
        // 1.进行业务校验
        check(request, orderList);
        // 2.进行上报
        List<HighDealOrderDtlPo> newOrderList = new ArrayList<>(orderList.size());
        Map<String, HighDealOrderDtlPo> oldOrderMap = new HashMap<>(orderList.size());
        Map<String, String> oldSubmitDtMap = new HashMap<>(orderList.size());
        for (HighDealOrderDtlPo order : orderList) {
            oldSubmitDtMap.put(order.getDealNo(), order.getSubmitTaDt());
            oldOrderMap.put(order.getDealNo(), order);

            HighDealOrderDtlPo newOrder = new HighDealOrderDtlPo();
            newOrderList.add(newOrder);
            newOrder.setDealNo(order.getDealNo());
            newOrder.setDealDtlNo(order.getDealDtlNo());
            newOrder.setNotifySubmitFlag(order.getNotifySubmitFlag());
            // 设置最新上报日
            newOrder.setSubmitTaDt(request.getSubmitTaDt());
            newOrder.setSubmitTaDtInterposeFlag(InterposeFlagEnum.DONE.getCode());
        }
        logger.info("HighDealDtlInterposeFacadeService|execute|newOrder:{},oldSubmitTaDt:{}", JSON.toJSONString(newOrderList), JSON.toJSONString(oldSubmitDtMap));
        int count = updateDtl(newOrderList, new Date(), oldSubmitDtMap);
        logger.info("HighDealDtlInterposeFacadeService|execute|count:{}", count);
        if (count < 1) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_INTERPOSE_ERROR, "中台干预上报日失败");
        }
        resp.setDescription("中台更新成功！");

        // 处理支付日期
        processPayment(newOrderList, oldOrderMap, resp);
    }

    private void check(HighDealDtlInterposeRequest request, List<HighDealOrderDtlPo> orderList) {
        String sysCode = SysCodeToProductChannelEnum.getByProductChannel(request.getProductChannel()).getSysCode();
        String workDay = workdayService.getSaleSysCurrWorkay();
        logger.info("HighDealDtlInterposeFacadeService|execute|workDay:{}", workDay);
        // 上报日期不能小于当前工作日
        if (workDay.compareTo(request.getSubmitTaDt()) > 0) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_INTERPOSE_ERROR, "上报日期不能小于当前工作日");
        }
        // 日期如果不是工作日,不允许提交
        boolean isWorkDay = tradeDayService.isTradeDayByCache(DateUtils.formatToDate(request.getSubmitTaDt(), DateUtils.YYYYMMDD));
        if (!isWorkDay) {
            logger.info("HighDealDtlInterposeFacadeService-check,上报日期不是工作日,submitTaDt={}", request.getSubmitTaDt());
            throw new BatchException(ExceptionCodes.HIGH_BATCH_INTERPOSE_ERROR, "上报日期不是工作日");
        }
        validCustomerStrongly(orderList);

        checkCanSubmit(orderList, request.getSubmitTaDt());
        // 当日交易申请日终完成，不能干预到今日
        if (workDay.equals(request.getSubmitTaDt())) {
            BusinessBatchFlowPo flow = businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(BusinessProcessingStepEnum.BPS_TRADE_APP_DAY_END.getCode(),
                    workDay, sysCode);
            logger.info("HighDealDtlInterposeFacadeService|execute|flow:{}", JSON.toJSONString(flow));
            if (flow == null) {
                throw new BatchException(ExceptionCodes.BATCH_CENTER_BATCH_FLOW_ERROR, "节点不存在");
            }
            if (BatchStatEnum.PROCESS_SUCCESS.getKey().equals(flow.getBatchStat())) {
                throw new BatchException(ExceptionCodes.HIGH_BATCH_INTERPOSE_ERROR, "当日交易申请日终完成，不能干预到今日");
            }
        }
    }

    /**
     * 反洗钱强控校验
     *
     * @param orderList
     */
    private void validCustomerStrongly(List<HighDealOrderDtlPo> orderList) {
        // 反洗钱强控
        for (HighDealOrderDtlPo highDealOrderDtlPo : orderList) {
            ValidCustomerInfoStronglyRequest validCustomerInfoStronglyRequest = new ValidCustomerInfoStronglyRequest();
            validCustomerInfoStronglyRequest.setTxChannel(TxChannelEnum.COUNTER.getCode());
            validCustomerInfoStronglyRequest.setOperIp("high-batch");
            validCustomerInfoStronglyRequest.setDisCode(highDealOrderDtlPo.getDisCode());
            validCustomerInfoStronglyRequest.setExternalDealNo("externalDealNo");
            validCustomerInfoStronglyRequest.setOutletCode(OUTLET_CODE);
            validCustomerInfoStronglyRequest.setCheckByTa(YesOrNoEnum.YES.getCode());
            validCustomerInfoStronglyRequest.setTxAcctNo(highDealOrderDtlPo.getTxAcctNo());
            validCustomerInfoStronglyRequest.setTaCode(highDealOrderDtlPo.getTaCode());
            ValidCustomerInfoStronglyResponse response = validCustomerInfoStronglyFacade.execute(validCustomerInfoStronglyRequest);
            if (response != null && YesOrNoEnum.NO.getCode().equals(response.getValidPass())) {
                throw new BatchException(ExceptionCodes.HIGH_BATCH_INTERPOSE_ERROR, highDealOrderDtlPo.getDealNo() + "用户:" + highDealOrderDtlPo.getTxAcctNo() + "反洗钱校验不通过" + response.getErrorMsg());
            }
        }
    }

    /**
     * 校验是否可以提交
     */
    private void checkCanSubmit(List<HighDealOrderDtlPo> orderList, String submitTaDt) {
        // 1.查询商品基础信息
        List<String> fundCodeList = orderList.stream().map(HighDealOrderDtlPo::getFundCode).distinct().collect(Collectors.toList());
        List<HighProductBaseInfoBean> highProductBaseInfoList = queryHighProductOuterService.getHighProductBaseInfoList(fundCodeList);
        Map<String, HighProductBaseInfoBean> productBaseInfoBeanMap = highProductBaseInfoList.stream().collect(Collectors.toMap(HighProductBaseInfoBean::getFundCode, x -> x));
        // 2.查询商品状态
        List<HighProductStatInfoBean> highProductStatInfoBeanList = queryHighProductOuterService.getBatchProductStatInfo(fundCodeList, submitTaDt);
        Map<String, HighProductStatInfoBean> productStatInfoBeanMap = highProductStatInfoBeanList.stream().collect(Collectors.toMap(x -> String.join(Constant.UNDER_LINE, x.getFundCode(), x.getShareClass()), x -> x));
        for (HighDealOrderDtlPo highDealOrderDtlPo : orderList) {
            // 订单产品信息
            HighProductBaseInfoBean highProductBaseInfoBean = productBaseInfoBeanMap.get(highDealOrderDtlPo.getFundCode());
            // 2.1.该笔订单的产品支持预约交易()，日期不是该产品该业务的开放日，不允许提交
            if (isScheduledTrade(highDealOrderDtlPo.getmBusiCode(), highProductBaseInfoBean)) {
                // 查询商品的开放日
                HighProductAppointmentInfoBean appointmentByAppointByOpenDt = queryHighProductOuterService.getAppointmentByAppointByOpenDt(highDealOrderDtlPo.getFundCode(), highDealOrderDtlPo.getmBusiCode(), submitTaDt);
                if (appointmentByAppointByOpenDt == null) {
                    logger.info("HighDealDtlInterposeFacadeService-checkCanSubmit,该笔订单产品支持预约交易,但不在开放日,fundCode={},mbusiCode={},submitTaDt={}", highDealOrderDtlPo.getFundCode(), highDealOrderDtlPo.getmBusiCode(), submitTaDt);
                    throw new BatchException(ExceptionCodes.BATCH_CENTER_BATCH_FLOW_ERROR, "该笔订单产品支持预约交易,但不在开放日");
                }

            } else {
                // 2.2.该笔订单的产品不支持预约交易，该日期下该产品的净值交易状态不可以交易（根据订单业务类型查对应的业务是否支持），不允许提交
                // 只有认申购,赎回,才去确定交易状态
                HighProductStatInfoBean highProductStatInfoBean = productStatInfoBeanMap.get(String.join(Constant.UNDER_LINE, highDealOrderDtlPo.getFundCode(), highDealOrderDtlPo.getFundShareClass()));
                if (BusinessCodeEnum.SUBS.getMCode().equals(highDealOrderDtlPo.getmBusiCode()) || BusinessCodeEnum.PURCHASE.getMCode().equals(highDealOrderDtlPo.getmBusiCode())) {
                    BusinessCodeEnum businessCodeEnum = ConvertCodeUtils.convertBuyBusiCode(highProductStatInfoBean.getFundStat());
                    if (businessCodeEnum == null) {
                        logger.info("HighDealDtlInterposeFacadeService-checkCanSubmit,订单的产品不支持预约交易，该日期下该产品的净值交易状态不可以购买,fundCode={},mbusiCode={},submitTaDt={}", highDealOrderDtlPo.getFundCode(), highDealOrderDtlPo.getmBusiCode(), submitTaDt);
                        throw new BatchException(ExceptionCodes.BATCH_CENTER_BATCH_FLOW_ERROR, Objects.requireNonNull(BusinessCodeEnum.getByMCode(highDealOrderDtlPo.getmBusiCode())).getDescription() + "在上报日" + submitTaDt + "的净值状态：" + highProductStatInfoBean.getFundStat() + "不支持购买");

                    }
                } else if (BusinessCodeEnum.REDEEM.getMCode().equals(highDealOrderDtlPo.getmBusiCode())) {
                    if (!MDataDic.CAN_REDEEM_SET.contains(highProductStatInfoBean.getFundStat())) {
                        logger.info("HighDealDtlInterposeFacadeService-checkCanSubmit,订单的产品不支持预约交易，该日期下该产品的净值交易状态不可以赎回,fundCode={},mbusiCode={},submitTaDt={}", highDealOrderDtlPo.getFundCode(), highDealOrderDtlPo.getmBusiCode(), submitTaDt);
                        throw new BatchException(ExceptionCodes.BATCH_CENTER_BATCH_FLOW_ERROR, Objects.requireNonNull(BusinessCodeEnum.getByMCode(highDealOrderDtlPo.getmBusiCode())).getDescription() + "在上报日" + submitTaDt + "的净值状态：" + highProductStatInfoBean.getFundStat() + "不支持购买");
                    }
                }

            }
        }


    }

    /**
     * 是否支持预约
     */
    private boolean isScheduledTrade(String mBusinessCode, HighProductBaseInfoBean highProductBaseInfoBean) {
        if (highProductBaseInfoBean == null) {
            return true;
        }
        // 1.如果不支持预约,直接返回false
        if (IsScheduledTradeEnum.NotSupportAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())) {
            return false;
        }
        // 2.支持购买预约
        else if (IsScheduledTradeEnum.SupportBuyAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())) {
            return BusinessCodeEnum.SUBS.getMCode().equals(mBusinessCode) || BusinessCodeEnum.PURCHASE.getMCode().equals(mBusinessCode);
        }
        // 3.支持赎回预约
        else if (IsScheduledTradeEnum.SupportRedeemAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())) {
            return BusinessCodeEnum.REDEEM.getMCode().equals(mBusinessCode);
        }
        // 4.支持购买赎回预约
        else if (IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())) {
            if (BusinessCodeEnum.SUBS.getMCode().equals(mBusinessCode) || BusinessCodeEnum.PURCHASE.getMCode().equals(mBusinessCode)) {
                return true;
            }
            return BusinessCodeEnum.REDEEM.getMCode().equals(mBusinessCode);
        }
        return false;
    }


    /**
     * 处理支付日期
     *
     * @param newOrderList
     * @param oldOrderMap
     * @param resp
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/4/9 13:32
     * @since JDK 1.8
     */
    private void processPayment(List<HighDealOrderDtlPo> newOrderList, Map<String, HighDealOrderDtlPo> oldOrderMap, HighDealDtlInterposeResponse resp) {
        String paymentType = null;
        String txPmtFlag = null;
        String pmtDealNo = null;
        String pmtDt = null;
        Date appDtm = null;
        for (HighDealOrderDtlPo newOrder : newOrderList) {
            HighDealOrderDtlPo oldOrder = oldOrderMap.get(newOrder.getDealNo());
            if (!oldOrder.getSubmitTaDt().equals(newOrder.getSubmitTaDt())) {
                if (BusinessCodeEnum.SUBS.getMCode().equals(oldOrder.getmBusiCode()) || BusinessCodeEnum.PURCHASE.getMCode().equals(oldOrder.getmBusiCode())) {
                    PaymentOrderPo pmtPo = getPmt(newOrder.getDealNo());
                    if (pmtPo == null) {
                        String msg = "根据dealno" + newOrder.getDealNo() + "未找到支付订单";
                        resp.setReturnCode(ExceptionCodes.HIGH_BATCH_INTERPOSE_ERROR);
                        resp.setDescription(resp.getDescription() + msg);
                        logger.error("HighDealDtlInterposeFacadeService|processPayment " + msg);
                        continue;
                    }
                    pmtDealNo = pmtPo.getPmtDealNo();
                    paymentType = pmtPo.getPaymentType();
                    txPmtFlag = pmtPo.getTxPmtFlag();
                    pmtDt = pmtPo.getPmtDt();
                    appDtm = pmtPo.getAppDtm();
                }
            } else {
                String msg = "修改后的上报日期与修改前一致。dealNo:" + newOrder.getDealNo();
                resp.setReturnCode(ExceptionCodes.HIGH_BATCH_INTERPOSE_ERROR);
                resp.setDescription(resp.getDescription() + msg);
                logger.info("HighDealDtlInterposeFacadeService|processPayment " + msg);
                continue;
            }

            if (StringUtils.isNotEmpty(pmtDealNo) && PaymentTypeEnum.SELF_DRAWING.getCode().equals(paymentType)) {
                // 自划款的订单调用资金修改上报日期接口
                processPaymentSelfDrawing(pmtDealNo, newOrder, resp);
            } else if (StringUtils.isNotEmpty(pmtDealNo) && PaymentTypeEnum.PIGGY.getCode().equals(paymentType)
                    && !(TxPmtFlagEnum.PAY_SUC.getCode().equals(txPmtFlag) || TxPmtFlagEnum.PIGGY_FRZ_PAY_SUCC.getCode().equals(txPmtFlag))) {
                // 储蓄罐预约支付
                processPaymentPiggy(pmtDealNo, pmtDt, appDtm, newOrder, oldOrder, resp);
            }
        }
    }

    /**
     * 处理自划款支付对账日
     *
     * @param pmtDealNo
     * @param newOrder
     * @param resp
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/4/9 13:31
     * @since JDK 1.8
     */
    private void processPaymentSelfDrawing(String pmtDealNo, HighDealOrderDtlPo newOrder, HighDealDtlInterposeResponse resp) {
        try {
            UpdateFinaSubmitTaDtContext context = new UpdateFinaSubmitTaDtContext();
            context.setPmtDealNo(pmtDealNo);
            context.setSubmitTaDt(newOrder.getSubmitTaDt());
            logger.info("HighDealDtlInterposeFacadeService|execute|UpdateFinaSubmitTaDtContext:{}", JSON.toJSONString(context));
            UpdateFinaSubmitTaDtResult result = updateFinaSubmitTaDtOuterService.process(context);
            logger.info("HighDealDtlInterposeFacadeService|execute|UpdateFinaSubmitTaDtResult:{}", JSON.toJSONString(result));
            if (!OutReturnCodes.FIN_ONLINE_SUCCESS.equals(result.getReturnCode())) {
                resp.setReturnCode(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR);
                resp.setDescription(resp.getDescription() + "更新后台资金系统失败，请联系运维处理！");
            } else {
                resp.setDescription(resp.getDescription() + "资金系统返回成功");
            }
            //通知支付变更上报TA日期
            List<String> targetList = new LinkedList<>();
            targetList.add(pmtDealNo);

            // 更新中台支付对账日期
            updatePmtCheckDt(targetList, newOrder.getSubmitTaDt());

            ChangeSelfTransProdDtContext changeSelfTransProdDtContext = new ChangeSelfTransProdDtContext();
            changeSelfTransProdDtContext.setPmtDealNoList(targetList);
            changeSelfTransProdDtContext.setNewSubmitTaDt(newOrder.getSubmitTaDt());
            ChangeSelfTransProdDtResult changeSelfTransProdDtResult = changeSelfTransProdDtService.process(changeSelfTransProdDtContext);
            if (changeSelfTransProdDtResult == null || !OutReturnCodes.DUBBO_SUCCESS.equals(changeSelfTransProdDtResult.getReturnCode())) {
                resp.setDescription(resp.getDescription() + "通知支付系统失败，请联系运维处理！" + JSON.toJSONString(changeSelfTransProdDtResult));
            } else {
                resp.setDescription(resp.getDescription() + "支付系统返回成功！");
            }
        } catch (Exception e) {
            resp.setReturnCode(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR);
            resp.setDescription(resp.getDescription() + "更新后台资金系统或者支付失败，请联系运维处理！" + e.getMessage());
        }
    }

    /**
     * 处理储蓄罐预约支付转换日
     *
     * @param pmtDealNo
     * @param pmtDt
     * @param appDtm
     * @param newOrder
     * @param oldOrder
     * @param resp
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/4/9 13:32
     * @since JDK 1.8
     */
    private void processPaymentPiggy(String pmtDealNo, String pmtDt, Date appDtm, HighDealOrderDtlPo newOrder, HighDealOrderDtlPo oldOrder, HighDealDtlInterposeResponse resp) {
        try {
            ProductAppointmentInfoBean appointmentInfo = queryHighProductOuterService.queryAppointmentInfoByAppointDate(
                    oldOrder.getFundCode(), "0", oldOrder.getFundShareClass(), oldOrder.getDisCode(), appDtm);
            String payDeadlineDt = null;
            String newPmtDt;
            if (appointmentInfo != null) {
                payDeadlineDt = appointmentInfo.getPayDeadlineDtm().substring(0, 8);
            }
            // 转换日期=min(上报日，打款截止日）
            if (StringUtils.isEmpty(payDeadlineDt) || payDeadlineDt.compareTo(newOrder.getSubmitTaDt()) > 0) {
                newPmtDt = newOrder.getSubmitTaDt();
            } else {
                newPmtDt = payDeadlineDt;
            }
            if (newPmtDt.equals(pmtDt)) {
                logger.info("HighDealDtlInterposeFacadeService|execute|updatePmtDt pmtDt无变化，无需修改");
            } else {
                PaymentOrderPo params = new PaymentOrderPo();
                params.setPmtDealNo(pmtDealNo);
                // 设置最新支付日
                params.setPmtDt(newPmtDt);
                params.setPmtdtInterposeFlag(PmtdtInterposeFlagEnum.HAS_INTERPOSE.getCode());
                logger.info("HighDealDtlInterposeFacadeService|execute|updatePmtDt newPo:{},oldPmtDt:{}", JSON.toJSONString(params), pmtDt);
                int updateNum = updatePmtDt(params);
                logger.info("HighDealDtlInterposeFacadeService|execute|updatePmtDt count:{}", updateNum);
            }
        } catch (Exception e) {
            resp.setReturnCode(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR);
            resp.setDescription(resp.getDescription() + "更新支付日期（转换日期）付失败，请联系运维处理！" + e.getMessage());
        }
    }

    private int updatePmtDt(PaymentOrderPo paymentOrderPo) {
        return paymentOrderRepository.updateSelective(paymentOrderPo);
    }

    private void updatePmtCheckDt(List<String> pmtDealNoList, String pmtCheckDt) {
        try {
            int count = paymentOrderRepository.updatePmtCheckDt(pmtDealNoList, pmtCheckDt);
            logger.info("HighDealDtlInterposeFacadeService|updatePmtCheckDt|count:{}", count);
        } catch (Exception e) {
            logger.error("HighDealDtlInterposeFacadeService|updatePmtCheckDt|e.getMessage:{}", e.getMessage(), e);
        }
    }

    private HighDealOrderDtlPo getHighDealOrderDtlPo(String dealNo) {
        List<HighDealOrderDtlPo> list = highDealOrderDtlRepository.getByDealNo(dealNo);
        if (CollectionUtils.isEmpty(list)) {
            throw new BatchException(ExceptionCodes.PARAMS_ERROR, "找不到订单号dealNo=" + dealNo + "的数据");
        }
        return list.get(0);
    }

    private List<HighDealOrderDtlPo> getMergeSubmitOrders(String mainDealNo) {
        return highDealOrderDtlRepository.getOrdersByMainDealNo(mainDealNo);
    }

    private PaymentOrderPo getPmt(String dealNo) {
        return paymentOrderRepository.getByDealNo(dealNo);
    }

    private int updateDtl(List<HighDealOrderDtlPo> orderList, Date now, Map<String, String> oldSubmitDtMap) {
        return highDealOrderDtlService.updateInterposeFlagAndSubmitTaDt(orderList, now, oldSubmitDtMap);
    }

    private int updateDtl(List<HighDealOrderDtlPo> newPoList, Date now, String dealNo) {
        return highDealOrderDtlService.updateInterposeFlagAndSubmitTaDt(newPoList, now, null, true);
    }
}
