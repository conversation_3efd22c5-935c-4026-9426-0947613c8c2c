package com.howbuy.tms.high.batch.service.business.finafilemonitor;

import com.howbuy.tms.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:是否使用此资金处理类
 * @Author: yun.lu
 * Date: 2025/4/8 10:34
 */
@Service
@Slf4j
public class FinaFileServiceFactory {
    @Autowired
    private List<AbstractFinaFileService> abstractFinaFileServiceList;


    public AbstractFinaFileService getFinaFileService(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AbstractFinaFileService abstractFinaFileService : abstractFinaFileServiceList) {
            if (abstractFinaFileService.userThis(code)) {
                return abstractFinaFileService;
            }
        }
        return null;
    }
}
