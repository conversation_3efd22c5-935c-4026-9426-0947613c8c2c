/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.constant.IgnoreExceptionsCodes;
import com.howbuy.tms.common.outerservice.fbsonline.fundredeem.FundRedeemContext;
import com.howbuy.tms.common.outerservice.fbsonline.fundredeem.FundRedeemOuterService;
import com.howbuy.tms.common.outerservice.fbsonline.fundredeem.FundRedeemResult;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.service.business.ordersubmit.BaseResponse;
import com.howbuy.tms.high.batch.service.business.ordersubmit.CheckOrderSubmitService;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:标准赎回接口（接口GN_FDS_NEW_02）
 * @reason:
 * <AUTHOR>
 * @date 2016年9月14日 下午1:59:49
 * @since JDK 1.6
 */
@Service("fundRedeemSubmitService")
public class FundRedeemSubmitService extends CheckOrderSubmitService {

    private static Logger logger = LogManager.getLogger(FundRedeemSubmitService.class);
    @Autowired
    private FundRedeemOuterService fundRedeemOuterService;

    @Override
    protected BaseResponse doSubmit(SimuFundCheckOrderManualSubmitVo fundCheckOrderPo) {
        FundRedeemContext context = generateContext(fundCheckOrderPo);
        FundRedeemResult result = fundRedeemOuterService.doFundRedeem(context);
        logger.info("FundRedeemSubmitService|result:" + JSON.toJSONString(result));
        BaseResponse baseResponse = new BaseResponse();
        BeanUtils.copyProperties(result, baseResponse);
        return baseResponse;
    }

    @Override
    protected void submitSuccess(SimuFundCheckOrderPo fundCheckOrderPo) {
    }

    @Override
    protected void submitFail(SimuFundCheckOrderPo fundCheckOrderPo) {
    }

    public static FundRedeemContext generateContext(SimuFundCheckOrderPo fundCheckOrderPo) {
        FundRedeemContext context = new FundRedeemContext();

        // 交易渠道 TO 交易渠道
        context.setTradeChannel(fundCheckOrderPo.getTxChannel());
        // 分销机构 TO 分销机构代码
        context.setDisCode(fundCheckOrderPo.getDisCode());
        // 网点号 TO 网点代码
        context.setOutletCode(fundCheckOrderPo.getOutletCode());
        // 上报Ta日 TO 申请日期
        context.setAppDt(fundCheckOrderPo.getSubmitTaDt());
        // 12点 TO 申请时间
        context.setAppTm("120000");
        // IP地址 TO 交易IP
        context.setIpAddress(fundCheckOrderPo.getIpAddress());
        // 上报订单号 TO 中台订单号
        context.setSubmitDealNo(fundCheckOrderPo.getSubmitDealNo());
        // 风险确认标志
        context.setRiskFlag(fundCheckOrderPo.getRiskFlag());

        // 交易账号
        context.setTxAcctNo(fundCheckOrderPo.getTxAcctNo());
        // 巨额赎回标志
        context.setLargeRedmFlag(fundCheckOrderPo.getLargeRedmFlag());
        // // 客户类型 TODO 待确认
        context.setInvstType(fundCheckOrderPo.getInvstType());
        // 客户姓名
        context.setCustName(fundCheckOrderPo.getCustName());
        // 证件类型
        //context.setIdType(fundCheckOrderPo.getIdType());
        // 证件号码
        //context.setIdNo(fundCheckOrderPo.getIdNo());
        // 银行账号
        //context.setBankAcct(fundCheckOrderPo.getBankAcct());
        // 银行代码
        context.setBankCode(fundCheckOrderPo.getBankCode());
        // 基金代码
        context.setFundCode(fundCheckOrderPo.getFundCode());
        // 份额类型
        context.setShareClass(fundCheckOrderPo.getFundShareClass());
        // 赎回份额
        context.setAppVol(fundCheckOrderPo.getAppVol());
        // 子交易账号
        context.setSubTxAcctNo(fundCheckOrderPo.getSubTxAcctNo());
        // 资金账号
        context.setCpAcctNo(fundCheckOrderPo.getCpAcctNo());
        // 备注
        context.setMemo(fundCheckOrderPo.getMemo());
        // 经办人姓名
        context.setTransactorName(fundCheckOrderPo.getTransactorName());
        // 经办人证件号码
        context.setTransactorIdNo(fundCheckOrderPo.getTransactorIdNo());
        // 经办人证件类型
        context.setTransactorIdType(fundCheckOrderPo.getTransactorIdType());
        context.setUnusualTransType(fundCheckOrderPo.getUnusualTransType());
        // 设置100%赎回时, 后台联机将忽略指定的错误码
        BigDecimal appRatio = fundCheckOrderPo.getAppRatio();
        if (appRatio != null && appRatio.doubleValue() == 1) {
            Set<String> ignoreExceptions = new HashSet<String>();
            ignoreExceptions.add(IgnoreExceptionsCodes.LESS_T_MIN_HOLD_LOW_LIMIT_AFTER_REDEEM);
            context.setIgnoreExceptions(ignoreExceptions);
        }
        context.setProtocolNo(fundCheckOrderPo.getProtocolNo());
        return context;
    }
}
