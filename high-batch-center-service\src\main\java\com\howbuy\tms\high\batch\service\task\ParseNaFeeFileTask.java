package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.high.batch.dao.po.batch.CmFileProcessRecPo;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.service.file.fileimport.AbstractFileWebDavImportService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ParseNaFeeFileTask extends AbstractHowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(ParseNaFeeFileTask.class);

    private AbstractFileWebDavImportService cmNaFileImportService;
    private CmFileProcessRecPo cmFileProcessRecPo;
    private List<ThreadExceptionStatus> statusList;
    private String lastWorkDay;
    private String crmFileReceiveType;
    private String naFeeFilePathName;

    public ParseNaFeeFileTask(CmFileProcessRecPo cmFileProcessRecPo, List<ThreadExceptionStatus> statusList, String lastWorkDay, AbstractFileWebDavImportService cmNaFileImportService, String crmFileReceiveType, String naFeeFilePathName) {
        this.cmNaFileImportService = cmNaFileImportService;
        this.cmFileProcessRecPo = cmFileProcessRecPo;
        this.statusList = statusList;
        this.lastWorkDay = lastWorkDay;
        this.crmFileReceiveType = crmFileReceiveType;
        this.naFeeFilePathName = naFeeFilePathName;
    }

    @Override
    protected void callTask() {
        try {
            FileImportContext context = new FileImportContext();
            context.setFileName(naFeeFilePathName);
            context.setRelationPath(File.separator + cmFileProcessRecPo.getDealDt() + File.separator + cmFileProcessRecPo.getsVersionNo() + File.separator);
            context.setTaTradeDt(cmFileProcessRecPo.getDealDt());
            context.getParams().put("importDt", cmFileProcessRecPo.getDealDt());
            context.getParams().put("versionNo", cmFileProcessRecPo.getVersionNo().toString());
            context.getParams().put("lastWorkDay", lastWorkDay);
            context.getParams().put("dealDt", cmFileProcessRecPo.getDealDt());
            context.getParams().put("crmFileType", crmFileReceiveType);
            cmNaFileImportService.process(context);
        } catch (Exception e) {
            ThreadExceptionStatus status = new ThreadExceptionStatus();
            status.setExsitException(true);
            status.setException(e);
            statusList.add(status);
            String msg = String.format("处理增量na费文件异常,版本号:%s", cmFileProcessRecPo.getVersionNo());
            OpsMonitor.warn(msg, OpsMonitor.ERROR);
            logger.error("处理增量na费文件异常:", e);
        }
    }
}