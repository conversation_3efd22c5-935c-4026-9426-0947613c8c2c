package com.howbuy.tms.high.batch.remote.main;


import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.EnableLoadTimeWeaving;
import org.springframework.context.annotation.ImportResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@RefreshScope
@RestController
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableDiscoveryClient
@ImportResource({"classpath:META-INF/spring/high-batch-center-configs.xml"})
@SpringBootApplication(scanBasePackages = {"com.howbuy.tms.high.batch", "com.howbuy.tms.common.simu.outerservice"}, exclude = {DataSourceAutoConfiguration.class})
public class HighBatchCenterApplication {
    private static final Logger log = LogManager.getLogger(HighBatchCenterApplication.class);

    @Value("${spring.application.name}")
    private String applicationName;

    public static void main(String[] args) {
        log.info("HighBatchCenterApplication-开始****************************************");
        //设置dubbo 日志为slf4j
        System.setProperty("dubbo.application.logger", "slf4j");
        //设置druid 不ping 数据库
        System.setProperty("druid.mysql.usePingMethod", "false");
        try {
            SpringApplication.run(HighBatchCenterApplication.class, args);
            log.info("HighBatchCenterApplication start success");
        } catch (Exception e) {
            log.error("HighBatchCenterApplication：" + e.getMessage(), e);
        }
    }

    @Configuration
    @ConditionalOnProperty(prefix = "spring.aspect.weaving", name = {"ltw", "flag"}, havingValue = "true", matchIfMissing = true)
    @EnableLoadTimeWeaving
    static class AspectJLTWConfig {

    }

    /**
     * 健康检查
     *
     * @return
     */
    @RequestMapping("/hello")
    public String hello() {
        return "hello, ".concat(applicationName);
    }
}