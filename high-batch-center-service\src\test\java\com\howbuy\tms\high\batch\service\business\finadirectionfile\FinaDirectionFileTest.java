/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.batch.service.business.finadirectionfile;

import com.howbuy.tms.high.batch.service.base.BaseTestSuite;
import com.howbuy.tms.high.batch.service.business.finadirectionmonitor.DirectionFinaFileProcessor;
import com.howbuy.tms.high.batch.service.business.finadirectionmonitor.FinaDirectionMessage;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Description:(TODO 请在此添加描述) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年8月15日 下午5:45:12
 * @since JDK 1.7
 */
public class FinaDirectionFileTest extends BaseTestSuite {

    @Autowired
    private DirectionFinaFileProcessor finaDirectionFileProcessor;

    @Test
    public void test() {
        FinaDirectionMessage msg = new FinaDirectionMessage();
        msg.setAckDt("20180425");
        msg.setFileName("20180425.txt");
        //finaDirectionFileProcessor.process(msg);
    }
}

