/**
 * Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit.refreshnotifystatus;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.outerservice.acccenter.queryaqsetmanagementcertificatestatus.QueryAssetManagementCertificateStatusOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryaqsetmanagementcertificatestatus.QueryAssetManagementCertificateStatusResult;
import com.howbuy.tms.common.outerservice.acccenter.queryassetcertificatestatus.QueryCustAssetCertificateStatusOuterService;
import com.howbuy.tms.common.outerservice.cc.center.queryassetcertificate.QueryCurrentAssetCertificateStatusResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductControlBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.TradeParamLocalUtils;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.service.business.submittadtcal.SubmitTaDtCalService;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.service.order.highdealorderdtl.HighDealOrderDtlService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description:(刷新订单通知状态任务)
 * @reason:
 * @date 2018年5月29日 下午2:08:35
 * @since JDK 1.7
 */
public class RefreshNotifyStatusTask implements Runnable {
    private static Logger logger = LogManager.getLogger(RefreshNotifyStatusTask.class);

    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    private HighDealOrderDtlService highDealOrderDtlService;
    private QueryHighProductOuterService queryHighProductOuterService;
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    private SubmitTaDtCalService submitTaDtCalService;
    private String retrievePeriod;
    private List<HighDealOrderDtlPo> list;
    // 查询资管资产证明状态
    private QueryAssetManagementCertificateStatusOuterService queryAssetManagementCertificateStatusOuterService;
    // 查询私募资产证明状态
    private QueryCustAssetCertificateStatusOuterService queryCustAssetCertificateStatusOuterService;

    /**
     * 计数栅
     */
    private CountDownLatch latch;

    private Date now;

    public RefreshNotifyStatusTask(HighDealOrderDtlRepository highDealOrderDtlRepository, QueryHighProductOuterService queryHighProductOuterService,
                                   QueryHbOneNoOuterService queryHbOneNoOuterService,
                                   SubmitTaDtCalService submitTaDtCalService,
                                   QueryAssetManagementCertificateStatusOuterService queryAssetManagementCertificateStatusOuterService,
                                   QueryCustAssetCertificateStatusOuterService queryCustAssetCertificateStatusOuterService,
                                   HighDealOrderDtlService highDealOrderDtlService,
                                   List<HighDealOrderDtlPo> list, String retrievePeriod,
                                   Date now, CountDownLatch latch) {
        super();
        this.highDealOrderDtlRepository = highDealOrderDtlRepository;
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.queryHbOneNoOuterService = queryHbOneNoOuterService;
        this.submitTaDtCalService = submitTaDtCalService;
        this.queryCustAssetCertificateStatusOuterService = queryCustAssetCertificateStatusOuterService;
        this.queryAssetManagementCertificateStatusOuterService = queryAssetManagementCertificateStatusOuterService;
        this.retrievePeriod = retrievePeriod;
        this.highDealOrderDtlService = highDealOrderDtlService;
        this.list = list;
        this.now = now;
        this.latch = latch;
    }

    @Override
    public void run() {
        logger.info("RefreshNotifyStatusTask|run|start...");
        try {
            boolean result = false;
            for (HighDealOrderDtlPo po : list) {
                List<HighDealOrderDtlPo> orders;
                if (YesOrNoEnum.YES.getCode().equals(po.getMergeSubmitFlag())) {
                    // 查询子订单列表
                    orders = getOrdersByMainDealNo(po.getMainDealOrderNo());
                } else {
                    orders = new ArrayList<>();
                    orders.add(po);
                }
                // 记录订单原始信息
                Map<String, OldOrderInfoBean> oldOrderInfoMap = new HashMap<>(orders.size());
                orders.forEach(order -> {
                    OldOrderInfoBean old = new OldOrderInfoBean();
                    old.setRetrieveDtm(order.getRetrieveDtm());
                    old.setNotifySubmitFlag(order.getNotifySubmitFlag());
                    old.setSubmitTaDt(order.getSubmitTaDt());
                    old.setAssetStatus(order.getAssetcertificateStatus());
                    oldOrderInfoMap.put(order.getDealNo(), old);
                });
                logger.info("RefreshNotifyStatusTask|run|oldOrderInfo:{}", JSON.toJSONString(oldOrderInfoMap));
                try {
                    result = proecess(orders);
                    logger.info("RefreshNotifyStatusTask|run|dealDtlNo:{}, result:{}", po.getDealDtlNo(), result);

                    // 更新结果
                    updateResult(result, orders, oldOrderInfoMap);
                } catch (Exception e) {
                    logger.error("RefreshNotifyStatusTask|run|error,msg:", e);
                    po.setRetrieveDtm(calRetrieveDtm(null, false));
                    updateResult(false, orders, oldOrderInfoMap);
                    String msg = "RefreshNotifyStatusTask-更新订单通知状态失败,dealNo:" + po.getDealNo();
                    OpsMonitor.warn(msg, OpsMonitor.INFO);
                    logger.error(msg);
                }
            }
        } catch (Exception ex) {
            logger.error("RefreshNotifyStatusTask|run|error, msg:{}", ex.getMessage(), ex);
        } finally {
            latch.countDown();
        }
    }

    /**
     * 查询子订单列表
     *
     * @param mainDealNo
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @author: huaqiang.liu
     * @date: 2021/7/2 16:38
     * @since JDK 1.8
     */
    private List<HighDealOrderDtlPo> getOrdersByMainDealNo(String mainDealNo) {
        return highDealOrderDtlRepository.getOrdersByMainDealNo(mainDealNo);
    }

    /**
     * updateResult:(更新结果)
     *
     * @param result
     * @param orders
     * @param oldOrderInfoMap
     * <AUTHOR>
     * @date 2018年5月29日 下午8:52:14
     */
    private void updateResult(boolean result, List<HighDealOrderDtlPo> orders, Map<String, OldOrderInfoBean> oldOrderInfoMap) {
        highDealOrderDtlService.updateNotifyStatus(result, orders, oldOrderInfoMap);
    }

    /**
     * proecess:(高端TP订单处理)
     *
     * @param orders
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午7:23:32
     */
    private boolean proecess(List<HighDealOrderDtlPo> orders) {
        HighDealOrderDtlPo po = orders.get(0);
        HighProductControlBean controlBean = queryHighProductOuterService.gethighProductControlInfo(po.getFundCode());
        if (controlBean == null) {
            logger.error("RefreshNotifyStatusTask|run|highProductControlBean is null, fundCode:{}", po.getFundCode());
            return false;
        }

        HighProductBaseInfoBean highProductBaseInfoBean = queryHighProductOuterService.getHighProductBaseInfo(po.getFundCode());
        if (highProductBaseInfoBean == null) {
            logger.error("RefreshNotifyStatusTask|run|highProductBaseInfoBean is null, fundCode:{}", po.getFundCode());
            return false;
        }

        // 资产证明校验
        boolean assetResult = validateAsset(orders, controlBean, highProductBaseInfoBean);
        if (!assetResult) {
            // 资产证明需要及时去刷新，无需设置查询时间
            orders.forEach(order -> order.setRetrieveDtm(null));
            logger.error("RefreshNotifyStatusTask|run|assetResult is false, dealNo:{}", po.getDealNo());
            return false;
        }

        // 冷静期校验
        boolean calmDtmResult = validateCalmDtm(orders, controlBean);
        if (!calmDtmResult) {
            orders.forEach(order -> order.setRetrieveDtm(calRetrieveDtm(order.getCalmDtm(), true)));
            logger.error("RefreshNotifyStatusTask|run|calmDtmResult is false, dealNo:{}", po.getDealNo());
            return false;
        }

        // 双录校验
        boolean dualentryResult = validateDualentry(orders, controlBean);
        if (!dualentryResult) {
            orders.forEach(order -> order.setRetrieveDtm(calRetrieveDtm(null, false)));
            logger.error("RefreshNotifyStatusTask|run|dualentryResult is false, dealNo:{}", po.getDealNo());
            return false;
        }

        // 回访校验
        boolean callbackResult = validateCallback(orders, controlBean);
        if (!callbackResult) {
            orders.forEach(order -> order.setRetrieveDtm(calRetrieveDtm(null, false)));
            logger.error("RefreshNotifyStatusTask|run|callbackResult is false, dealNo:{}", po.getDealNo());
            return false;
        }

        // 产品通道为“7-TP私募”，则通过上报TA日计算模块计算新的上报TA日
        if (ProductChannelEnum.TP_SM.getCode().equals(po.getProductChannel())) {
            String submitTaDt = submitTaDtCalService.cal(po.getDealDtlNo());
            orders.forEach(order -> order.setSubmitTaDt(submitTaDt));
        }

        // 更新订单通知状态
        orders.forEach(order -> order.setNotifySubmitFlag(NotifySubmitFlagEnum.NO_NOTIFY.getCode()));

        return true;
    }

    /**
     * validateAsset:(资产证明校验)
     *
     * @param orders
     * @param controlBean
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午4:33:33
     */
    private boolean validateAsset(List<HighDealOrderDtlPo> orders, HighProductControlBean controlBean, HighProductBaseInfoBean highProductBaseInfoBean) {
        String hbOneNo = null;
        String assertCertificateStatus = null;
        for (HighDealOrderDtlPo po : orders) {
            // 关键属性非空校验
            if (StringUtils.isEmpty(po.getAssetcertificateStatus()) || StringUtils.isEmpty(controlBean.getAssetProvFlag())
                    || StringUtils.isEmpty(po.getAssetInterposeFlag())) {
                logger.error("RefreshNotifyStatusTask|validateAsset|param is null, DealDtlNo:{}, AssetcertificateStatus:{}, AssetProvFlag:{}, AssetInterposeFlag:{}",
                        new Object[]{po.getDealDtlNo(), po.getAssetcertificateStatus(), controlBean.getAssetProvFlag(), po.getAssetInterposeFlag()});
                return false;
            }

            // 1. “上报是否需要资产证明”为”是”
            // 2. 资产证明状态为”无效”, 且资产证明人工干预标识为”未干预”
            if (SwitchEnum.OPEN.getCode().equals(controlBean.getAssetProvFlag()) && AssetcertificateStatusEnum.INVALID.getCode().equals(po.getAssetcertificateStatus())
                    && InterposeFlagEnum.UN_DONE.getCode().equals(po.getAssetInterposeFlag())) {
                TradeParamLocalUtils.setDisCode(po.getDisCode());

                if (assertCertificateStatus == null) {
                    // 查询资产证明状态
                    hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(po.getTxAcctNo());
                    assertCertificateStatus = queryAssertCertificateStatus(highProductBaseInfoBean, hbOneNo);
                }

                if (assertCertificateStatus == null) {
                    logger.error("RefreshNotifyStatusTask|validateAsset|queryKycInfoResult or property is null, DealDtlNo:{}, hbOneNo:{}, ManagerAttribute:{}",
                            po.getDealDtlNo(), hbOneNo, highProductBaseInfoBean.getManagerAttribute());
                    return false;
                }

                if (!Constant.ASSET_CERTIFICATE_STATUS_VALID.equals(assertCertificateStatus)) {
                    return false;
                } else {
                    po.setAssetcertificateStatus(AssetcertificateStatusEnum.VALID.getCode());
                }
            }
        }

        return true;
    }

    private String queryAssertCertificateStatus(HighProductBaseInfoBean highProductBaseInfoBean, String hbOneNo) {
        if (ManagerAttributeEnum.SIMU.getCode().equals(highProductBaseInfoBean.getManagerAttribute())) {
            QueryCurrentAssetCertificateStatusResult queryRst = queryCustAssetCertificateStatusOuterService.queryCurrentAssetCertificateStatus(hbOneNo);
            return queryRst.getStatus();
        } else if (ManagerAttributeEnum.ASSERT_MANAGER.getCode().equals(highProductBaseInfoBean.getManagerAttribute())) {
            QueryAssetManagementCertificateStatusResult queryRst = queryAssetManagementCertificateStatusOuterService.queryCurrentAssetCertificateStatus(hbOneNo);
            return queryRst.getStatus();
        }

        return null;
    }

    /**
     * validateCallback:(回访校验)
     *
     * @param orders
     * @param controlBean
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午3:33:43
     */
    private boolean validateCallback(List<HighDealOrderDtlPo> orders, HighProductControlBean controlBean) {
        for (HighDealOrderDtlPo po : orders) {
            // 关键属性非空校验
            if (StringUtils.isEmpty(po.getCallbackStatus()) || StringUtils.isEmpty(controlBean.getVisitForceFlag())
                    || StringUtils.isEmpty(po.getCallbackInterposeFlag())) {
                logger.error(
                        "RefreshNotifyStatusTask|validateCallback|param is null, DealDtlNo:{}, CallbackStatus:{}, VisitForceFlag:{}, CallbackInterposeFlag:{}",
                        new Object[]{po.getDealDtlNo(), po.getCallbackStatus(), controlBean.getVisitForceFlag(), po.getCallbackInterposeFlag()});
                return false;
            }

            // 1. 上报是否需要强制回访”为”是”
            // 2. 回访状态为”未回访”且”回访人工干预标识”为”未干预”
            if (SwitchEnum.OPEN.getCode().equals(controlBean.getVisitForceFlag()) && CallbackStatusEnum.UN_DONE.getCode().equals(po.getCallbackStatus())
                    && InterposeFlagEnum.UN_DONE.getCode().equals(po.getCallbackInterposeFlag())) {
                return false;
            }
        }

        return true;
    }

    /**
     * validateDualentry:(双录校验)
     *
     * @param orders
     * @param controlBean
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午3:33:43
     */
    private boolean validateDualentry(List<HighDealOrderDtlPo> orders, HighProductControlBean controlBean) {
        for (HighDealOrderDtlPo po : orders) {
            // 关键属性非空校验
            if (StringUtils.isEmpty(po.getDualentryStatus()) || StringUtils.isEmpty(po.getDualentryInterposeFlag())) {
                logger.error("RefreshNotifyStatusTask|validateDualentry|param is null, DealDtlNo:{}, DualentryStatus:{}, DualentryInterposeFlag:{}",
                        new Object[]{po.getDealDtlNo(), po.getDualentryStatus(), po.getDualentryInterposeFlag()});
                return false;
            }

            // 双录状态为”未双录”且”双录人工干预标识”为”未干预”
            if (InterposeFlagEnum.UN_DONE.getCode().equals(po.getDualentryInterposeFlag())
                    && DualentryStatusEnum.UN_DONE.getCode().equals(po.getDualentryStatus())) {
                return false;
            }
        }

        return true;
    }

    /**
     * validateCalmDtm:(冷静期校验)
     *
     * @param orders
     * @param controlBean
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午3:33:43
     */
    private boolean validateCalmDtm(List<HighDealOrderDtlPo> orders, HighProductControlBean controlBean) {
        for (HighDealOrderDtlPo po : orders) {
            // 关键属性非空校验
            if (po.getCalmDtm() == null || StringUtils.isEmpty(controlBean.getCalmOverFlag()) || StringUtils.isEmpty(po.getCalmdtmInterposeFlag())) {
                logger.error("RefreshNotifyStatusTask|validateCalmDtm|param is null, dealDtlNo:{}, calmDtm:{}, calmOverFlag:{}, calmdtmInterposeFlag:{}",
                        new Object[]{po.getDealDtlNo(), po.getCalmDtm(), controlBean.getCalmOverFlag(), po.getCalmdtmInterposeFlag()});
                return false;
            }

            // 1. 上报是否需要过冷静期”为”是”
            // 2. 当前系统时间小于等于订单冷静期且”冷静期干预标识”为”未干预”
            if (SwitchEnum.OPEN.getCode().equals(controlBean.getCalmOverFlag()) && now.compareTo(po.getCalmDtm()) < 0
                    && InterposeFlagEnum.UN_DONE.getCode().equals(po.getCalmdtmInterposeFlag())) {
                return false;
            }
        }

        return true;
    }

    /**
     * calRetrieveDtm:(计算检索时间)
     *
     * @param calmDtm
     * @param calmDtmFlag
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午4:20:38
     */
    private Date calRetrieveDtm(Date calmDtm, boolean calmDtmFlag) {
        if (calmDtmFlag && calmDtm != null) {
            return new Date(calmDtm.getTime() - now.getTime());
        } else {
            return DateUtils.addDateByType(now, Calendar.MINUTE, Integer.parseInt(retrievePeriod));
        }
    }

}