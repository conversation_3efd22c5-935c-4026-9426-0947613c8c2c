package com.howbuy.tms.high.batch.service.filter;

import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.slf4j.MDC;
import com.howbuy.trace.RequestChainTrace;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.UUID;

/**
 * Dubbo MDC上下文传递Filter
 * 
 * 解决Dubbo 3.x升级后MDC上下文传递问题，确保日志中的tid和ranNo能够正确显示
 * 
 * <AUTHOR>
 * @date 2024-11-08
 */
@Activate(group = {"provider", "consumer"}, order = -10000)
public class MdcContextFilter implements Filter {
    
    private static final Logger logger = LogManager.getLogger(MdcContextFilter.class);
    
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        // 获取或生成traceId
        String traceId = RequestChainTrace.getReqId();
        if (StringUtils.isEmpty(traceId)) {
            traceId = UUID.randomUUID().toString().replace("-", "");
            RequestChainTrace.buildAndSet(traceId, null);
        }
        
        // 生成ranNo
        String ranNo = String.valueOf(System.currentTimeMillis() % 100000);
        
        // RequestChainTrace.buildAndSet已经自动设置了SLF4J MDC
        // 我们只需要设置Log4j2 ThreadContext来支持${ctx:uuid}语法
        String currentRanNo = RequestChainTrace.getRanNo();
        org.apache.logging.log4j.ThreadContext.put("uuid", traceId);
        org.apache.logging.log4j.ThreadContext.put("ranNo", currentRanNo);
        
        logger.debug("MdcContextFilter set context: uuid={}, ranNo={}", traceId, ranNo);
        
        try {
            return invoker.invoke(invocation);
        } finally {
            // 清理ThreadContext，MDC由RequestChainTrace管理
            try {
                org.apache.logging.log4j.ThreadContext.remove("uuid");
                org.apache.logging.log4j.ThreadContext.remove("ranNo");
                logger.debug("MdcContextFilter cleared ThreadContext");
            } catch (Exception e) {
                logger.warn("MdcContextFilter clear context error: {}", e.getMessage());
            }
        }
    }
}
