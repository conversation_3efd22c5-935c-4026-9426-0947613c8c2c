spring.application.name=high-batch-center-remote
spring.cloud.nacos.config.server-addr=nacos1.inner.howbuy.com:8848,nacos2.inner.howbuy.com:8848,nacos3.inner.howbuy.com:8848
spring.cloud.nacos.config.namespace=it06
spring.cloud.nacos.config.group=4.7.77
spring.cloud.nacos.config.file-extension=properties

spring.profiles.active=dev

#spring.aspect.weaving.ltw=true
spring.aspect.weaving.flag=false

#健康检查
management.server.port=39999
management.health.db.enabled=false
management.health.redis.enabled=false
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=ALWAYS

spring.freemarker.checkTemplateLocation=false
spring.freemarker.prefer-file-system-access=false

#logging.config=classpath:log4j2.xml

