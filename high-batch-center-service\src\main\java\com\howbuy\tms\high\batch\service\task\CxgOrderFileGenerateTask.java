package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.high.batch.service.business.cxgorderfile.CxgOrderFileGenerateProcessor;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @Description:生成储蓄罐存入文件
 * @Author: yun.lu
 * Date: 2025/3/25 16:10
 */
@Data
@AllArgsConstructor
public class CxgOrderFileGenerateTask extends AbstractHowbuyBaseTask {
    private static Logger log = LogManager.getLogger(CxgOrderFileGenerateTask.class);
    private String taTradeDt;
    private String batchNo;
    private CxgOrderFileGenerateProcessor cxgOrderFileGenerateProcessor;

    @Override
    protected void callTask() {
        cxgOrderFileGenerateProcessor.execute(taTradeDt, batchNo);
    }
}
