/**
 * Copyright (c) 2020, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.generatefile;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.FileOptionStatus;
import com.howbuy.tms.common.enums.database.FileTypeEnum;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.common.enums.database.WorkdayTypeEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessRecPo;
import com.howbuy.tms.high.batch.dao.po.batch.WorkdayPo;
import com.howbuy.tms.high.batch.dao.po.order.ExportCmNaReceivFeePo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.facade.enums.HeExportFileTypeEnum;
import com.howbuy.tms.high.batch.service.business.generatefile.bean.CreateAssetTxtFileParam;
import com.howbuy.tms.high.batch.service.common.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.FileSdkUtil;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.repository.CmFileProcessRecRepository;
import com.howbuy.tms.high.batch.service.repository.ExportCmNaReceivFeeRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.bean.TxtFileExportContext;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.impl.ExportHighCrmNaFeeAssertFileService;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description: 生成高端直销业绩因子文件处理类
 * @date 2020/1/14 16:22
 * @since JDK 1.7
 */
@Service
public class GenerateHighCmNaAssertFileService extends AbstractGenerateAssertFileService {

    private static Logger logger = LogManager.getLogger(GenerateHighCmNaAssertFileService.class);

    @Value("${export.highCmNaFile.fileFileds}")
    private String fileFileds;

    @Value("${export.highCmNaFile.fileFiledsName}")
    private String fileFiledsName;

    @Autowired
    private ExportCmNaReceivFeeRepository exportCmNaReceivFeeRepository;

    @Autowired
    private CmFileProcessRecRepository cmFileProcessRecRepository;

    @Autowired
    private ExportHighCrmNaFeeAssertFileService exportHighCrmNaFeeAssertFileService;

    /**
     * @param
     * @return void
     * @description: 交易确认文件生成
     * @author: hongdong.xie
     * @date: 2020/1/15 15:48
     * @since JDK 1.7
     */
    public void generate() throws Exception {
        WorkdayPo workdayPo = workdayService.getWorkay(WorkdayTypeEnum.SYS_TYPE);
        String tradeDt = workdayService.getSaleSysCurrWorkay();
        String lastWorkDay = workdayPo.getLastWorkday();

        // 判断当前日期是否是工作日
        String nowDay = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        if (!StringUtils.equals(nowDay, tradeDt)) {
            logger.info("GenerateHighCmNaFileService|generate|nowDay:{} is not work day :{}", nowDay, tradeDt);
            return;
        }

        // 判断ok文件是否已经生成
        FundFileProcessRecPo processPo = fundFileProcessRecRepository.selectByUniqueKey(tradeDt, FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode(), Constant.ALL_TA_CODE);
        if (processPo != null && ProcessStatusEnum.NOTICE_SUCCESS.getCode().equals(processPo.getProcessStatus())) {
            logger.info("GenerateHighCmBlackFileService|generate|ok文件{}已生成", processPo.getFileName());
            return;
        }

        // 查询直销业文件处理记录
        Map<String, FundFileProcessRecPo> recPoMap = fundFileProcessRecRepository.getMapByFileTypeAndTaTradeDt(FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode(), tradeDt);

        // 直销处理
        cmProcess(tradeDt, lastWorkDay, recPoMap);

        //发送文件消息&OK文件消息
        sendFileMessage(tradeDt);
    }

    private void cmProcess(String tradeDt, String lastWorkDay, Map<String, FundFileProcessRecPo> recPoMap) throws Exception {
        // 处理直销文件
        boolean fileProcess = crmFileHasProcess(tradeDt, FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode());
        if (!fileProcess) {
            logger.info("na费直销文件还没有处理完成,tradeDt={}", tradeDt);
            return;
        }
        BigDecimal latestVersion = cmFileProcessRecRepository.selectLatestDayVersion();
        FundFileProcessRecPo recPo = null;
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setFileName(getCheckFileName(tradeDt, Constant.HOWBUY_CM_TA, latestVersion + ""));
        fileSdkPathInfo.setMiddlePath(getPath(tradeDt));
        fileSdkPathInfo.setBusinessCode(getBusinessCode());
        String absolutePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
        if (recPoMap.isEmpty()) {
            recPo = addFundFileProcessRec(tradeDt, Constant.HOWBUY_CM_TA, FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode(), absolutePath);
        } else {
            recPo = recPoMap.get(Constant.HOWBUY_CM_TA);
            if (recPo == null) {
                //判断文件记录是否生成，如果没有则生成文件记录
                recPo = addFundFileProcessRec(tradeDt, Constant.HOWBUY_CM_TA, FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode(), absolutePath);
            }
        }

        //判断文件是否已经成功
        if (FileOpStatusEnum.MAKE_SUCCESSFUL.getKey().equals(recPo.getFileOpStatus())) {
            logger.info("cmFileProcess ack file has process ");
        } else {
            CountDownLatch latch = new CountDownLatch(1);
            THREAD_POOL.execute(ThreadTraceHelper.decorate(new CrmNaTask(recPo, lastWorkDay, latch)));
            try {
                //等待处理
                latch.await();
            } catch (InterruptedException e) {
                logger.error("", e);
            }
        }
    }

    /**
     * @param tradeDt 交易日期
     * @return void
     * @description: 发送文件消息
     * @author: hongdong.xie
     * @date: 2020/1/15 15:19
     * @since JDK 1.7
     */
    private void sendFileMessage(String tradeDt) throws Exception {
        // 发送文件消息
        sendSingleMessage(tradeDt);
        //生成OK文件&发送
        sendOKMessage(tradeDt);
    }

    private void sendOKMessage(String tradeDt) throws Exception {
        //校验OK文件是否已经生成
        FundFileProcessRecPo processPo = fundFileProcessRecRepository.selectByUniqueKey(tradeDt, FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode(), Constant.ALL_TA_CODE);
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setFileName(getOkFileName(tradeDt));
        fileSdkPathInfo.setMiddlePath(getPath(tradeDt));
        fileSdkPathInfo.setBusinessCode(getBusinessCode());
        String absolutePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
        if (processPo == null) {
            processPo = addFundFileProcessRec(tradeDt, Constant.ALL_TA_CODE, FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode(), absolutePath);
        }
        if (!FileOpStatusEnum.MAKE_SUCCESSFUL.getKey().equals(processPo.getFileOpStatus())) {
            fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, processPo.getFileType(), processPo.getTaCode(), FileOpStatusEnum.PROCESSING.getKey(), processPo.getFileOpStatus());
            writeOkFile("1", tradeDt);
            fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, processPo.getFileType(), processPo.getTaCode(), FileOpStatusEnum.MAKE_SUCCESSFUL.getKey(), FileOpStatusEnum.PROCESSING.getKey());
        }

        if (ProcessStatusEnum.NOTICE_SUCCESS.getCode().equals(processPo.getProcessStatus())) {
            return;
        }

        //消息发送
        sendMessage(processPo.getTaTradeDt(), processPo.getTaCode(), processPo.getFileName(), HeExportFileTypeEnum.EXPORT_NA_FEE.getKey());
        //设置处理状态
        FundFileProcessRecPo rPo2 = new FundFileProcessRecPo();
        rPo2.setRecordNo(processPo.getRecordNo());
        rPo2.setProcessStatus(ProcessStatusEnum.NOTICE_SUCCESS.getCode());
        fundFileProcessRecRepository.updateByRecordNo(rPo2);
        OpsMonitor.warn(tradeDt + "NA费资产中心文件已推送", OpsMonitor.INFO);
    }

    private void sendSingleMessage(String tradeDt) {
        //5.检查所有TA是否生成成功
        Map<String, FundFileProcessRecPo> recMap = fundFileProcessRecRepository.getMapByFileTypeAndTaTradeDt(FileTypeEnum.EX_HIGH_FUND_CM_NA_FEE_FILE_TYPE.getCode(), tradeDt);
        FundFileProcessRecPo recPo = recMap.get(Constant.HOWBUY_CM_TA);
        //文件未生成成功
        if (recPo == null || !FileOptionStatus.CREATE_SUCCESS.getCode().equals(recPo.getFileOpStatus())) {
            throw new BatchException(ExceptionCodes.PARAMS_ERROR, "文件未生成或者生成失败");
        }
        //发送消息,只有未处理的才发送消息
        if (!ProcessStatusEnum.NOT_PROCESS.getCode().equals(recPo.getProcessStatus())) {
            logger.info("处理状态不是-未处理中{}", JSON.toJSONString(recPo));
            throw new BatchException(ExceptionCodes.PARAMS_ERROR, "处理状态不是-未处理中");
        }
        //消息发送
        boolean flag = sendMessage(recPo.getTaTradeDt(), recPo.getTaCode(), recPo.getFileName(), HeExportFileTypeEnum.EXPORT_NA_FEE.getKey());
        if (!flag) {
            logger.error("message send fail,tradeDt:{},taCode:{},fileName:{}", recPo.getTaTradeDt(), recPo.getTaCode(), recPo.getFileName());
        }
        //设置处理状态
        FundFileProcessRecPo rPo = new FundFileProcessRecPo();
        rPo.setRecordNo(recPo.getRecordNo());
        rPo.setProcessStatus(ProcessStatusEnum.NOTICE_SUCCESS.getCode());
        fundFileProcessRecRepository.updateByRecordNo(rPo);
    }

    /**
     * @description: 生成件任务
     * @author: hongdong.xie
     * @date: 2020/1/15 13:23
     * @since JDK 1.7
     */
    private class CrmNaTask implements Runnable {

        private FundFileProcessRecPo recPo;

        private String lastWorkDay;

        private CountDownLatch latch;

        public CrmNaTask(FundFileProcessRecPo recPo, String lastWorkDay, CountDownLatch latch) {
            this.recPo = recPo;
            this.latch = latch;
            this.lastWorkDay = lastWorkDay;
        }

        @Override
        public void run() {
            String tradeDt = recPo.getTaTradeDt();
            String fileType = recPo.getFileType();
            String taCode = recPo.getTaCode();
            String fileOpStatus = recPo.getFileOpStatus();
            try {
                //1.更新文件处理记录为处理中
                int count = fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, fileType, taCode, FileOpStatusEnum.PROCESSING.getKey(), fileOpStatus);
                if (count != 1) {
                    logger.info("updateFileOpStatusByPrimaryKeyWithVersion fail,tradeDt:{},fileType:{},taCode:{},newFileStatus:{},oldFileOpStatus:{}", tradeDt, fileType, taCode, FileOpStatusEnum.PROCESSING.getKey(), fileOpStatus);
                    return;
                }
                //2.文件生成
                addNewFile(recPo, lastWorkDay);
                //3.更新文件处理记录为生成成功
                count = fundFileProcessRecRepository.updateFileOpStatusByPrimaryKeyWithVersion(tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_SUCCESSFUL.getKey(), FileOpStatusEnum.PROCESSING.getKey());
                if (count != 1) {
                    logger.info("updateFileOpStatusByPrimaryKeyWithVersion fail,tradeDt:{},fileType:{},taCode:{},newFileStatus:{},oldFileOpStatus:{}", tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_SUCCESSFUL.getKey(), FileOpStatusEnum.PROCESSING.getKey());
                }
            } catch (Exception e) {
                logger.error("", e);
                //更新文件处理记录为生成失败
                fundFileProcessRecRepository.updateFileOpStatusByPrimaryKey(tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_FAILED.getKey());
                logger.info("updateFileOpStatusByPrimaryKey,tradeDt:{},fileType:{},taCode:{},fileStatus:{}", tradeDt, fileType, taCode, FileOpStatusEnum.MAKE_FAILED.getKey());
            } finally {
                latch.countDown();
            }
        }
    }

    /**
     * @description: 生成文件
     * @author: hongdong.xie
     * @date: 2020/1/15 14:06
     * @since JDK 1.7
     */
    private void generateFile(FundFileProcessRecPo recPo, String lastWorkDay) throws Exception {
        logger.info("generate trade ack file start,recPo:{},lastWorkDay:{}", JSON.toJSONString(recPo), lastWorkDay);
        //生成数据
        int totalCount = 0;
        exportCmNaReceivFeeRepository.deleteCmBatchFile(recPo.getTaTradeDt(), recPo.getTaCode());
        totalCount = exportCmNaReceivFeeRepository.addCmBatchFile(recPo.getTaTradeDt());

        //生成文件
        CreateAssetTxtFileParam createAssetTxtFileParam = new CreateAssetTxtFileParam();
        createAssetTxtFileParam.setTradeDt(recPo.getTaTradeDt());
        createAssetTxtFileParam.setFileFieldsName(fileFiledsName);
        createAssetTxtFileParam.setFileFields(fileFileds);
        createAssetTxtFileParam.setBusinessCode(getBusinessCode());
        createAssetTxtFileParam.setFilePath(getPath(recPo.getTaTradeDt()));
        createAssetTxtFileParam.setFileName(getFileNameByAbsolutePath(recPo.getFileName()));
        createAssetTxtFileParam.setTaCode(recPo.getTaCode());
        createAssetTxtFileParam.setTotalCount(totalCount);
        boolean flag = createTXTFile(createAssetTxtFileParam);
        if (!flag) {
            logger.error("createTXTFile>>failed>>>>>createAssetTxtFileParam{}", JSON.toJSONString(createAssetTxtFileParam));
        }
        logger.info("generate trade ack file end,createAssetTxtFileParam:{}", JSON.toJSONString(createAssetTxtFileParam));

    }


    @Override
    public boolean addNewFile(FundFileProcessRecPo recPo, String lastWorkDay) throws Exception {
        TxtFileExportContext txtFileExportContext = new TxtFileExportContext();
        txtFileExportContext.setRelationPath(getPath(recPo.getTaTradeDt()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("taCode", recPo.getTaCode());
        paramMap.put("taTradeDt", recPo.getTaTradeDt());
        paramMap.put("lastWorkDay", lastWorkDay);
        txtFileExportContext.setParams(paramMap);
        txtFileExportContext.setFileName(getFileNameByAbsolutePath(recPo.getFileName()));
        exportHighCrmNaFeeAssertFileService.process(txtFileExportContext);
        return true;
    }

    @Override
    public String getCheckFileName(String tradeDt, String taCode, String latestVersion) {
        return File.separator + tradeDt + "_v" + latestVersion + "_he_crm_na_fee_" + taCode + ".txt";
    }

    @Override
    public String getBusinessCode() {
        return FilePathStoreBusinessCodeConfig.NAFEE_CHECK_FILE_PATH;
    }

    @Override
    public String getPath(String tradeDt) {
        return File.separator + tradeDt;
    }

    @Override
    public String getOkFileName(String tradeDt) {
        return File.separator + tradeDt + "_he_crm_na_fee.txt.ok";
    }

    /**
     * @param tradeDt  交易日
     * @param taCode   TA代码
     * @param pageNo   页码
     * @param pageSize 每页大小
     * @return com.github.pagehelper.Page<com.howbuy.tms.batch.dao.po.order.ExportFundAckFileRecPo>
     * @description: 分页查询确认数据
     * @author: hongdong.xie
     * @date: 2020/1/14 18:14
     * @since JDK 1.7
     */
    @Override
    protected Page<ExportCmNaReceivFeePo> getData(final String tradeDt, final String taCode, final int pageNo, final int pageSize) {
        Page<ExportCmNaReceivFeePo> page = exportCmNaReceivFeeRepository.getByTradeDtTaCode(tradeDt, taCode, pageNo, pageSize);

        if (CollectionUtils.isEmpty(page)) {
            return null;
        }
        return page;
    }


}
