package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.high.batch.dao.po.batch.CmFileProcessRecPo;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.CmYjyzFileImportService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ParseYjyzFileTask extends AbstractHowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(ParseYjyzFileTask.class);

    private CmYjyzFileImportService cmYjyzFileImportService;
    private CmFileProcessRecPo cmFileProcessRecPo;
    private List<ThreadExceptionStatus> statusList;
    private String lastWorkDay;
    private String yjyzFilePathName;

    public ParseYjyzFileTask(CmFileProcessRecPo cmFileProcessRecPo, List<ThreadExceptionStatus> statusList, String lastWorkDay, CmYjyzFileImportService cmYjyzFileImportService, String yjyzFilePathName) {
        this.cmYjyzFileImportService = cmYjyzFileImportService;
        this.cmFileProcessRecPo = cmFileProcessRecPo;
        this.statusList = statusList;
        this.lastWorkDay = lastWorkDay;
        this.yjyzFilePathName = yjyzFilePathName;
    }

    @Override
    protected void callTask() {
        try {
            FileImportContext context = new FileImportContext();
            context.setFileName(yjyzFilePathName);
            context.setRelationPath(File.separator + cmFileProcessRecPo.getDealDt() + File.separator + cmFileProcessRecPo.getsVersionNo() + File.separator);
            context.setTaTradeDt(cmFileProcessRecPo.getDealDt());
            context.getParams().put("importDt", cmFileProcessRecPo.getDealDt());
            context.getParams().put("versionNo", cmFileProcessRecPo.getVersionNo().toString());
            context.getParams().put("lastWorkDay", lastWorkDay);
            cmYjyzFileImportService.process(context);
        } catch (Exception e) {
            ThreadExceptionStatus status = new ThreadExceptionStatus();
            status.setExsitException(true);
            status.setException(e);
            statusList.add(status);
            String msg = String.format("处理当天直销平衡因子文件异常,版本号:%s", cmFileProcessRecPo.getVersionNo());
            OpsMonitor.warn(msg, OpsMonitor.ERROR);
            logger.error("处理当天直销平衡因子文件异常:", e);
        }
    }
}