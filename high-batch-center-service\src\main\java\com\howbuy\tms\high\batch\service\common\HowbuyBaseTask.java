package com.howbuy.tms.high.batch.service.common;

import com.howbuy.trace.RequestChainTrace;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.CountDownLatch;

/**
 * @Description:基础任务
 * @Author: yun.lu
 * Date: 2024/7/5 11:07
 */
public abstract class HowbuyBaseTask implements Runnable {
    private static final Logger logger = LogManager.getLogger(HowbuyBaseTask.class);

    private CountDownLatch latch;


    public CountDownLatch getLatch() {
        return latch;
    }

    public void setLatch(CountDownLatch latch) {
        this.latch = latch;
    }

    @Override
    public void run() {
        String uuid = RequestChainTrace.getReqId();
        RequestChainTrace.buildAndSet(uuid, (String)null);
        try {
            // 然后执行具体的事件处理逻辑
            callTask();
        } catch (Exception e) {
            logger.error("异步任务调用出现异常,e:", e);
            throw e;
        } finally {
            if (latch != null) {
                latch.countDown();
            }
        }

    }

    /**
     * 执行task方法
     */
    protected abstract void callTask();

}
