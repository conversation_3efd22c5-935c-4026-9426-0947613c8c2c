package com.howbuy.tms.high.batch.service.job;

import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.enums.database.WorkdayTypeEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryidcardinfo.QueryIdCardInfoOuterService;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.utils.PartListUtil;
import com.howbuy.tms.high.batch.dao.po.batch.WorkdayPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.common.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.config.mq.MessageQueueConfig;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import com.howbuy.tms.high.batch.service.task.CheckCustomerIdCardExpireTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:校验用户身份证是否过期,每天一次,校验非当天上报的已支付订单
 * @Author: yun.lu
 * Date: 2025/1/23 10:23
 */
@Service
@Slf4j
public class CheckCustomerIdCardDayExpireJob extends BatchMessageProcessor {
    @Autowired
    private MessageQueueConfig messageQueueConfig;
    @Autowired
    private WorkdayService workdayService;
    @Autowired
    private QueryCustInfoOuterService queryCustInfoOuterService;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    @Override
    protected String getQuartMessageChannel() {
        return messageQueueConfig.getCheckCustomerIdCardExpireDayQueue();
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        WorkdayPo workdayPo = workdayService.getWorkay(WorkdayTypeEnum.SYS_TYPE);
        String workDay = workdayPo.getWorkday();
        log.info("校验用户身份证是否过期,每天一次,校验非当天上报的已支付订单,workDay={}", workDay);
        // 1.查询当天上报的认申购订单
        List<HighDealOrderDtlPo> highDealOrderDtlPoList = highDealOrderDtlRepository.queryNotSubmitBuyDealOrder(workDay);
        if (CollectionUtils.isEmpty(highDealOrderDtlPoList)) {
            log.info("没有需要上报的认申购订单,workDay={}", workDay);
            return;
        }
        //  2.分段并发处理订单
        List<CheckCustomerIdCardExpireTask> taskList = new ArrayList<>();
        List<List<HighDealOrderDtlPo>> partitionList = PartListUtil.partition(highDealOrderDtlPoList, 10);
        for (List<HighDealOrderDtlPo> dealOrderDtlPos : partitionList) {
            taskList.add(new CheckCustomerIdCardExpireTask(dealOrderDtlPos, queryCustInfoOuterService,queryHbOneNoOuterService, workDay));
        }
        howBuyRunTaskUil.runTask(taskList);
    }
}
