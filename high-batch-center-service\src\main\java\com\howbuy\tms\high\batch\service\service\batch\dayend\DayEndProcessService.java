/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.service.batch.dayend;


import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.high.batch.dao.po.batch.BusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaskDependDefCfgPo;
import com.howbuy.tms.high.batch.facade.enums.BusinessProcessingStepEnum;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.event.HighEventPublisher;
import com.howbuy.tms.high.batch.service.event.dayend.DayEndEvent;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.facade.trade.task.DayEndTask;
import com.howbuy.tms.high.batch.service.repository.DayEndProcessRepository;
import com.howbuy.tms.high.batch.service.repository.TaBusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.service.batch.base.AbstractProcessService;
import com.howbuy.tms.high.batch.service.service.order.dayend.HighDayEndService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description:日终处理服务类
 * @date 2017年7月10日 下午9:51:51
 * @since JDK 1.6
 */
@Service("dayEndProcessService")
public class DayEndProcessService extends AbstractProcessService {

    private static Logger logger = LogManager.getLogger(DayEndProcessService.class);

    @Autowired
    private DayEndProcessRepository dayEndProcessRepository;
    @Autowired
    private HighDayEndService highDayEndService;
    @Autowired
    private TaBusinessBatchFlowRepository taBusinessBatchFlowRepository;
    @Autowired
    private HighEventPublisher highEventPublisher;

    /**
     * flowCheck:流程检查
     */
    public void flowCheck(String tradeDt, String sysCode) {
        String taskId = BusinessProcessingStepEnum.BPS_DAY_END_PROCESS.getCode();
        // 流程任务项检查
        List<TaskDependDefCfgPo> taskPreDependList = taskDependDefCfgRepository.selectPreUnCompletedTask(taskId, tradeDt, sysCode);
        if (CollectionUtils.isNotEmpty(taskPreDependList)) {
            // 前置节点状态未完成，不能执行当前操作
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_PRE_UNCOMPLETED, "前置节点状态未完成，不能执行当前操作");
        }

        BusinessBatchFlowPo businessBatchFlowPo = businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(taskId, tradeDt, sysCode);
        if (businessBatchFlowPo == null) {
            // 当前任务不存在
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_BATCH_FLOW_ERROR, "当前任务不存在");
        }

        if (BatchStatEnum.PROCESSING.getKey().equals(businessBatchFlowPo.getBatchStat())) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_PROCESSING, "批处理节点正在执行中");
        }

        if (BatchStatEnum.PROCESS_SUCCESS.getKey().equals(businessBatchFlowPo.getBatchStat())) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_COMPLETED_UNREPEAT, "批处理节点已完成且不可重复执行");
        }

        // 更新为执行中
        String startTm = String.format("%1$tH%1$tM%1$tS", new Date());
        businessBatchFlowRepository.updateBatchStatus(tradeDt, taskId, BatchStatEnum.PROCESSING.getKey(), startTm, null, sysCode);

        clearTaskPool();
    }

    /**
     * execTask:执行日终任务
     *
     * @param tradeDt
     * @param sysCode
     * <AUTHOR>
     * @date 2017年7月11日 下午3:52:07
     */
    public void execDayEndProcess(String tradeDt, String sysCode) {
        // 删除历史备份数据
        delHisBackData(tradeDt, sysCode);
        // 备份数据
        backDate(tradeDt, sysCode);
        // 执行task
        callFacade(tradeDt, sysCode);
        // 数据清理
        clearData();
    }

    /**
     * delHisBackData:删除历史备份数据
     *
     * @param tradeDt
     * <AUTHOR>
     * @date 2017年7月21日 下午3:20:48
     */
    private void delHisBackData(String tradeDt, String sysCode) {
        // 清除上次备份的数据
        dayEndProcessRepository.deleteHisBankAcctPaymentCheck(tradeDt, sysCode);
        dayEndProcessRepository.deleteHisCxgPaymentCheck(tradeDt, sysCode);

    }

    /**
     * backDate:备份数据
     *
     * @param tradeDt
     * <AUTHOR>
     * @date 2017年7月11日 下午5:40:37
     */
    private void backDate(String tradeDt, String sysCode) {
        // 备份后台确认数据
        dayEndProcessRepository.insertHisHighFundAckFileRec();
        // 备份高端份额
        dayEndProcessRepository.insertHisHighFundVolFileRec(tradeDt);
        // 备份份额对账明细数据
        dayEndProcessRepository.insertHisHighFundVolDtlFileRec(tradeDt);
        // 备份支付对账数据、储蓄罐对账数据
        dayEndProcessRepository.insertHisBankAcctPaymentCheck(tradeDt, sysCode);
        dayEndProcessRepository.insertHisCxgPaymentCheck(tradeDt, sysCode);
        // 备份产品文件数据
        dayEndProcessRepository.insertHisFundFileProcessDtlFileRec();
    }

    /**
     * callFacade:callFacade
     *
     * @param tradeDt
     * <AUTHOR>
     * @date 2017年7月11日 下午5:50:21
     */
    private void callFacade(String tradeDt, String sysCode) {
        List<String> taCodes = queryTaCodes(tradeDt);
        // 客户处理:
        logger.info("taCodes = " + taCodes + "\t");
        // 处理
        List<String> failTa = process(taCodes, tradeDt, sysCode);
        if (CollectionUtils.isNotEmpty(failTa)) {
            logger.info("failTa = " + failTa);
            // 存在失败的线程
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_DAY_END_PROCESS_FAIL, "批处理执行失败！ 日终处理失败 failTa = " + failTa);
        }
    }

    private List<String> process(List<String> taCodeList, String tradeDt, String sysCode) {
        List<ThreadExceptionStatus> exList = new ArrayList<>();
        ThreadExceptionStatus exStatus = null;
        CountDownLatch latch = new CountDownLatch(taCodeList.size());
        for (String taCode : taCodeList) {
            exStatus = new ThreadExceptionStatus();
            exList.add(exStatus);
            exStatus.setTaCode(taCode);
            CommonThreadPool.execute(new DayEndTask(taCode, tradeDt, sysCode, highDayEndService,
                    exStatus, latch));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("latch await error.", e);
            Thread.currentThread().interrupt();
        }
        // 异常处理
        return processEx(exList);
    }

    private List<String> processEx(List<ThreadExceptionStatus> resultList) {
        List<String> failTaList = new ArrayList<>();
        for (ThreadExceptionStatus exStatus : resultList) {
            if (exStatus.isExsitException()) {
                failTaList.add(exStatus.getTaCode());
            }
        }

        return failTaList;
    }

    /**
     * clearData:清理数据
     *
     * <AUTHOR>
     * @date 2017年7月11日 下午5:49:00
     */
    private void clearData() {
        // 删除确认数据
        dayEndProcessRepository.truncateHighFundAckFileRec();
        // 删除份额对账数据
        dayEndProcessRepository.truncateHighFundVolCheckFileRec();
        // 删除份额明细对账数据
        dayEndProcessRepository.truncateHighVolDtlFileRec();
        // 删除产品文件明细数据
        dayEndProcessRepository.deleteFundFileProcessDtlRec();
    }

    /**
     * queryTaCodes:高端所有TA
     *
     * @return
     * <AUTHOR>
     * @date 2017年7月21日 下午2:19:38
     */
    private List<String> queryTaCodes(String tradeDt) {
        return taBusinessBatchFlowRepository.getTaCodes(tradeDt, BusinessProcessingStepEnum.BPS_ACK_PROCESS.getCode(), SysCodeEnum.BATCH_HIGH.getCode());
    }

    public void endoff(String tradeDt, String flowStat, String sysCode) {
        String taskId = BusinessProcessingStepEnum.BPS_DAY_END_PROCESS.getCode();
        String endTm = String.format("%1$tH%1$tM%1$tS", new Date());
        // 统计当前TA业务节点是否存在未处理成功的
        businessBatchFlowRepository.updateBatchStatus(tradeDt, taskId, flowStat, endTm, endTm, sysCode);
        if (BatchStatEnum.PROCESS_SUCCESS.getKey().equals(flowStat)) {
            // 发送ta确认处理结束事件
            highEventPublisher.publishEvent(new DayEndEvent(tradeDt));
            logger.info("高端批处理执行结束,当前TA日:{}", tradeDt);
            String msg = String.format("高端批处理执行结束,当前TA日%s", tradeDt);
            OpsMonitor.warn(msg, OpsMonitor.INFO);
        }
    }


}
