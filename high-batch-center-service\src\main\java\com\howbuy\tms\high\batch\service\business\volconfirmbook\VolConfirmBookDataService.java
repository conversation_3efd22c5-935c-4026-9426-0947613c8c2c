/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.volconfirmbook;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.VolConfirmBookStatusEnum;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.crm.nt.consultant.QueryConsInfoOuterService;
import com.howbuy.tms.common.outerservice.crm.nt.consultant.QueryConsInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.batch.dao.po.batch.HighVolConfirmBookPo;
import com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.service.common.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.repository.HighVolConfirmBookRepository;
import com.howbuy.tms.high.batch.service.repository.SimuFundCheckOrderRepository;
import com.howbuy.tms.high.batch.service.task.CreateConfirmPdfTask;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description:(份额确认书数据生成服务)
 * @reason:
 * @date 2018年3月26日 下午2:39:30
 * @since JDK 1.7
 */
@Service("volConfirmBookDataService")
public class VolConfirmBookDataService {

    private static final Logger logger = LogManager.getLogger(VolConfirmBookDataService.class);
    @Autowired
    private SimuFundCheckOrderRepository simuFundCheckOrderRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    public VolConfirmBookService volConfirmBookService;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private QueryConsInfoOuterService queryConsInfoOuterService;
    @Autowired
    private HighVolConfirmBookRepository highVolConfirmBookRepository;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;


    /**
     * execute:(生成份额确认书数据)
     *
     * @param tradeDt 工作日
     * <AUTHOR>
     * @date 2018年2月12日 上午10:48:50
     */
    public void execute(String tradeDt) {
        logger.info("VolConfirmBookDataService|execute|start..., tradeDt:{}", tradeDt);
        List<SimuFundCheckOrderDto> list = getNeedGenVolConfirmBookByAckImportDt(tradeDt);
        if (CollectionUtils.isEmpty(list)) {
            logger.info("VolConfirmBookDataService|execute|getByAckDtAndProductChannel() is empty,end.");
            return;
        }
        logger.info("VolConfirmBookDataService|execute|list Size:{}", list.size());
        // 查询协会对应的购买最大编号
        Map<String, Integer> buyMaxBookNoMap = highVolConfirmBookRepository.getBuyMaxBookNo();
        // 查询协会对应的赎回最大编号
        Map<String, Integer> redeemMaxBookNoMap = highVolConfirmBookRepository.getRedeemMaxBookNo();
        // 查询确认日期对应的份额确认书表的上报订单号set
        Set<String> existSubmitNoSet = highVolConfirmBookRepository.getExsitSubmitDealNo(tradeDt);
        // 查询新增股权产品信息
        List<String> peFundList = queryHighProductOuterService.queryAllDirectNewPeFundList();
        Map<String, HighProductBaseInfoBean> productMap = new HashMap<>();
        for (SimuFundCheckOrderDto simuFundCheckOrderPo : list) {
            setVolConfirmBookInfo(buyMaxBookNoMap, redeemMaxBookNoMap, existSubmitNoSet, peFundList, productMap, simuFundCheckOrderPo);
        }
        // 异步发起创建份额确认书
        howBuyRunTaskUil.runTask(new CreateConfirmPdfTask(volConfirmBookService));
        logger.info("VolConfirmBookDataService|execute|end...");
    }

    private void setVolConfirmBookInfo(Map<String, Integer> buyMaxBookNoMap, Map<String, Integer> redeemMaxBookNoMap, Set<String> existSubmitNoSet, List<String> peFundList, Map<String, HighProductBaseInfoBean> productMap, SimuFundCheckOrderDto simuFundCheckOrderPo) {
        Integer bookNo;
        HighVolConfirmBookPo highVolConfirmBookPo;
        HighProductBaseInfoBean highProductBaseBean;
        Map<String, Integer> bookNoMap;
        logger.info("VolConfirmBookDataService|execute|submitDealNo:{},mbusiCode:{}", simuFundCheckOrderPo.getSubmitDealNo(),
                simuFundCheckOrderPo.getmBusiCode());
        if (peFundList.contains(simuFundCheckOrderPo.getFundCode())) {
            logger.warn("VolConfirmBookDataService|execute fund:{} is new direct pe product, skip", simuFundCheckOrderPo.getFundCode());
            return;
        }
        if (BusinessCodeEnum.REDEEM.getMCode().equals(simuFundCheckOrderPo.getmBusiCode())) {
            bookNoMap = redeemMaxBookNoMap;
        } else {
            bookNoMap = buyMaxBookNoMap;
        }
        if (existSubmitNoSet.contains(simuFundCheckOrderPo.getSubmitDealNo())) {
            logger.warn("VolConfirmBookDataService|execute|HighProductBaseBean|submitDealNo:{} haven exist!", simuFundCheckOrderPo.getSubmitDealNo());
            return;
        }

        // 查询投顾信息
        QueryConsInfoResult queryConsInfoResult = getConsInfoResult(simuFundCheckOrderPo.getTxAcctNo());

        // 获取产品信息
        highProductBaseBean = getProductInfo(simuFundCheckOrderPo.getFundCode(), productMap);
        if (highProductBaseBean == null) {
            logger.error("VolConfirmBookDataService|execute|HighProductBaseBean|highProductBaseBean is null, fundCode:{}",
                    simuFundCheckOrderPo.getFundCode());
            return;
        }

        // 获取协会对应的编码
        bookNo = getBookNo(bookNoMap.get(highProductBaseBean.getAsocFundCode()), simuFundCheckOrderPo.getmBusiCode());
        logger.info("VolConfirmBookDataService|execute|HighProductBaseBean|sacFundCode:{}, bookNo:{}", highProductBaseBean.getAsocFundCode(), bookNo);
        if (bookNo == null) {
            logger.error("VolConfirmBookDataService|execute|HighProductBaseBean|bookNo is null, sacFundCode:{}", highProductBaseBean.getAsocFundCode());
            return;
        }

        // 创建份额确认书po
        highVolConfirmBookPo = createHighVolConfirmBookPo(simuFundCheckOrderPo, bookNo, highProductBaseBean, queryConsInfoResult);

        // 插入份额确认书数据
        int num = addHighVolConfirmBookPo(highVolConfirmBookPo);
        if (num < 1) {
            return;
        }

        // 更新已处理信息
        update(highProductBaseBean.getAsocFundCode(), bookNo, simuFundCheckOrderPo.getSubmitDealNo(), bookNoMap, existSubmitNoSet);
    }

    /**
     * @param txAcctNo
     * @return com.howbuy.tms.common.outerservice.crm.nt.consultant.QueryConsInfoResult
     * @Description 查询投顾信息
     * <AUTHOR>
     * @Date 2020/7/24 15:07
     **/
    private QueryConsInfoResult getConsInfoResult(String txAcctNo) {
        String hboneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
        if (StringUtils.isEmpty(hboneNo)) {
            logger.info("getConsInfoResult|txAcctNo:{}, hboneNo is empty ", txAcctNo);
        }

        QueryConsInfoResult queryConsInfoResult = queryConsInfoOuterService.queryConsInfo(hboneNo);

        return queryConsInfoResult;
    }

    /**
     * update:(更新已处理信息)
     *
     * @param sacFundCode
     * @param bookNo
     * @param submitDealNo
     * @param maxBookNoMap
     * @param existSubmitNoSet
     * <AUTHOR>
     * @date 2018年3月29日 下午2:48:14
     */
    private void update(String sacFundCode, Integer bookNo, String submitDealNo, Map<String, Integer> maxBookNoMap, Set<String> existSubmitNoSet) {
        Integer maxBookNo = maxBookNoMap.get(sacFundCode);
        if (maxBookNo == null || maxBookNo < bookNo) {
            maxBookNoMap.put(sacFundCode, bookNo);
        }

        existSubmitNoSet.add(submitDealNo);
    }

    /**
     * createHighVolConfirmBookPo:(创建份额确认书po)
     *
     * @param simuFundCheckOrderPo
     * @param bookNo
     * @param highProductBaseBean
     * @param queryConsInfoResult
     * @return
     * <AUTHOR>
     * @date 2018年3月26日 下午2:45:53
     */
    private HighVolConfirmBookPo createHighVolConfirmBookPo(SimuFundCheckOrderDto simuFundCheckOrderPo, Integer bookNo, HighProductBaseInfoBean highProductBaseBean, QueryConsInfoResult queryConsInfoResult) {
        HighVolConfirmBookPo highVolConfirmBookPo = new HighVolConfirmBookPo();
        BeanUtils.copyProperties(simuFundCheckOrderPo, highVolConfirmBookPo);
        String status = VolConfirmBookStatusEnum.NO_CREATE.getCode();
        if (BusinessCodeEnum.REDEEM.getMCode().equals(simuFundCheckOrderPo.getmBusiCode())) {
            status = VolConfirmBookStatusEnum.NO_NEED_CREATE.getCode();
        }
        highVolConfirmBookPo.setStatus(status);
        highVolConfirmBookPo.setBookNo(bookNo);
        highVolConfirmBookPo.setEstablishDt(highProductBaseBean.getEstablishDt());
        highVolConfirmBookPo.setSacFundCode(highProductBaseBean.getAsocFundCode());
        highVolConfirmBookPo.setFundName(highProductBaseBean.getFundName());
        // 投顾姓名
        highVolConfirmBookPo.setConsName(queryConsInfoResult.getConsName());
        // 投顾部门
        highVolConfirmBookPo.setOrgName(queryConsInfoResult.getOrgName());
        highVolConfirmBookPo.setCreateDtm(new Date());
        highVolConfirmBookPo.setUpdateDtm(new Date());

        // 合并单查询总金额
        if (YesOrNoEnum.YES.getCode().equals(simuFundCheckOrderPo.getMergeSubmitFlag())) {
            SimuFundCheckOrderPo total = simuFundCheckOrderRepository.getTotalAmountByMainDealNo(simuFundCheckOrderPo.getMainDealOrderNo());
            highVolConfirmBookPo.setAckAmt(total.getAckAmt());
            highVolConfirmBookPo.setAckVol(total.getAckVol());
            highVolConfirmBookPo.setFee(total.getFee());
            highVolConfirmBookPo.setVolByInterest(total.getVolByInterest());
        }

        // 利息转份额处理，如果确认金额 = 确认份额 * 净值 + 费用 ，则利息转份额为0
        BigDecimal ackAmt = highVolConfirmBookPo.getAckAmt();
        BigDecimal ackVol = highVolConfirmBookPo.getAckVol();
        BigDecimal nav = highVolConfirmBookPo.getNav();
        BigDecimal fee = highVolConfirmBookPo.getFee();
        if (ackAmt.compareTo(ackVol.multiply(nav).add(fee)) == 0) {
            highVolConfirmBookPo.setVolByInterest(BigDecimal.ZERO);
        }


        return highVolConfirmBookPo;
    }

    /**
     * getBookNo:(获取协会对应的编号)
     *
     * @param maxBookNo
     * @param mBusiCode
     * @return
     * <AUTHOR>
     * @date 2018年3月26日 下午2:35:04
     */
    private Integer getBookNo(Integer maxBookNo, String mBusiCode) {
        if (maxBookNo == null) {
            maxBookNo = 0;
        }

        if (BusinessCodeEnum.REDEEM.getMCode().equals(mBusiCode)) {
            if (maxBookNo < 10000) {
                maxBookNo = 60000 + maxBookNo;
            } else {
                maxBookNo = 60000 + maxBookNo % 10000;
            }
        } else {
            if (maxBookNo < 10000) {
                maxBookNo = 10000 + maxBookNo;
            }
        }

        // 递增+1
        return ++maxBookNo;
    }

    /**
     * getProductInfo:(获取产品信息)
     *
     * @param fundCode
     * @param productMap
     * @return
     * <AUTHOR>
     * @date 2018年3月26日 下午1:57:06
     */
    private HighProductBaseInfoBean getProductInfo(String fundCode, Map<String, HighProductBaseInfoBean> productMap) {
        if (productMap.containsKey(fundCode)) {
            return productMap.get(fundCode);
        }

        HighProductBaseInfoBean bean = queryHighProductOuterService.getHighProductBaseInfo(fundCode);
        if (bean == null) {
            logger.info("VolConfirmBookDataService|execute|HighProductBaseBean|fundCode:" + fundCode + " bean is empty!");
            return null;
        }

        // 放入产品map
        productMap.put(fundCode, bean);
        return bean;
    }

    /**
     * addHighVolConfirmBookPo:(插入份额确认书)
     *
     * @param highVolConfirmBookPo
     * <AUTHOR>
     * @date 2018年3月26日 下午2:57:53
     */
    private int addHighVolConfirmBookPo(HighVolConfirmBookPo highVolConfirmBookPo) {
        logger.info("VolConfirmBookDataService|addHighVolConfirmBookPo|highVolConfirmBookPo:{}", JSON.toJSONString(highVolConfirmBookPo));
        int num = 0;
        try {
            num = highVolConfirmBookRepository.insert(highVolConfirmBookPo);
            logger.info("VolConfirmBookDataService|addHighVolConfirmBookPo|insert num:{}", num);
        } catch (Exception e) {
            logger.error("VolConfirmBookDataService|addHighVolConfirmBookPo|error msg:{}", e.getMessage(), e);
        }
        return num;
    }

    /**
     * getByAckImportDtAndProductChannel:(查询需生成份额确认书的当日确认订单)
     *
     * @param ackImportDt
     * @return
     * <AUTHOR>
     * @date 2018年2月22日 下午4:37:05
     */
    private List<SimuFundCheckOrderDto> getNeedGenVolConfirmBookByAckImportDt(String ackImportDt) {
        return simuFundCheckOrderRepository.getNeedGenVolConfirmBookByAckImportDt(ackImportDt);
    }
}
