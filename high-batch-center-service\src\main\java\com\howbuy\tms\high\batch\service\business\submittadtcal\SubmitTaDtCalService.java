/**
 * Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.batch.service.business.submittadtcal;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.high.batch.dao.po.batch.BusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaBusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.WorkdayPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.exception.NotAllOrderPaidException;
import com.howbuy.tms.high.batch.service.repository.TaBusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.service.batch.businessbatchflow.BusinessBatchFlowService;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:(上报Ta日期日计算)
 * @reason:
 * <AUTHOR>
 * @date 2018年5月28日 下午1:36:55
 * @since JDK 1.7
 */
@Service("submitTaDtCalService")
public class SubmitTaDtCalService {
    private static org.apache.logging.log4j.Logger logger = LogManager.getLogger(SubmitTaDtCalService.class);

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    @Autowired
    private BusinessBatchFlowService businessBatchFlowService;

    @Autowired
    private WorkdayService workdayService;

    @Autowired
    private TaBusinessBatchFlowRepository taBusinessBatchFlowRepository;

    /**
     * cal:(上报Ta日计算服务)
     *
     * @param dealDtlNo
     * @return
     * <AUTHOR>
     * @date 2018年5月28日 下午1:38:44
     */
    public String cal(String dealDtlNo) {
        try {
            if (StringUtils.isEmpty(dealDtlNo)) {
                logger.error("SubmitTaDtCalService|cal|dealDtlNo is null!");
                throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_PARAM_ERROR);
            }

            // 获取订单明细信息
            HighDealOrderDtlPo highDealOrderDtlPo = getHighDealOrderDtl(dealDtlNo);
            if (highDealOrderDtlPo == null) {
                logger.error("SubmitTaDtCalService|cal|dealDtlNo:{}, highDealOrderDtlPo is null!", dealDtlNo);
                throw new BusinessException(ExceptionCodes.HIGH_BATCH_DEAL_NOT_EXIST);
            }

            if (YesOrNoEnum.YES.getCode().equals(highDealOrderDtlPo.getMergeSubmitFlag())) {
                // 统计非付款成功的订单
                int n = countNotPaySuccessSubOrders(highDealOrderDtlPo.getMainDealOrderNo());
                if (n > 0) {
                    // 合并单没有全部支付完成，不更新上报日
                    logger.info("SubmitTaDtCalService|cal|mainDealOrderNo:{} 合并单没有全部支付完成，不更新上报日", highDealOrderDtlPo.getMainDealOrderNo());
                    throw new NotAllOrderPaidException();
                }
            }

            // 关键属性非空校验
            String productChannel = highDealOrderDtlPo.getProductChannel();
            String mBusiCode = highDealOrderDtlPo.getmBusiCode();
            String submitTaDt = highDealOrderDtlPo.getSubmitTaDt();
            String taTradeDt = highDealOrderDtlPo.getTaTradeDt();
            String supportAdvanceFlag = highDealOrderDtlPo.getSupportAdvanceFlag();
            String submitTaDtInterposeFlag = highDealOrderDtlPo.getSubmitTaDtInterposeFlag();
            if (InterposeFlagEnum.DONE.getCode().equals(submitTaDtInterposeFlag)) {
                logger.warn("SubmitTaDtCalService|cal|dealDtlNo:{}, submitTaDtInterposeFlag is done! return oldsubmitTaDt", dealDtlNo);
                return submitTaDt;
            }
            if (StringUtils.isEmpty(productChannel) || StringUtils.isEmpty(mBusiCode) || StringUtils.isEmpty(taTradeDt)
                    || StringUtils.isEmpty(submitTaDt) || StringUtils.isEmpty(supportAdvanceFlag)) {
                logger.error("SubmitTaDtCalService|cal|dealDtlNo:{}, exist param is null, info:{}", dealDtlNo, JSON.toJSONString(highDealOrderDtlPo));
                throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_PARAM_ERROR);
            }

            // 认申购预约交易
            if (BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode)
                    || (BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode)
                    && (IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)
                    || IsScheduledTradeEnum.SupportBuyAdvance.getCode().equals(supportAdvanceFlag)))) {
                // 根据原上报TA日, 获取对应的开放日历信息
                ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByOpenDate(highDealOrderDtlPo.getFundCode(),
                        highDealOrderDtlPo.getFundShareClass(), highDealOrderDtlPo.getDisCode(), submitTaDt, "0");
                if (productAppointmentInfoBean == null) {
                    logger.error("SubmitTaDtCalService|cal|dealDtlNo:{}, productAppointmentInfoBean is null!", dealDtlNo);
                    throw new BusinessException(ExceptionCodes.HIGH_BATCH_APPOINTMENT_NOT_EXIST);
                }

                // 获取高端系统工作日
                WorkdayPo workdayPo = workdayService.getWorkay(WorkdayTypeEnum.SYS_TYPE);
                if (workdayPo == null) {
                    logger.error("SubmitTaDtCalService|cal|dealDtlNo:{}, workdayPo is null!", dealDtlNo);
                    throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_PARAM_ERROR);
                }

                String currWorkDt = null;
                // 下单申请taTradeDt小于当前系统工作日, 以系统工作日为准
                if (taTradeDt.compareTo(workdayPo.getWorkday()) <= 0) {
                    // 高端对应通道的交易申请日终处理状态
                    BusinessBatchFlowPo businessBatchFlowPo = businessBatchFlowService.getCurrBatchFlowByTaskId(BusinessProcessingStepEnum.BPS_TRADE_APP_DAY_END.getCode(), SysCodeEnum.BATCH_HIGH.getCode());
                    if (businessBatchFlowPo == null) {
                        logger.error("SubmitTaDtCalService|cal|dealDtlNo:{}, businessBatchFlowPo is null!", dealDtlNo);
                        throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_PARAM_ERROR);
                    }

                    // TA对应交易申请日终状态
                    TaBusinessBatchFlowPo taBusinessBatchFlowPo = taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(highDealOrderDtlPo.getTaCode(),
                            SysCodeEnum.BATCH_HIGH.getCode(), workdayPo.getWorkday(),
                            BusinessProcessingStepEnum.BPS_TRADE_APP_DAY_END.getCode());
                    if (taBusinessBatchFlowPo == null) {
                        logger.error("SubmitTaDtCalService|cal|dealDtlNo:{}, taBusinessBatchFlowPo is null!", dealDtlNo);
                        throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_PARAM_ERROR);
                    }
                    // 执行中, 执行失败, 执行成功
                    if (appEndHasProcess(businessBatchFlowPo.getBatchStat(), taBusinessBatchFlowPo.getFlowStat())) {
                        currWorkDt = workdayPo.getNextWorkday();
                    } else {
                        currWorkDt = workdayPo.getWorkday();
                    }
                } else {
                    // 下单申请taTradeDt大于等于当前系统工作日, 以下单申请TaTradeDt为准
                    currWorkDt = taTradeDt;
                }

                // currWorkDt 小于 openStartDt, 则submitTaDt = openStartDt 否则 submitTaDt = currWorkDt
                if (currWorkDt.compareTo(productAppointmentInfoBean.getOpenStartDt()) < 0) {
                    submitTaDt = productAppointmentInfoBean.getOpenStartDt();
                } else {
                    submitTaDt = currWorkDt;
                }

                // 上报TA日大于开放截止日, 抛出异常
                if (submitTaDt.compareTo(productAppointmentInfoBean.getOpenEndDt()) > 0) {
                    String msg = "上报TA日大于开放截止日,dealNo:" + highDealOrderDtlPo.getDealNo() + "计算得出的上报日:" + submitTaDt + "开放截止日:" + productAppointmentInfoBean.getOpenEndDt();
                    OpsMonitor.warn(msg, OpsMonitor.ERROR);
                    logger.error("SubmitTaDtCalService|cal|dealDtlNo:{}, submitTaDt over openEndDt, submitTaDt:{}, openEndDt:{}",
                            new Object[]{dealDtlNo, submitTaDt, productAppointmentInfoBean.getOpenEndDt()});
                    throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR);
                }
            }

            logger.info("SubmitTaDtCalService|cal|finished, dealDtlNo:{}, newSubmitTaDt:{}", dealDtlNo, submitTaDt);
            return submitTaDt;
        } catch (Exception e) {
            logger.error("SubmitTaDtCalService|cal|error, msg:{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * getHighDealOrderDtl:(查询订单明细信息)
     *
     * @param dealDtlNo
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 上午10:24:25
     */
    private HighDealOrderDtlPo getHighDealOrderDtl(String dealDtlNo) {
        HighDealOrderDtlPo highDealOrderDtlPo = null;
        try {
            highDealOrderDtlPo = highDealOrderDtlRepository.selectByDealDtlNo(dealDtlNo);
        } catch (Exception e) {
            logger.error("SubmitTaDtCalService|cal|getHighDealOrderDtl error, msg:{}", e.getMessage(), e);
        }
        return highDealOrderDtlPo;

    }

    /**
     * 统计未支付成功的子订单数量
     * @param mainDealNo
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/6/8 10:24
     * @since JDK 1.8
     */
    private int countNotPaySuccessSubOrders(String mainDealNo) {
        return highDealOrderDtlRepository.countNotPaySuccessSubOrders(mainDealNo);
    }

    /**
     *
     * @Description 交易申请日终，或者TA交易申请日终已处理
     * @param batchStat 交易申请日终状态
     * @param taFlowStat TA交易申请日终状态
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Date 2020/10/26 17:07
     **/
    private Boolean appEndHasProcess(String batchStat, String taFlowStat) {
        // 执行中, 执行失败, 执行成功
        return !BatchStatEnum.NON_PROCESS.getKey().equals(taFlowStat) || !BatchStatEnum.NON_PROCESS.getKey().equals(batchStat);
    }

}

