/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.finadirectionmonitor;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.ProductTypeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.high.batch.dao.po.batch.FinaFileProcessRecPo;
import com.howbuy.tms.high.batch.dao.po.batch.HighFinaDirectionFileRecPo;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.business.finafilemonitor.AbstractFinaFileService;
import com.howbuy.tms.high.batch.service.business.message.FinaFileMessageBean;
import com.howbuy.tms.high.batch.service.common.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.repository.HighFinaDirectionFileRecRepository;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.FinaDirectionFileImportService;
import com.howbuy.tms.high.batch.service.task.FinaDirectionFileTask;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description:资金储蓄罐下发文件
 * @reason:
 * @date 2018年5月30日 下午2:07:59
 * @since JDK 1.6
 */
@Service("finaDirectionFileProcessor")
public class DirectionFinaFileProcessor extends AbstractFinaFileService {
    private final Logger logger = LogManager.getLogger(DirectionFinaFileProcessor.class);

    @Autowired
    private HighFinaDirectionFileRecRepository highFinaDirectionFileRecRepository;

    @Autowired
    private FinaDirectionFileRecPoProcessor finaDirectionFileRecPoProcessor;
    @Autowired
    private FinaDirectionFileImportService finaDirectionFileImportService;

    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    @Override
    public boolean userThis(String code) {
        return "TA_SETTLE_TYPE".equals(code) || "UPDATE_SETTLE_TYPE".equals(code);
    }

    @Override
    public void process(FinaFileMessageBean finaFileMessageBean) {
        logger.info("FinaDirectionFileProcessor|process start");
        FinaFileProcessRecPo finaFileProcessRecPo = new FinaFileProcessRecPo();
        try {
            // 消息数据落库
            preFileProcess(finaFileMessageBean, finaFileProcessRecPo);
            // 解析并保存文件内容
//            parseFile(finaFileMessageBean, finaFileProcessRecPo.getRecordNo());
            finaDirectionFileImport(finaFileMessageBean, finaFileProcessRecPo);

        } catch (Exception e) {
            if (!StringUtils.isEmpty(finaFileProcessRecPo.getRecordNo())) {
                finaFileProcessRecPo.setFileOpStatus(FileOpStatusEnum.IMPORT_FAILED.getKey());
            }
            logger.error("FinaDirectionFileProcessor|process|msg:{}", e.getMessage(), e);
        } finally {
            if (!StringUtils.isEmpty(finaFileProcessRecPo.getRecordNo())) {
                if (!FileOpStatusEnum.IMPORT_FAILED.getKey().equals(finaFileProcessRecPo.getFileOpStatus())) {
                    finaFileProcessRecPo.setFileOpStatus(FileOpStatusEnum.IMPORT_SUCCESSFUL.getKey());
                }
                // 更新文件处理状态
                afterFileProcess(finaFileProcessRecPo);
            }
            logger.info("FinaDirectionFileProcessor|process end,recordNo:{} processStatus:{}", finaFileProcessRecPo.getRecordNo(), finaFileProcessRecPo.getProcessStatus());
        }

        // 异步更新回款方向操作
        dataExecute(finaFileMessageBean.getExportDt());
    }

    private void finaDirectionFileImport(FinaFileMessageBean finaFileMessageBean, FinaFileProcessRecPo finaFileProcessRecPo) throws Exception {
        FileImportContext fileImportContext = new FileImportContext();
        fileImportContext.setFileName(finaFileMessageBean.getFileName());
        fileImportContext.setRelationPath(File.separator + finaFileMessageBean.getExportDt() + File.separator);
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("ackDt", finaFileMessageBean.getExportDt());
        paramMap.put("dataSourceType", getDataSourceType(finaFileMessageBean.getDisFileType()));
        paramMap.put("batchNo", finaFileProcessRecPo.getRecordNo());
        paramMap.put("now", new Date());
        fileImportContext.setParams(paramMap);
        finaDirectionFileImportService.process(fileImportContext);
    }

    private void dataExecute(String tradeDt) {
        FinaDirectionFileTask finaDirectionFileTask = new FinaDirectionFileTask(finaDirectionFileRecPoProcessor, tradeDt);
        howBuyRunTaskUil.runTask(finaDirectionFileTask);
    }

    /**
     * buildFinaDirectionFileRecPo:回款方向
     *
     * @param recDtl
     * @param ackDt
     * @param date
     * @return
     * <AUTHOR>
     * @date 2018年6月15日 下午1:44:52
     */
    @Override
    public HighFinaDirectionFileRecPo build(String[] recDtl, String ackDt, Date date, String dataSourceType, String batchNo) {
        // 只处理专户和私募,小集合的数据
        if (!ProductTypeEnum.ZHUANHU.getCode().equals(recDtl[1]) &&
                !ProductTypeEnum.QUANSHANG_XIAOJIHE.getCode().equals(recDtl[1]) &&
                !ProductTypeEnum.SM.getCode().equals(recDtl[1])) {
            return null;
        }
        HighFinaDirectionFileRecPo po = new HighFinaDirectionFileRecPo();
        // 分销确认流水号|基金类型(7-专户 11-私募)|回款方向0-客户银行卡、1-客户可用余额、3-储蓄罐、4-不出款
        po.setAckDt(ackDt);
        po.setDisDealAckNo(StringUtils.trimToNull(recDtl[0]));
        po.setProductType(StringUtils.trimToNull(recDtl[1]));
        po.setDirection(StringUtils.trimToNull(recDtl[2]));
        po.setProcessStatus(YesOrNoEnum.NO.getCode());
        po.setDataSourceType(dataSourceType);
        po.setBatchNo(batchNo);
        po.setCreateDtm(date);
        po.setUpdateDtm(date);
        return po;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void insert(List<?> list) {
        highFinaDirectionFileRecRepository.batchInsert((List<HighFinaDirectionFileRecPo>) list);
    }

    @Override
    public void validate(String[] recDtl) {
        if (recDtl.length < 3) {
            logger.error("资金回款文件格式错误：{}", JSON.toJSONString(recDtl));
            // 文件格式错误
            throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_INVALID_FINA_DIRECTION_FILE);
        }
    }

}
