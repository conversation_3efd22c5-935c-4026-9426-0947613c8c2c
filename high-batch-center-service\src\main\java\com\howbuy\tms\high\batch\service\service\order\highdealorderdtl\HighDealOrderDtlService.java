/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.service.order.highdealorderdtl;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.NotifySubmitFlagEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.service.business.ordersubmit.refreshnotifystatus.OldOrderInfoBean;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.repository.CustBooksDtlRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.PaymentOrderRepository;
import com.howbuy.tms.high.batch.service.repository.SimuFundCheckOrderRepository;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Description:订单明细处理类
 *
 * <AUTHOR>
 * @date 2017年3月29日 上午9:32:39
 * @since JDK 1.7
 */
@Service("highDealOrderDtlService")
public class HighDealOrderDtlService {
    private static final Logger logger = LogManager.getLogger();
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private PaymentOrderRepository paymentOrderRepository;
    @Autowired
    private CustBooksDtlRepository custBooksDtlRepository;
    @Autowired
    private SimuFundCheckOrderRepository simuFundCheckOrderRepository;


    /**
     * updateNotifyStatus:(更新订单状态)
     *
     * @param dealDtlNo
     * @param notifyFlag
     * @param submitTaDt
     * @param assetStatus
     * @param oldUpdateDtm
     * @param oldSubmitTaDt
     * @param oldNotifyFlag
     * @param oldAssetStatus
     * <AUTHOR>
     * @date 2018年5月29日 下午7:08:43
     */
    public void updateNotifyStatus(String dealDtlNo, String notifyFlag, String submitTaDt, String assetStatus, Date oldUpdateDtm,
                                   String oldSubmitTaDt, String oldNotifyFlag, String oldAssetStatus, Date oldRetrieveDtm, Date retrieveDtm, String dealNo) {
        // 更新的高端订单明细
        int affectNum = highDealOrderDtlRepository.updateNotifyStatus(dealDtlNo, notifyFlag, submitTaDt, assetStatus, oldUpdateDtm,
                oldSubmitTaDt, oldNotifyFlag, oldAssetStatus, oldRetrieveDtm, retrieveDtm);
        if (0 == affectNum) {
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_NOTIFY_FLAG_FAILED,
                    MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_APP_FLAG_FAILED));
        }

        if (StringUtils.isNotBlank(submitTaDt) && StringUtils.isNotBlank(dealDtlNo)) {
            // 若转换日期大于上报日期，更新转换日期为上报日期
            int updatePmtCount = paymentOrderRepository.updatePmtDtAfterSubmitTaDt(dealNo, submitTaDt, new Date());
            logger.info("HighDealOrderDtlService|updateNotifyStatus|dealNo:{}, update pmtDt succCount:{}", dealNo, updatePmtCount);
        }

        // 更新账本明细表的taTradeDt
        if (!StringUtils.isEmpty(submitTaDt) && !submitTaDt.equals(oldSubmitTaDt)) {
            int num = custBooksDtlRepository.updateTaTradeDt(dealDtlNo, submitTaDt, oldSubmitTaDt);
            if (0 == num) {
                throw new BusinessException(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_NOTIFY_FLAG_FAILED,
                        MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_APP_FLAG_FAILED));
            }
        }
    }

    /**
     * 批量更新订单状态
     *
     * @param result
     * @param orders
     * @param oldOrderInfoMap
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/6/10 14:01
     * @since JDK 1.8
     */
    public void updateNotifyStatus(boolean result, List<HighDealOrderDtlPo> orders, Map<String, OldOrderInfoBean> oldOrderInfoMap) {
        for (HighDealOrderDtlPo po : orders) {
            OldOrderInfoBean old = oldOrderInfoMap.get(po.getDealNo());
            if (result) {
                logger.info("HighDealOrderDtlService|updateNotifyStatus|dealDtlNo:{}, NotifySubmitFlag:{}, SubmitTaDt:{}, AssetcertificateStatus:{}",
                        new Object[]{po.getDealDtlNo(), po.getNotifySubmitFlag(), po.getSubmitTaDt(), po.getAssetcertificateStatus()});
                // 检查通过更新通知上报状态、上报日、资产证明状态
                updateNotifyStatus(po.getDealDtlNo(), po.getNotifySubmitFlag(), po.getSubmitTaDt(), po.getAssetcertificateStatus(),
                        po.getUpdateDtm(), old.getSubmitTaDt(), old.getNotifySubmitFlag(), old.getAssetStatus(), null, null, po.getDealNo());

            } else if (po.getRetrieveDtm() != null) {
                logger.info("HighDealOrderDtlService|updateNotifyStatus|dealDtlNo:{}, RetrieveDtm:{}", po.getDealDtlNo(), po.getRetrieveDtm());
                // 检查未通过，资产证明状态变有效后更新资产证明状态
                updateNotifyStatus(po.getDealDtlNo(), null, null, po.getAssetcertificateStatus(), po.getUpdateDtm(), null, null,
                        old.getAssetStatus(), old.getRetrieveDtm(), po.getRetrieveDtm(), null);
            }
        }
    }


    /**
     * updateInterposeFlagAndSubmitTaDt:更新干预状态和上报ta日期
     *
     * @param po
     * @param now
     * @param submitTaDtOld
     * @param upFlag
     * @return
     * <AUTHOR>
     * @date 2018年6月7日 下午5:03:29
     */
    public int updateInterposeFlagAndSubmitTaDt(HighDealOrderDtlPo po, Date now, String submitTaDtOld, boolean upFlag) {
        int count = highDealOrderDtlRepository.updateInterposeFlagByDealNo(po.getDealNo(), po.getAssetInterposeFlag(), po.getDualentryInterposeFlag(), po.getCallbackInterposeFlag(), po.getCalmdtmInterposeFlag(), now, po.getSubmitTaDt(), submitTaDtOld, po.getSubmitTaDtInterposeFlag(), po.getRetrieveDtm());
        // 上报日变化才需更新账本明细和上报表
        if (upFlag) {
            custBooksDtlRepository.updateTaTradeDt(po.getDealDtlNo(), po.getSubmitTaDt(), submitTaDtOld);
            if (NotifySubmitFlagEnum.HAS_BEEN_NOTIFY.getCode().equals(po.getNotifySubmitFlag())) {
                simuFundCheckOrderRepository.updateSubmitTaDtByDealNo(po.getDealNo(), po.getSubmitTaDt(), submitTaDtOld, now);
            }
        }
        return count;
    }

    /**
     * 批量更新干预状态和上报ta日期
     *
     * @param newPoList
     * @param now
     * @param submitTaDtOld
     * @param upFlag
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/6/2 10:20
     * @since JDK 1.8
     */
    public int updateInterposeFlagAndSubmitTaDt(List<HighDealOrderDtlPo> newPoList, Date now, String submitTaDtOld, boolean upFlag) {
        int count = 0;
        for (HighDealOrderDtlPo newPo : newPoList) {
            count += updateInterposeFlagAndSubmitTaDt(newPo, now, submitTaDtOld, upFlag);
        }
        return count;
    }

    /**
     * 更新干预状态和上报ta日期
     *
     * @param orderList
     * @param now
     * @param oldSubmitDtMap
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/4/9 11:07
     * @since JDK 1.8
     */
    public int updateInterposeFlagAndSubmitTaDt(List<HighDealOrderDtlPo> orderList, Date now, Map<String, String> oldSubmitDtMap) {
        int count = 0;
        for (HighDealOrderDtlPo order : orderList) {
            count += updateInterposeFlagAndSubmitTaDt(order, now, oldSubmitDtMap.get(order.getDealNo()), true);
        }
        return count;
    }

}

