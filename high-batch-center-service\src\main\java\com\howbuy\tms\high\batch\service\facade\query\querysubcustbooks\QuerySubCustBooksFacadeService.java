/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 * <p>
 * Copyright (c) 2016, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 * <p>
 * Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 * <p>
 * Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 * <p>
 * Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 *Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.query.querysubcustbooks;

import com.github.pagehelper.Page;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.QueryTaInfoOuterService;
import com.howbuy.tms.high.batch.dao.po.order.SubCustBooksPo;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.QuerySubCustBooksFacade;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.QuerySubCustBooksRequest;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.QuerySubCustBooksResponse;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.bean.QuerySubCustBooksCondition;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.bean.SubCustBooksBean;
import com.howbuy.tms.high.batch.service.common.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.facade.AbstractService;
import com.howbuy.tms.high.batch.service.repository.SubCustBooksRepository;
import com.howbuy.tms.high.batch.service.task.BuildTaInfoMapTask;
import com.howbuy.tms.high.batch.service.task.SubCustBookQueryFundInfoTask;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 * @description:查询子账本
 * <AUTHOR>
 * @date 2018年9月20日 下午4:53:58
 * @since JDK 1.6
 */
@DubboService
@Service("querySubCustBooksFacade")
public class QuerySubCustBooksFacadeService extends AbstractService<QuerySubCustBooksRequest, QuerySubCustBooksResponse>
        implements QuerySubCustBooksFacade {
    private static Logger logger = LogManager.getLogger(QuerySubCustBooksFacadeService.class);
    @Autowired
    private SubCustBooksRepository subCustBooksRepository;
    @Autowired
    private QueryTaInfoOuterService queryTaInfoOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;
    /**
     * 每次查询量
     */
    private static int maxQrySize = 50;

    @Override
    public QuerySubCustBooksResponse process(QuerySubCustBooksRequest request) {
        QuerySubCustBooksResponse resp = new QuerySubCustBooksResponse();

        /* 参数 */
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        // 查询条件
        QuerySubCustBooksCondition queryCondition = request.getQuerySubCustBooksCondition();
        if (queryCondition == null) {
            throw new ValidateException(ExceptionCodes.PARAMS_ERROR, MessageSource.getMessageByCode(ExceptionCodes.PARAMS_ERROR));
        }

        // 代销场检
        if (YesOrNoEnum.NO.getCode().equals(queryCondition.getIsHBJGAuth())) {
            queryCondition.setFilterFundCodeList(queryHighProductOuterService.queryNotHBJGFundListService());
        }

        // 查询产品中心得到tainfomap和fundinfomap
        Page<SubCustBooksPo> subCustBooksPoPage = getSubCustBooksPoPage(queryCondition, pageNo, pageSize);
        Map<String, String> taInfoMap = getTaInfoMap();
        Map<String, String> fundInfoMap = getFundInfoMap();

        /* 转换 */
        SubCustBooksBean subCustBooksBean = null;
        List<SubCustBooksBean> subCustBooksBeanList = new ArrayList<SubCustBooksBean>();
        for (SubCustBooksPo po : subCustBooksPoPage.getResult()) {
            subCustBooksBean = new SubCustBooksBean();
            BeanUtils.copyProperties(po, subCustBooksBean);
            subCustBooksBean.setFundName(fundInfoMap.get(subCustBooksBean.getFundCode()));
            subCustBooksBean.setTaName(taInfoMap.get(subCustBooksBean.getTaCode()));
            subCustBooksBeanList.add(subCustBooksBean);
        }
        resp.setSubCustBooksBeanList(subCustBooksBeanList);

        resp.setPageNo(subCustBooksPoPage.getPageNum());
        resp.setTotalCount(subCustBooksPoPage.getTotal());
        resp.setTotalPage(subCustBooksPoPage.getPages());

        resp.setReturnCode(ExceptionCodes.SUCCESS);
        return resp;
    }

    /**
     *
     * getSubCustBooksPoPage:查询子账本
     * @param queryCondition
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2018年9月20日 下午5:10:31
     */
    private Page<SubCustBooksPo> getSubCustBooksPoPage(QuerySubCustBooksCondition queryCondition, Integer pageNo, Integer pageSize) {
        return subCustBooksRepository.getSubCustBooksForConsole(queryCondition, pageNo, pageSize);
    }

    private Map<String, String> getTaInfoMap() {
        Map<String, String> map = new ConcurrentHashMap<>(2);
        List<BuildTaInfoMapTask> taskList = new ArrayList<>();
        taskList.add(new BuildTaInfoMapTask(queryTaInfoOuterService, map, ProductChannelEnum.HIGH_FUND.getCode()));
        taskList.add(new BuildTaInfoMapTask(queryTaInfoOuterService, map, ProductChannelEnum.TP_SM.getCode()));
        howBuyRunTaskUil.runTask(taskList);
        return map;
    }

    private Map<String, String> getFundInfoMap() {
        Map<String, String> fundMap = new ConcurrentHashMap<>(2);
        List<SubCustBookQueryFundInfoTask> taskList = new ArrayList<>();
        taskList.add(new SubCustBookQueryFundInfoTask(fundMap, ProductChannelEnum.HIGH_FUND.getCode(), queryHighProductOuterService));
        taskList.add(new SubCustBookQueryFundInfoTask(fundMap, ProductChannelEnum.TP_SM.getCode(), queryHighProductOuterService));
        howBuyRunTaskUil.runTask(taskList);
        return fundMap;
    }

}
