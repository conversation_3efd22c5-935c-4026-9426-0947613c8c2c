/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 * <p>
 * Copyright (c) 2016, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 *Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.query.querysubcustbooks;

import com.github.pagehelper.Page;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.QueryTaInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.bean.TaInfoBean;
import com.howbuy.tms.high.batch.dao.po.order.SubCustBooksPo;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.QuerySubCustBooksFacade;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.QuerySubCustBooksRequest;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.QuerySubCustBooksResponse;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.bean.QuerySubCustBooksCondition;
import com.howbuy.tms.high.batch.facade.query.querysubcustbooks.bean.SubCustBooksBean;
import com.howbuy.tms.high.batch.service.facade.AbstractService;
import com.howbuy.tms.high.batch.service.repository.SubCustBooksRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 *
 * @description:查询子账本
 * <AUTHOR>
 * @date 2018年9月20日 下午4:53:58
 * @since JDK 1.6
 */
@DubboService
@Service("querySubCustBooksFacade")
public class QuerySubCustBooksFacadeService extends AbstractService<QuerySubCustBooksRequest, QuerySubCustBooksResponse>
        implements QuerySubCustBooksFacade {
    private static Logger logger = LogManager.getLogger(QuerySubCustBooksFacadeService.class);
    @Autowired
    private SubCustBooksRepository subCustBooksRepository;
    @Autowired
    private QueryTaInfoOuterService queryTaInfoOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    /**
     * 每次查询量
     */
    private static int maxQrySize = 50;

    @Override
    public QuerySubCustBooksResponse process(QuerySubCustBooksRequest request) {
        QuerySubCustBooksResponse resp = new QuerySubCustBooksResponse();

        /* 参数 */
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        QuerySubCustBooksCondition queryCondition = request.getQuerySubCustBooksCondition(); // 查询条件
        if (queryCondition == null) {
            throw new ValidateException(ExceptionCodes.PARAMS_ERROR, MessageSource.getMessageByCode(ExceptionCodes.PARAMS_ERROR));
        }

        // 代销场检
        if (YesOrNoEnum.NO.getCode().equals(queryCondition.getIsHBJGAuth())) {
            queryCondition.setFilterFundCodeList(queryHighProductOuterService.queryNotHBJGFundListService());
        }

        // 查询产品中心得到tainfomap和fundinfomap
        Page<SubCustBooksPo> subCustBooksPoPage = getSubCustBooksPoPage(queryCondition, pageNo, pageSize);
        Map<String, String> taInfoMap = getTaInfoMap();
        Map<String, String> fundInfoMap = getFundInfoMap();

        /* 转换 */
        SubCustBooksBean subCustBooksBean = null;
        List<SubCustBooksBean> subCustBooksBeanList = new ArrayList<SubCustBooksBean>();
        for (SubCustBooksPo po : subCustBooksPoPage.getResult()) {
            subCustBooksBean = new SubCustBooksBean();
            BeanUtils.copyProperties(po, subCustBooksBean);
            subCustBooksBean.setFundName(fundInfoMap.get(subCustBooksBean.getFundCode()));
            subCustBooksBean.setTaName(taInfoMap.get(subCustBooksBean.getTaCode()));
            subCustBooksBeanList.add(subCustBooksBean);
        }
        resp.setSubCustBooksBeanList(subCustBooksBeanList);

        resp.setPageNo(subCustBooksPoPage.getPageNum());
        resp.setTotalCount(subCustBooksPoPage.getTotal());
        resp.setTotalPage(subCustBooksPoPage.getPages());

        resp.setReturnCode(ExceptionCodes.SUCCESS);
        return resp;
    }

    /**
     *
     * getSubCustBooksPoPage:查询子账本
     * @param queryCondition
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2018年9月20日 下午5:10:31
     */
    private Page<SubCustBooksPo> getSubCustBooksPoPage(QuerySubCustBooksCondition queryCondition, Integer pageNo, Integer pageSize) {
        return subCustBooksRepository.getSubCustBooksForConsole(queryCondition, pageNo, pageSize);
    }

    private Map<String, String> getTaInfoMap() {
        final Map<String, String> map = new HashMap<>();
        final CountDownLatch latch = new CountDownLatch(2);
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    List<TaInfoBean> taList = queryTaInfoOuterService.getTaInfosByProductChannel(ProductChannelEnum.HIGH_FUND.getCode());
                    if (CollectionUtils.isEmpty(taList)) {
                        logger.warn("QuerySubCustBooksFacadeService|execute|taList is empty");
                        return;
                    }
                    for (TaInfoBean bean : taList) {
                        map.put(bean.getTaCode(), bean.getTaName());
                    }
                } finally {
                    latch.countDown();
                }
            }
        }).start();

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    List<TaInfoBean> taList = queryTaInfoOuterService.getTaInfosByProductChannel(ProductChannelEnum.TP_SM.getCode());
                    if (CollectionUtils.isEmpty(taList)) {
                        logger.warn("QuerySubCustBooksFacadeService|execute|taList is empty");
                        return;
                    }
                    for (TaInfoBean bean : taList) {
                        map.put(bean.getTaCode(), bean.getTaName());
                    }
                } finally {
                    latch.countDown();
                }
            }
        }).start();

        try {
            if (latch != null) {
                latch.await();
            }
        } catch (InterruptedException e) {
            logger.warn("QuerySubCustBooksFacadeService|execute|latch.await error, msg:{}", e.getMessage(), e);
            Thread.currentThread().interrupt();
        }
        return map;
    }

    private Map<String, String> getFundInfoMap() {
        final Map<String, String> fundMap = new HashMap<>();
        final CountDownLatch latch = new CountDownLatch(2);
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    List<HighProductBaseInfoBean> list = null;
                    for (int k = 1; ; k++) {
                        list = queryHighProductOuterService.getHighProductBaseInfoList(ProductChannelEnum.HIGH_FUND.getCode(), k, maxQrySize);
                        logger.info("QuerySubCustBooksFacadeService|pageNum:{}, pageSize:{}", k, list == null ? 0 : list.size());
                        if (CollectionUtils.isEmpty(list)) {
                            logger.warn("QuerySubCustBooksFacadeService|execute|highProductBaseInfoBeanList is empty, k:{}", k);
                            break;
                        }
                        for (HighProductBaseInfoBean bean : list) {
                            fundMap.put(bean.getFundCode(), bean.getFundAttr());
                        }
                    }
                } finally {
                    latch.countDown();
                }
            }
        }).start();

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    List<HighProductBaseInfoBean> list = null;
                    for (int k = 1; ; k++) {
                        list = queryHighProductOuterService.getHighProductBaseInfoList(ProductChannelEnum.TP_SM.getCode(), k, maxQrySize);
                        logger.info("QuerySubCustBooksFacadeService|pageNum:{}, pageSize:{}", k, list == null ? 0 : list.size());
                        if (CollectionUtils.isEmpty(list)) {
                            logger.warn("QuerySubCustBooksFacadeService|execute|highProductBaseInfoBeanList is empty, k:{}", k);
                            break;
                        }
                        for (HighProductBaseInfoBean bean : list) {
                            fundMap.put(bean.getFundCode(), bean.getFundAttr());
                        }
                    }
                } finally {
                    latch.countDown();
                }
            }
        }).start();
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.warn("QuerySubCustBooksFacadeService|execute|latch.await error, msg:{}", e.getMessage(), e);
            Thread.currentThread().interrupt();
        }
        return fundMap;
    }

}
