/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.constant.OutReturnCodes;
import com.howbuy.tms.common.enums.TxPmtFlagEnum;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.database.TxAppFlagEnum;
import com.howbuy.tms.common.outerservice.fbsonline.fundwithdraw.FundWithdrawContext;
import com.howbuy.tms.common.outerservice.fbsonline.fundwithdraw.FundWithdrawOuterService;
import com.howbuy.tms.common.outerservice.fbsonline.fundwithdraw.FundWithdrawResult;
import com.howbuy.tms.high.batch.dao.po.order.PaymentOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.service.business.ordersubmit.BaseResponse;
import com.howbuy.tms.high.batch.service.business.ordersubmit.CheckOrderSubmitCommonService;
import com.howbuy.tms.high.batch.service.business.ordersubmit.CheckOrderSubmitService;
import com.howbuy.tms.high.batch.service.repository.PaymentOrderRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:GN_FDS_NEW_05标准撤单接口服务
 * @reason:
 * <AUTHOR>
 * @date 2016年9月14日 下午1:59:49
 * @since JDK 1.6
 */
@Service("fundWithdrawSubmitService")
public class FundWithdrawSubmitService extends CheckOrderSubmitService {

    private static Logger logger = LogManager.getLogger(FundWithdrawSubmitService.class);
    /**
     * 强制撤销
     */
    private static final String FORCE_WITHDRAW = "0";
    /**
     * 自行撤销
     */
    private static final String SELF_WITHDRAW = "1";

    @Autowired
    private FundWithdrawOuterService fundWithdrawOuterService;

    @Autowired
    private PaymentOrderRepository paymentOrderRepository;

    @Autowired
    private CheckOrderSubmitCommonService checkOrderSubmitCommonService;

    @Override
    protected BaseResponse doSubmit(SimuFundCheckOrderManualSubmitVo fundCheckOrderPo) {
        FundWithdrawContext context = generateContextFund(fundCheckOrderPo);
        BaseResponse baseResponse = new BaseResponse();
        FundWithdrawResult result = fundWithdrawOuterService.doWithdraw(context);
        logger.info("FundWithdrawSubmitService|result:" + JSON.toJSONString(result));
        BeanUtils.copyProperties(result, baseResponse);
        return baseResponse;
    }

    @Override
    protected void submitSuccess(SimuFundCheckOrderPo fundCheckOrderPo) {
    }

    @Override
    protected void submitFail(SimuFundCheckOrderPo fundCheckOrderPo) {
    }

    private FundWithdrawContext generateContextFund(SimuFundCheckOrderPo fundCheckOrderPo) {
        FundWithdrawContext context = new FundWithdrawContext();

        // 交易渠道 TO 交易渠道
        context.setTradeChannel(fundCheckOrderPo.getTxChannel());
        // 分销机构 TO 分销机构代码
        context.setDisCode(fundCheckOrderPo.getDisCode());
        // 网点号 TO 网点代码
        context.setOutletCode(fundCheckOrderPo.getOutletCode());
        // 申请日期 TO 申请日期
        context.setAppDt(fundCheckOrderPo.getAppDate());
        // 申请时间 TO 申请时间
        context.setAppTm(fundCheckOrderPo.getAppTime());
        // IP地址 TO 交易IP
        context.setIpAddress(fundCheckOrderPo.getIpAddress());
        // 上报订单号 TO 中台订单号
        context.setSubmitDealNo(fundCheckOrderPo.getSubmitDealNo());
        // 风险确认标志
        // context.setRiskFlag(fundCheckOrderPo.getRiskFlag());

        // 交易账号
        context.setTxAcctNo(fundCheckOrderPo.getTxAcctNo());
        // 经办人姓名
        context.setTransactorName(fundCheckOrderPo.getTransactorName());
        // 经办人证件号码
        context.setTransactorIdNo(fundCheckOrderPo.getTransactorIdNo());
        // 经办人证件类型
        context.setTransactorIdType(fundCheckOrderPo.getTransactorIdType());
        if (TxAppFlagEnum.FORCE_REVOCATION.getCode().equals(fundCheckOrderPo.getTxAppFlag())) {
            context.setIsDrawSelf(FORCE_WITHDRAW);
        } else {
            context.setIsDrawSelf(SELF_WITHDRAW);
        }
        // 付款状态
        // 从支付明细表取
        String dealDtlNo = fundCheckOrderPo.getDealDtlNo();
        if (BusinessCodeEnum.PURCHASE.getMCode().equals(fundCheckOrderPo.getmBusiCode())
                || BusinessCodeEnum.SUBS.getMCode().equals(fundCheckOrderPo.getmBusiCode())) {
            // 认购、申购需要支付状态
            PaymentOrderPo paymentOrderPo = paymentOrderRepository.getByDealNo(fundCheckOrderPo.getDealNo());
            context.setTxPmtFlag(paymentOrderPo.getTxPmtFlag());
        } else {
            // 其他业务，支付状态=无需付款
            context.setTxPmtFlag(TxPmtFlagEnum.NOT_NEED.getKey());
        }
        context.setProtocolNo(fundCheckOrderPo.getProtocolNo());
        return context;
    }

    protected boolean isSuccess(BaseResponse response) {
        // 先校验是否0000
        if (checkOrderSubmitCommonService.isSuccess(response)) {
            return true;
        }

        // 不是0000，再查是否外部撤单已成功
        String returnCode = response.getReturnCode();
        return OutReturnCodes.DUBBO_WITHDRAW_EXTERNAL_DEAL_SUCCESS.equals(returnCode);
    }

}
