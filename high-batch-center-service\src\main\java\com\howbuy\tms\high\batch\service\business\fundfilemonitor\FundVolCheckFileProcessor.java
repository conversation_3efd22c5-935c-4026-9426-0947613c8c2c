/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.fundfilemonitor;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.FileOptionEnum;
import com.howbuy.tms.common.enums.database.FileOptionStatus;
import com.howbuy.tms.common.enums.database.FileTypeEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.QueryTaInfoOuterService;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessDtlRecPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.common.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.FileSdkUtil;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.repository.FundFileProcessDtlRecRepository;
import com.howbuy.tms.high.batch.service.repository.HighFundVolCheckFileRecRepository;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.FundVolCheckFileImportService;
import com.howbuy.tms.high.batch.service.service.sequence.SequenceService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @description:(高端公募份额对账文件消息处理)
 * @reason:
 * @date 2018年1月25日 下午6:51:54
 * @since JDK 1.7
 */
@Service("fundVolCheckFileProcessor")
public class FundVolCheckFileProcessor implements FundFileProcessor {
    private final Logger logger = LogManager.getLogger();

    @Autowired
    private FundFileProcessDtlRecRepository fundFileProcessDtlRecRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryTaInfoOuterService queryTaInfoOuterService;
    @Autowired
    private HighFundVolCheckFileRecRepository highFundVolCheckFileRecRepository;
    @Autowired
    private FundVolCheckFileImportService fundVolCheckFileImportService;
    @Autowired
    private SequenceService sequenceService;


    @Override
    public void process(FundFileMessage fundFileMessage) throws Exception {
        logger.info("FundVolCheckFileProcessor-process,开始处理后台份额对账文件消息, fundFileMessage:{}", JSON.toJSONString(fundFileMessage));

        // 查询所高端(专户/私募)TA
        Set<String> taCodeList = queryTaInfoOuterService.getAllHighTaCode();

        // 判断当天TA文件是否是需要处理，如果是，保存文件消息；否则忽略该消息
        if (taCodeList.contains(fundFileMessage.getTaCode())) {
            String sysCode = SysCodeEnum.BATCH_HIGH.getCode();
            String fileType = FileTypeEnum.H_VOL_CHK.getCode();
            String taTradeDt = fundFileMessage.getExportDt();
            String taCode = fundFileMessage.getTaCode();
            String fundCode = fundFileMessage.getFundCode();
            // 查询fundCode对应的产品通道
            HighProductBaseInfoBean product = queryHighProductOuterService.getHighProductBaseInfo(fundCode);
            if (product == null) {
                logger.error("FundVolCheckFileProcessor|product is not exsit, fundCode:{}", fundCode);
                return;
            }

            FundFileProcessDtlRecPo file = fundFileProcessDtlRecRepository.selectFundFileProcessDtlRec(taTradeDt, fileType, taCode, fundCode);
            if (file != null) {
                if (FileOptionStatus.PROCESSING.getCode().equals(file.getFileOpStatus())) {
                    logger.info("FundVolCheckFileProcessor.FundFileProcessDtlRecPo is processing|file:{}", JSON.toJSONString(file));
                    throw new BatchException(ExceptionCodes.HIGH_BATCH_CENETER_FUND_VOL_IMPORT_ERROR, "文件正在处理中,不能导入");
                }
            }

            // 保存文件消息
            FundFileProcessDtlRecPo fundFileProcessDtlRecPo = new FundFileProcessDtlRecPo();
            fundFileProcessDtlRecPo.setTaCode(taCode);
            fundFileProcessDtlRecPo.setFundCode(fundCode);
            fundFileProcessDtlRecPo.setTaTradeDt(taTradeDt);
            fundFileProcessDtlRecPo.setFileType(fileType);
            fundFileProcessDtlRecPo.setFileOption(FileOptionEnum.IMPORT.getCode());
            // 直接默认处理中
            fundFileProcessDtlRecPo.setFileOpStatus(FileOpStatusEnum.PROCESSING.getKey());
            fundFileProcessDtlRecPo.setOperator(Constant.OPERATOR_SYS);
            FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
            fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.ASSET_VOL);
            fileSdkPathInfo.setMiddlePath(fundFileMessage.getExportDt() + File.separator + taCode + File.separator);
            fileSdkPathInfo.setFileName(fundFileMessage.getFileName());
            fundFileProcessDtlRecPo.setFileName(FileSdkUtil.getAbsolutePath(fileSdkPathInfo));
            fundFileProcessDtlRecRepository.deleteByUniqueKey(fundFileProcessDtlRecPo);
            Date date = new Date();
            fundFileProcessDtlRecPo.setCreateDtm(date);
            fundFileProcessDtlRecPo.setUpdateDtm(date);
            fundFileProcessDtlRecPo.setRecordNo(sequenceService.getRecordNo(CacheKeyPrefix.HIGH_FILE_RECORDNO_PREFIX,null));
            fundFileProcessDtlRecRepository.insert(fundFileProcessDtlRecPo);
            FundFileProcessDtlRecPo updatePo = null;
            try {
                // 解析并保存文件内容
                logger.info("开始处理高端公募份额对账文件消息");
                FileImportContext context = new FileImportContext();
                context.setTaTradeDt(taTradeDt);
                context.setFileName(fundFileMessage.getFileName());
                context.setRelationPath(fundFileMessage.getExportDt() + File.separator + taCode + File.separator);
                context.getParams().put("checkDt", taTradeDt);
                context.getParams().put("sysCode", sysCode);
                context.getParams().put("fundCode", fundCode);
                fundVolCheckFileImportService.process(context);
                highFundVolCheckFileRecRepository.deleteByFundCode(fundCode);
                highFundVolCheckFileRecRepository.batchInsert(fundCode);

                // 更新文件处理状态
                updatePo = new FundFileProcessDtlRecPo();
                updatePo.setRecordNo(fundFileProcessDtlRecPo.getRecordNo());
                updatePo.setFileOpStatus(Constant.FILE_OP_STATUS_IMPORT_SUCC);
                updatePo.setUpdateDtm(date);
                fundFileProcessDtlRecRepository.updateByRecordNo(updatePo);
            } catch (Exception e) {
                updatePo = new FundFileProcessDtlRecPo();
                updatePo.setFileOpStatus(Constant.FILE_OP_STATUS_IMPORT_FAIL);
                if (e instanceof BusinessException) {
                    updatePo.setMemo(MessageSource.getMessageByCode(((BusinessException) e).getErrorCode()));
                } else {
                    updatePo.setMemo("文件处理发生异常");
                }
                updatePo.setRecordNo(fundFileProcessDtlRecPo.getRecordNo());
                updatePo.setUpdateDtm(date);
                fundFileProcessDtlRecRepository.updateByRecordNo(updatePo);
                logger.error(fundFileProcessDtlRecPo.getMemo(), e);
            }

        }

    }

}
