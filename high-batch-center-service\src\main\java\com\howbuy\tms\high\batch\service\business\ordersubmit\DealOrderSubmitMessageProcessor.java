/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit;

import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.high.batch.service.repository.DealOrderRepository;
import org.springframework.beans.factory.annotation.Value;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.database.WorkdayTypeEnum;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * @api {MQ} highDealOrderDtlJobQueue
 * @apiGroup schedule
 * @apiName 生成对账订单的调度消息接收器
 * @apiDescription 生成对账订单的调度消息接收器
 */
@Service("dealOrderSubmitMessageProcessor")
public class DealOrderSubmitMessageProcessor extends BatchMessageProcessor  {
    private static Logger logger = LogManager.getLogger(DealOrderSubmitMessageProcessor.class);

    @Value("${queue.highDealOrderDtljob}")
    private String highDealOrderDtlJobQueue;

    @Autowired
    private WorkdayService workdayService;
    
    /**
     * 上报订单生成服务
     */
    @Autowired
    private DealOrderSubmitService dealOrderSubmitService;
    
    /**
     * 订单服务
     */
    @Autowired
    private DealOrderRepository dealOrderRepository;
    
    /**
     * 最大线程数
     */
    private int maxPoolNum = 5;

    @Autowired
    private LockService lockService;

    @Override
    public void doProcessMessage(SimpleMessage message) {
        String lockKey = CacheKeyPrefix.IDEMPOTENT_KEY_LOCK_PREFIX + "DealOrderSubmitMessageProcessor";
        // 运行锁，只有在获取了锁之后才准许执行
        boolean lock = lockService.getLock(lockKey, 600);
        if (!lock) {
            logger.info("DealOrderSubmitMessageProcessor|doProcessMessage|get lock fail.");
            return;
        }
        
        try {
            // 交易日
            String tradeDt = workdayService.getWorkay(WorkdayTypeEnum.SYS_TYPE).getWorkday();
            logger.info("DealOrderSubmitMessageProcessor|doProcessMessage|tradeDt:{}", tradeDt);

            // 查询所有待通知/需要重新通知的订单明细
            List<SimuFundCheckOrderDto> list = dealOrderSubmitService.getDealOrdersToNotify(tradeDt);
            if(CollectionUtils.isEmpty(list)){
                logger.info("DealOrderSubmitMessageProcessor|doProcessMessage|submit list isEmpty");
                return;
            }
            
            List<SimuFundCheckOrderDto> normalList = new ArrayList<>();
            List<SimuFundCheckOrderDto> transferList = new ArrayList<>();
            for (SimuFundCheckOrderDto dto : list) {
                if (BusinessCodeEnum.FUND_SHARE_TRANSFER_IN.getMCode().equals(dto.getmBusiCode())
                        || BusinessCodeEnum.FUND_SHARE_TRANSFER_OUT.getMCode().equals(dto.getmBusiCode())) {
                    transferList.add(dto);
                } else {
                    normalList.add(dto);
                }
            }
            // 处理常规业务
            processNormalList(normalList);
            // 处理份额迁移业务
            processTransferList(transferList);
        } catch (Exception e) {
            logger.warn("DealOrderSubmitMessageProcessor|doProcessMessage|error, msg:{}", e.getMessage(), e);
        } finally {
            lockService.releaseLock(lockKey);
        }
    }
    
    private void processTransferList(List<SimuFundCheckOrderDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, List<SimuFundCheckOrderDto>> map = new HashMap<>();
        List<SimuFundCheckOrderDto> subList = null;
        for (SimuFundCheckOrderDto dto : list) {
            String key = dto.getTxAcctNo();
            if (map.containsKey(key)) {
                map.get(key).add(dto);
            } else {
                subList = new ArrayList<>();
                subList.add(dto);
                map.put(key, subList);
            }
        }
        for (String key : map.keySet()) {
            subList = map.get(key);
            dealOrderSubmitService.saveBatchToCheckOrder(subList);
        }
    }
    
    private void processNormalList(List<SimuFundCheckOrderDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        CountDownLatch latch = null;// 计数器、线程同步
        try {
            /* 将产品平均分配到maxPoolNum个线程进行处理 */
            int divs = list.size() / maxPoolNum;
            int mods = list.size() % maxPoolNum;
            
            if (divs > 0) {
                latch = new CountDownLatch(maxPoolNum);
                List<SimuFundCheckOrderDto> subList = null;
                for (int i = 0; i < maxPoolNum; i++) {
                    if (i == (maxPoolNum - 1)) {
                        subList = list.subList(i * divs, list.size());
                    } else {
                        subList = list.subList(i * divs, ((i + 1) * divs));
                    }
                    // 异步多线程处理
                    CommonThreadPool.execute(new SaveCheckOrderTask(dealOrderSubmitService, dealOrderRepository, subList, latch));
                }
            } else if (mods > 0) {
                latch = new CountDownLatch(1);
                // 异步多线程处理
                CommonThreadPool.execute(new SaveCheckOrderTask(dealOrderSubmitService, dealOrderRepository, list, latch));
            }
            try {
                if(latch != null) {
                    latch.await();
                }
            } catch (Exception e) {
                logger.warn("DealOrderSubmitMessageProcessor|doProcessMessage|latch.await error, msg:{}", e.getMessage(), e);
            }
        } catch (Exception e) {
            logger.warn("DealOrderSubmitMessageProcessor|doProcessMessage|error, msg:{}", e.getMessage(), e);
        } 
    }

    @Override
    protected String getQuartMessageChannel() {
        return highDealOrderDtlJobQueue;
    }

}
