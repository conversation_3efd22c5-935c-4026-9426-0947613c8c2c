/**
 * Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit;

import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.SubmitAppFlagEnum;
import com.howbuy.tms.common.enums.database.TxAppFlagEnum;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend.FundRedeemMergeSubmitService;
import com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend.FundSubsOrPurMergeSubmitService;
import com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend.FundWithdrawSubmitServiceAbstract;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.SimuFundCheckOrderRepository;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:手工点击上报
 * @date 2018年9月20日 下午3:35:11
 * @since JDK 1.6
 */
@Service("checkOrderSubmitManualProcessor")
public class CheckOrderSubmitManualProcessor {
    private static Logger logger = LogManager.getLogger(CheckOrderSubmitManualProcessor.class);

    /**
     * 撤单上报服务
     */
    @Autowired
    private FundWithdrawSubmitServiceAbstract fundWithdrawSubmitService;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private SimuFundCheckOrderRepository simuFundCheckOrderRepository;
    @Autowired
    private UnMarkService unMarkService;
    @Autowired
    private FundRedeemMergeSubmitService fundRedeemMergeSubmitService;
    @Autowired
    private FundSubsOrPurMergeSubmitService fundSubsOrPurMergeSubmitService;
    @Autowired
    private CheckOrderSubmitCommonService checkOrderSubmitCommonService;
    @Autowired
    private CheckOrderSubmitFactory checkOrderSubmitFactory;


    public void doSubmit(List<SimuFundCheckOrderManualSubmitVo> list) {
        logger.info("CheckOrderSubmitManualProcessor|doProcessMessage|start.");
        // 标准上报服务
        AbstractCheckOrderSubmitService abstractCheckOrderSubmitService = null;
        // 合并上报服务
        CheckOrderMergeSubmitService mergeSubmitService = null;

        for (SimuFundCheckOrderManualSubmitVo checkOrderPo : list) {
            logger.info("CheckOrderSubmitManualProcessor|dealDtlNo:{}", checkOrderPo.getDealDtlNo());
            List<SimuFundCheckOrderManualSubmitVo> orders;
            if (isMergeSubmitOrder(checkOrderPo.getMergeSubmitFlag())) {
                // 合并上报单查询所有子订单
                orders = getCheckOrdersToManualSubmitByMainDealNo(checkOrderPo.getDealNo());
                if (!checkMergeSubmitOrderCanSubmit(orders)) {
                    continue;
                }
                // 反脱敏
                unMarkService.resetIdNoAndBankAcct(orders);
            } else {
                orders = new ArrayList<>(1);
                orders.add(checkOrderPo);
            }

            // 检查并获取对应上报接口
            if (isCancelOrder(checkOrderPo.getTxAppFlag())) {
                // 撤单
                abstractCheckOrderSubmitService = fundWithdrawSubmitService;
                if (!processCancelData(orders)) {
                    continue;
                }
            } else {
                // 不是撤单
                String mBusiCode = StringUtils.trim(checkOrderPo.getmBusiCode());
                if (isMergeSubmitOrder(checkOrderPo.getMergeSubmitFlag())) {
                    // 合并上报
                    if (BusinessCodeEnum.REDEEM.getMCode().equals(mBusiCode)) {
                        mergeSubmitService = fundRedeemMergeSubmitService;
                    } else if (BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode) || BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode)) {
                        mergeSubmitService = fundSubsOrPurMergeSubmitService;
                    } else {
                        logger.error("CheckOrderSubmitManualProcessor|doSubmit|unsupported merge submit business. mBusiCode:{} mainDealOrderNo:{}", mBusiCode, checkOrderPo.getMainDealOrderNo());
                        continue;
                    }
                } else {
                    abstractCheckOrderSubmitService = checkOrderSubmitFactory.getCheckOrderSubmitService(mBusiCode);
                    if (abstractCheckOrderSubmitService == null) {
                        // 判断mBusiCode不合规的情况，记错误日志
                        logger.error("CheckOrderSubmitManualProcessor|Unknown mBusiCode {} in order detail {}", mBusiCode, checkOrderPo.getDealDtlNo());
                        // 跳过本条
                        continue;
                    }
                }

                // 设置上报申请时间
                orders.forEach(order -> checkOrderSubmitCommonService.setSubmitAppDtm(order));
                // 设置生效时间，用于外层【高端中台需要排序的产品生效时间一致的预警】 20221215
                // 对于合并上报（储蓄罐）的订单，其子订单的生效时间一致
                checkOrderPo.setEffectiveTm(orders.get(0).getEffectiveTm());
            }

            // 产品通道是高端公募或TP私募的认申购赎回交易 校验产品净值信任状态
            if ((BusinessCodeEnum.PURCHASE.getMCode().equals(checkOrderPo.getmBusiCode())
                    || BusinessCodeEnum.SUBS.getMCode().equals(checkOrderPo.getmBusiCode())
                    || BusinessCodeEnum.REDEEM.getMCode().equals(checkOrderPo.getmBusiCode()))) {
                boolean validateNavStatus = checkOrderSubmitCommonService.validateProductNavStatus(checkOrderPo.getFundCode(), checkOrderPo.getSubmitTaDt());
                if (!validateNavStatus) {
                    logger.error("CheckOrderSubmitManualProcessor|doProcessMessage|validateProductNavStatus:false, fundCode:{}, submitTaDt:{}",
                            checkOrderPo.getFundCode(), checkOrderPo.getSubmitTaDt());
                    String msg = "订单:" + checkOrderPo.getDealNo() + "上报前置校验失败,因为产品:" + checkOrderPo.getFundCode() + "当天：" + checkOrderPo.getSubmitTaDt() + "校验产品净值的信任状态不通过";
                    OpsMonitor.warn(msg, OpsMonitor.WARN);
                    continue;
                }
            }

            // 上报处理
            if (useMergeSubmit(checkOrderPo.getTxAppFlag(), checkOrderPo.getMergeSubmitFlag())) {
                // 合并上报
                processMerge(orders, mergeSubmitService);
            } else {
                // 标准上报
                for (SimuFundCheckOrderManualSubmitVo order : orders) {
                    checkOrderSubmitCommonService.process(order, abstractCheckOrderSubmitService);
                }
            }
        }
        logger.info("CheckOrderSubmitManualProcessor|doProcessMessage|end.");
    }

    /**
     * 验证合并上报单是否可以上报
     *
     * @param orders
     * @return boolean
     * @author: huaqiang.liu
     * @date: 2021/3/30 13:42
     * @since JDK 1.8
     */
    private boolean checkMergeSubmitOrderCanSubmit(List<SimuFundCheckOrderManualSubmitVo> orders) {
        boolean canSubmit = true;
        String preBusiType = null;
        for (SimuFundCheckOrderManualSubmitVo order : orders) {
            // 检查上报状态
            if (!SubmitAppFlagEnum.SUBMITTING.getCode().equals(order.getSubmitAppFlag())
                    && !SubmitAppFlagEnum.SUBMIT_AGAIN.getCode().equals(order.getSubmitAppFlag())) {
                logger.error("CheckOrderSubmitManualProcessor|checkMergeSubmitOrderCanSubmit 合并上报单存在不可上报子订单：dealNo:{} submitAppFlag:{}", order.getDealNo(), order.getSubmitAppFlag());
                canSubmit = false;
                break;
            }
            // 检查订单状态（撤单）
            String busiType = isCancelOrder(order.getTxAppFlag()) + "|" + order.getmBusiCode();
            if (preBusiType == null) {
                preBusiType = busiType;
            } else if (!preBusiType.equals(busiType)) {
                logger.error("CheckOrderSubmitManualProcessor|checkMergeSubmitOrderCanSubmit 合并上报单子订单状态（是否撤单+mBusiCode）不一致 mainDealNo:{}", order.getMainDealOrderNo());
                canSubmit = false;
                break;
            }
        }
        // 检查上报表与订单明细表订单数量
        String mainDealOrderNo = orders.get(0).getMainDealOrderNo();
        int num = countDealOrderMergeSubmitNum(mainDealOrderNo);
        if (num != orders.size()) {
            logger.error("CheckOrderSubmitManualProcessor|checkMergeSubmitOrderCanSubmit 上报表与订单明细表合并上报子订单数量不一致 mainDealNo:{} 上报表:{} 订单明细:{}", mainDealOrderNo, orders.size(), num);
            canSubmit = false;
        }
        return canSubmit;
    }

    /**
     * 是否合并上报
     *
     * @param txAppFlag
     * @param mergeSubmitFlag
     * @return boolean
     * @author: huaqiang.liu
     * @date: 2021/3/11 19:27
     * @since JDK 1.8
     */
    private boolean useMergeSubmit(String txAppFlag, String mergeSubmitFlag) {
        // 仅非撤单的合并上报场景使用合并上报接口
        return !isCancelOrder(txAppFlag) && isMergeSubmitOrder(mergeSubmitFlag);
    }

    /**
     * 是否合并上报订单
     *
     * @param mergeSubmitFlag
     * @return boolean
     * @author: huaqiang.liu
     * @date: 2021/3/12 11:18
     * @since JDK 1.8
     */
    private boolean isMergeSubmitOrder(String mergeSubmitFlag) {
        return YesOrNoEnum.YES.getCode().equals(mergeSubmitFlag);
    }

    /**
     * 是否撤单
     *
     * @param txAppFlag
     * @return boolean
     * @author: huaqiang.liu
     * @date: 2021/3/11 19:23
     * @since JDK 1.8
     */
    private boolean isCancelOrder(String txAppFlag) {
        return TxAppFlagEnum.SELF_REVOCATION.getCode().equals(txAppFlag)
                || TxAppFlagEnum.FORCE_REVOCATION.getCode().equals(txAppFlag);
    }

    /**
     * 处理撤单数据
     *
     * @param orders
     * @return boolean
     * @author: huaqiang.liu
     * @date: 2021/3/11 19:22
     * @since JDK 1.8
     */
    private boolean processCancelData(List<SimuFundCheckOrderManualSubmitVo> orders) {
        for (SimuFundCheckOrderManualSubmitVo order : orders) {
            if (StringUtils.isEmpty(order.getRefundDt())) {
                logger.error("CheckOrderSubmitManualProcessor|dealDtlNo:{}, reFundDt is null", order.getDealDtlNo());
                return false;
            }
            // 设置上报申请时间
            checkOrderSubmitCommonService.setDefaultAppDtm(order, order.getRefundDt());
        }
        return true;
    }


    /**
     * 合并上报处理
     *
     * @param checkOrderList
     * @param mergeSubmitService
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/3/12 10:38
     * @since JDK 1.8
     */
    private void processMerge(List<SimuFundCheckOrderManualSubmitVo> checkOrderList, CheckOrderMergeSubmitService mergeSubmitService) {
        try {
            logger.info("CheckOrderSubmitManualProcessor|processMerge|start submit,mainDealOrderNo:{}", checkOrderList.get(0).getMainDealOrderNo());
            // 上报
            BaseResponse response = mergeSubmitService.submit(checkOrderList);
            // 上报后处理
            mergeSubmitService.execPostSubmit(checkOrderList, response);
        } catch (Exception ex) {
            logger.error("CheckOrderSubmitManualProcessor|processMerge|Error ", ex);
        }
    }

    private List<SimuFundCheckOrderManualSubmitVo> getCheckOrdersToManualSubmitByMainDealNo(String dealNo) {
        return simuFundCheckOrderRepository.getCheckOrdersToManualSubmitByMainDealNo(dealNo);
    }

    /**
     * 统计订单表上报单数量
     *
     * @param mainDealNo
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/4/9 10:32
     * @since JDK 1.8
     */
    private int countDealOrderMergeSubmitNum(String mainDealNo) {
        return highDealOrderDtlRepository.countDealOrderMergeSubmitNum(mainDealNo);
    }
}
