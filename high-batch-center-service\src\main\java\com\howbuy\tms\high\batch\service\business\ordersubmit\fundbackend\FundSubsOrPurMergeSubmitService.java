/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.ordersubmit.fundbackend;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.outerservice.fbsonline.fundsuborpur.FundSubsOrPurContext;
import com.howbuy.tms.common.outerservice.fbsonline.fundsuborpurmerge.FundSubsOrPurMergeContext;
import com.howbuy.tms.common.outerservice.fbsonline.fundsuborpurmerge.FundSubsOrPurMergeOuterService;
import com.howbuy.tms.common.outerservice.fbsonline.fundsuborpurmerge.FundSubsOrPurMergeResult;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.service.business.ordersubmit.BaseResponse;
import com.howbuy.tms.high.batch.service.business.ordersubmit.CheckOrderMergeSubmitService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 认申购合并上报服务
 * <AUTHOR>
 * @date 2021/6/3 15:42
 * @since JDK 1.8
 */
@Service("fundSubsOrPurMergeSubmitService")
public class FundSubsOrPurMergeSubmitService extends CheckOrderMergeSubmitService {

    private static Logger logger = LogManager.getLogger(FundSubsOrPurMergeSubmitService.class);

    @Autowired
    private FundSubsOrPurMergeOuterService fundSubsOrPurMergeOuterService;
    @Autowired
    private FundSubsOrPurSubmitServiceAbstract fundSubsOrPurSubmitService;

    @Override
    protected BaseResponse doSubmit(List<SimuFundCheckOrderManualSubmitVo> checkOrderList) {
        FundSubsOrPurMergeContext context = generateContext(checkOrderList);

        FundSubsOrPurMergeResult result = fundSubsOrPurMergeOuterService.doFundSubsOrPur(context);
        logger.info("FundSubsOrPurMergeSubmitService|result:" + JSON.toJSONString(result));
        BaseResponse baseResponse = new BaseResponse();
        BeanUtils.copyProperties(result, baseResponse);
        baseResponse.setMiddleDealNoToContractNoMap(result.getDealNoMapping());
        return baseResponse;
    }

    private FundSubsOrPurMergeContext generateContext(List<SimuFundCheckOrderManualSubmitVo> checkOrderList) {
        FundSubsOrPurMergeContext mergeContext = new FundSubsOrPurMergeContext();
        // 合并支付主订单号
        mergeContext.setMainDealOrderNo(checkOrderList.get(0).getMainDealOrderNo());

        // 合并支付订单列表
        List<FundSubsOrPurContext> orderList = new ArrayList<>(checkOrderList.size());
        for (SimuFundCheckOrderManualSubmitVo order : checkOrderList) {
            FundSubsOrPurContext context = fundSubsOrPurSubmitService.generateContext(order);
            orderList.add(context);
        }
        mergeContext.setOrderList(orderList);

        return mergeContext;
    }

}