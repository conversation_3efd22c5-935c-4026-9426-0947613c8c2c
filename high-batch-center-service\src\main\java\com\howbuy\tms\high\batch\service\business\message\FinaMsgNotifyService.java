/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.message;

import com.alibaba.fastjson.JSON;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.enums.busi.FinaFileTypeEnum;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.business.finafilemonitor.FinaAbstractFileService;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @api {MQ} capital_middle_file_mq
 * @apiGroup message
 * @apiName 资金消息接收处理
 * @apiDescription 资金消息接收处理
 */
@Service("finaMsgNotifyService")
public class FinaMsgNotifyService extends BatchMessageProcessor {
    private static final Logger logger = LogManager.getLogger(FinaMsgNotifyService.class);
    
    @Value("${queue.fina.file.notify}")
    private String finaMsgNotifyQueueName;
    
    private Map<String, FinaAbstractFileService> finaAbstractFileServices;

    /**
     * 是否调度
     * @return
     */
    @Override
    public boolean isSchedule() {
        return false;
    }

    @Override
    protected String getQuartMessageChannel() {
        return finaMsgNotifyQueueName;
    }

    @Override
    public void doProcessMessage(SimpleMessage msg) {
        logger.info("FinaMsgNotifyService|doProcessMessage start.");

        Object message = msg.getContent();

        if (message == null) {
            return;
        }

        logger.info("FinaDirectionFileNotifyProcessor|message:{}", message);

        String content = (String) message;

        CapitalFileMessage capitalFileMessage = JSON.parseObject(content, CapitalFileMessage.class);

        FinaFileMessageBean bean = new FinaFileMessageBean();
        bean.setDisFileType(capitalFileMessage.getMqKey());
        bean.setExportDt(capitalFileMessage.getSysWorkDay());
        bean.setFileName(capitalFileMessage.getFileName());

        if (StringUtils.isEmpty(bean.getFileName()) || StringUtils.isEmpty(bean.getExportDt())
                || StringUtils.isEmpty(bean.getDisFileType())) {
            return;
        }
        
        String fileType = FinaFileTypeEnum.getValue(bean.getDisFileType());
        if (StringUtils.isEmpty(fileType)) {
            return;
        }
        
        FinaAbstractFileService finaAbstractFileService = finaAbstractFileServices.get(bean.getDisFileType());
        if (finaAbstractFileService == null) {
            return;
        }
        finaAbstractFileService.process(bean);
        logger.info("FinaDirectionFileNotifyProcessor|doProcessMessage end.");
    }
    
    public void setFinaAbstractFileServices(Map<String, FinaAbstractFileService> finaAbstractFileServices) {
        this.finaAbstractFileServices = finaAbstractFileServices;
    }

}
