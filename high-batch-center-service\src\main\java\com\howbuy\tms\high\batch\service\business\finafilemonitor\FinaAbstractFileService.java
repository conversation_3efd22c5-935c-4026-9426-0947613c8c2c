/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.finafilemonitor;

import com.howbuy.tms.common.enums.busi.FinaFileTypeEnum;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.high.batch.dao.po.batch.FinaFileProcessRecPo;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.business.message.FinaFileMessageBean;
import com.howbuy.tms.high.batch.service.common.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.FileSdkUtil;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.FinaFileProcessRecRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 直销份额文件处理
 * @date 2019年4月10日 上午10:08:15
 * @since JDK 1.6
 */
@Service("finaAbstractFileService")
public abstract class FinaAbstractFileService {
    private static Logger logger = LogManager.getLogger(FinaAbstractFileService.class);
    @Autowired
    private FinaFileProcessRecRepository finaFileProcessRecRepository;

    public abstract void process(FinaFileMessageBean finaFileMessageBean);

    public abstract void validate(String[] recDtl);

    public abstract Object build(String[] recDtl, String ackDt, Date date, String dataSourceType, String batchNo) throws Exception;

    public abstract void insert(List<?> list);

    public void preFileProcess(FinaFileMessageBean finaFileMessageBean, FinaFileProcessRecPo finaFileProcessRecPo) throws Exception {
        finaFileProcessRecPo.setTaTradeDt(finaFileMessageBean.getExportDt());
        finaFileProcessRecPo.setFileType(FinaFileTypeEnum.getValue(finaFileMessageBean.getDisFileType()));
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.FINAL_FILE_STORE);
        fileSdkPathInfo.setMiddlePath(finaFileMessageBean.getExportDt() + File.separator);
        fileSdkPathInfo.setFileName(finaFileMessageBean.getFileName());
        finaFileProcessRecPo.setFileName(FileSdkUtil.getAbsolutePath(fileSdkPathInfo));
        finaFileProcessRecPo.setFileOpStatus(FileOpStatusEnum.PROCESSING.getKey());
        finaFileProcessRecPo.setProcessStatus(ProcessStatusEnum.NOT_PROCESS.getCode());
        finaFileProcessRecPo.setProcessCount(BigDecimal.ZERO);
        Date date = new Date();
        finaFileProcessRecPo.setCreateDtm(date);
        finaFileProcessRecPo.setUpdateDtm(date);
        finaFileProcessRecPo.setSysCode(SysCodeEnum.BATCH_HIGH.getCode());
        finaFileProcessRecRepository.insert(finaFileProcessRecPo);
    }

    public int afterFileProcess(FinaFileProcessRecPo finaFileProcessRecPo) {
        FinaFileProcessRecPo newPo = new FinaFileProcessRecPo();
        newPo.setFileOpStatus(finaFileProcessRecPo.getFileOpStatus());
        newPo.setRecordNo(finaFileProcessRecPo.getRecordNo());
        Date date = new Date();
        newPo.setUpdateDtm(date);
        return finaFileProcessRecRepository.updateByRecordNo(newPo);
    }


    /**
     * getDataSourceType:根据消息类型区分是TA下发还是修改,1-TA下发2-修改
     *
     * @param mqKey
     * @return
     * <AUTHOR>
     * @date 2019年7月3日 下午5:42:16
     */
    public String getDataSourceType(String mqKey) {
        if ("TA_SETTLE_TYPE".equals(mqKey)) {
            return "1";
        } else if ("UPDATE_SETTLE_TYPE".equals(mqKey)) {
            return "2";
        }
        return null;
    }
}
