/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.autosendnotifyemail;

import com.howbuy.tms.common.outerservice.ccmessage.sendemailwithattachment.SendEmailWithAttachmentOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.sendemailwithattachment.bean.SendEmailWithAttachmentBean;
import com.howbuy.tms.high.batch.service.common.HowbuyBaseTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @className SendEmailWithAttachTask
 * @description
 * <AUTHOR>
 * @date 2019/8/16 18:31
 */
public class SendEmailWithAttachTask extends HowbuyBaseTask {
    private static final Logger logger = LoggerFactory.getLogger(SendEmailWithAttachTask.class);
    private SendEmailWithAttachmentBean req;

    private SendEmailWithAttachmentOuterService sendEmailWithAttachmentOuterService;

    public SendEmailWithAttachTask(SendEmailWithAttachmentBean req, SendEmailWithAttachmentOuterService sendEmailWithAttachmentOuterService) {
        this.req = req;
        this.sendEmailWithAttachmentOuterService = sendEmailWithAttachmentOuterService;
    }

    @Override
    protected void callTask() {
        logger.info("SendManagerEmailTask|sendEmail start");
        sendEmailWithAttachmentOuterService.execute(req);
        logger.info("SendManagerEmailTask|sendEmail end");
    }
}
