/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.ordersubmit;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.service.FundProductService;
import com.howbuy.paycommon.model.enums.SysCodeEnum;
import com.howbuy.payonline.facade.query.querytradeinfolist.QueryTradeInfo;
import com.howbuy.payonline.facade.query.querytradeinfolist.QueryTradeInfoListFacade;
import com.howbuy.payonline.facade.query.querytradeinfolist.QueryTradeInfoListRequest;
import com.howbuy.payonline.facade.query.querytradeinfolist.QueryTradeInfoListResponse;
import com.howbuy.tms.common.constant.OutReturnCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.DiffProcessStatusEnum;
import com.howbuy.tms.common.enums.database.PaymentTypeEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.enums.database.ProductNavTrustTradeStatEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.batch.HighTradeExceptionPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.PaymentOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.service.repository.HighTradeExceptionRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.PaymentOrderRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2022/3/28 18:42
 * @since JDK 1.8
 */
@Service
public class CheckOrderSubmitCommonService  {

    /**
     * 基金代码
     */
    @Value("${order.report.fund.codes}")
    private String fundCodes;

    private Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private FundProductService fundProductService;
    @Autowired
    private HighTradeExceptionRepository highTradeExceptionRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;


    @Autowired
    private PaymentOrderRepository paymentOrderRepository;

    @Autowired
    private QueryTradeInfoListFacade queryTradeInfoListFacade;

    protected String queryPreAckDt(SimuFundCheckOrderPo simuFundCheckOrderPo){
        try{
            return fundProductService.getPreActDt(simuFundCheckOrderPo.getFundCode(),simuFundCheckOrderPo.getFundType(),simuFundCheckOrderPo.getBusiCode(),simuFundCheckOrderPo.getTradeDt());
        }catch (Exception e){
            logger.error("query preAckDt Exception",e);
        }
        return null;
    }

    /**
     *
     * isSuccess:判断返回是否成功
     *
     * @param response
     * @return true/false
     * <AUTHOR>
     * @date 2016年9月19日 下午6:59:56
     */
    public boolean isSuccess(BaseResponse response) {
        String returnCode = response.getReturnCode();
        // 成功
        return (returnCode != null && (returnCode.equals(OutReturnCodes.RMI_SUCCESS) || returnCode.equals(OutReturnCodes.DUBBO_SUCCESS)));
    }

    /**
     *
     * isCallError:是否调用异常
     *
     * @param response
     * @return
     * <AUTHOR>
     * @date 2017年7月18日 下午5:31:37
     */
    protected boolean isCallError(BaseResponse response) {
        String returnCode = response.getShortReturnCode();
        return StringUtils.isEmpty(returnCode) || returnCode.equals(OutReturnCodes.SIMU_SYSTEM_ERROR) || returnCode.equals(OutReturnCodes.SYSTEM_ERROR)
                || returnCode.equals(OutReturnCodes.DB_ERROR) || returnCode.equals(OutReturnCodes.DOING) || returnCode.equals(OutReturnCodes.CONCURRENT_ERROR)
                || returnCode.equals(OutReturnCodes.FREZZ_VOL_CONCURRENT);
    }

    /**
     *
     * @Description 已上报
     *
     * @param response
     * @return boolean
     * <AUTHOR>
     * @Date 2019/12/24 10:37
     **/
    protected boolean isCallSubmited(BaseResponse response) {
        String returnCode = response.getShortReturnCode();
        return OutReturnCodes.DEAL_ALREADY_SUBMIT.equals(returnCode);
    }

    /**
     *
     * addFundCheckOrderDiff:记录上报异常明细
     *
     * @param orderDiff
     * <AUTHOR>
     * @date 2017年9月20日 下午2:41:53
     */
    protected void addFundCheckOrderDiff(HighTradeExceptionPo orderDiff) {
        List<HighTradeExceptionPo> existHighTradeExceptionist  =  highTradeExceptionRepository.selectByDealNo(orderDiff.getDealNo());
        if(CollectionUtils.isNotEmpty(existHighTradeExceptionist)){
            for (HighTradeExceptionPo tradeExceptionPo : existHighTradeExceptionist) {
                if(DiffProcessStatusEnum.UNPROCESS.getKey().equals(tradeExceptionPo.getProcessStatus())){
                    HighTradeExceptionPo updateHighTradeExceptionPo = new HighTradeExceptionPo();
                    updateHighTradeExceptionPo.setRecordNo(tradeExceptionPo.getRecordNo());
                    updateHighTradeExceptionPo.setProcessStatus(DiffProcessStatusEnum.PROCESSED.getKey());
                    updateHighTradeExceptionPo.setMemo("系统更新");
                    updateHighTradeExceptionPo.setOperator("system");
                    highTradeExceptionRepository.updateByPrimaryKeySelective(updateHighTradeExceptionPo);
                }
            }
        }

        highTradeExceptionRepository.insertSelective(orderDiff);
    }

    /**
     *
     * feedShortReturnCode:填充返回码后4位
     *
     * @param response
     * <AUTHOR>
     * @date 2016年10月12日 下午5:53:00
     */
    protected void feedShortReturnCode(BaseResponse response) {
        String shortReturnCode = null;
        String returnCode = response.getReturnCode();
        if (returnCode != null && returnCode.length() >= 4) {
            // 返回不为空，且不小于4位
            shortReturnCode = returnCode.substring(returnCode.length() - 4);
        }
        response.setShortReturnCode(shortReturnCode);

        String retDesc = response.getDescription();
        if (retDesc != null && retDesc.length() > 100) {
            retDesc = retDesc.substring(0, 100);
        }
        response.setDescription(retDesc);
    }

    /**
     * process:(上报处理)
     *
     * @param checkOrderPo
     * @param checkOrderSubmitService
     * @return
     * <AUTHOR>
     * @date 2018年6月8日 下午8:08:49
     */
    public void process(SimuFundCheckOrderManualSubmitVo checkOrderPo, CheckOrderSubmitService checkOrderSubmitService) {
        try {
            logger.info("CheckOrderSubmitManualProcessor|process|start submit,submitDealNo:{}", checkOrderPo.getSubmitDealNo());
            // 上报
            BaseResponse response = checkOrderSubmitService.submit(checkOrderPo);
            // 上报后处理
            checkOrderSubmitService.execPostSubmit(checkOrderPo, response);
        } catch (Exception ex) {
            logger.error("CheckOrderSubmitManualProcessor|process|Error ", ex);
        }
    }

    /**
     *
     * validateProductNavStatus:(校验产品净值的信任状态)
     * @param fundCode
     * @param taTradeDt
     * @return
     * <AUTHOR>
     * @date 2018年5月30日 下午2:37:29
     */
    public boolean validateProductNavStatus(String fundCode, String taTradeDt){
        HighProductStatInfoBean highProductStatInfoBean= queryHighProductOuterService.getHighProductStatInfo(fundCode, taTradeDt);
        if(highProductStatInfoBean == null){
            return false;
        }
        if(ProductNavTrustTradeStatEnum.YES.getCode().equals(highProductStatInfoBean.getIsTrustTradeStat())){
            return true;
        }
        logger.info("validateProductNavStatus-校验产品净值的信任状态,highProductStatInfoBean={}", JSON.toJSONString(highProductStatInfoBean));
        return false;

    }

    public void setDefaultAppDtm(SimuFundCheckOrderPo checkOrderPo, String appDate) {
        checkOrderPo.setAppDate(appDate);
        checkOrderPo.setAppTime("145959");
    }
    /**
     *
     * setSubmitAppDtm:(设置上报申请日期)
     * @param checkOrderPo
     */
    public void setSubmitAppDtm(SimuFundCheckOrderManualSubmitVo checkOrderPo){

        if (BusinessCodeEnum.REDEEM.getMCode().equals(checkOrderPo.getmBusiCode()) || isPurOrSub(checkOrderPo)) {
            // 设置默认上报申请日期
            setDefaultAppDtm(checkOrderPo, checkOrderPo.getSubmitTaDt());

            setEffectiveTime(checkOrderPo);
        }
    }

    /**
     * @param checkOrderPo
     * @return
     */
    private String getMaxTime(SimuFundCheckOrderManualSubmitVo checkOrderPo) {
        // 默认 订单申请 日期和时间
        String timeStr = DateUtils.formatToString(checkOrderPo.getAppDtm(), DateUtils.YYYYMMDDHHMMSS);

        if (PaymentTypeEnum.SELF_DRAWING.getCode().equals(checkOrderPo.getPaymentType())) {
            QueryTradeInfoListResponse response = getQueryTradeInfoList(checkOrderPo);

            if (response != null && CollectionUtils.isNotEmpty(response.getQueryOrderInfo())) {
                List<QueryTradeInfo> queryOrderInfo = response.getQueryOrderInfo();
                for (QueryTradeInfo info : queryOrderInfo) {
                    Date earliestPayTime = info.getEarliestPayTime();
                    if (earliestPayTime == null) {
                        break;
                    }

                    // 客户意愿时间=max（订单申请日期+时间，资金来款最早时间）
                    String earliestPayTimeStr = DateUtils.formatToString(earliestPayTime, DateUtils.YYYYMMDDHHMMSS);
                    // 资金来款最早时间>订单申请时间
                    if (earliestPayTimeStr.compareTo(timeStr) > 0) {
                        timeStr = earliestPayTimeStr;
                        break;
                    }
                }
            }
        }
        return timeStr;
    }

    private QueryTradeInfoListResponse getQueryTradeInfoList(SimuFundCheckOrderPo checkOrderPo) {

        PaymentOrderPo paymentOrderPo = getByDealNo(checkOrderPo.getDealNo());
        if (paymentOrderPo == null) {
            return null;
        }

        // 接口里面加资金来款最早时间
        QueryTradeInfoListRequest request = new QueryTradeInfoListRequest();
        request.setDealNo(paymentOrderPo.getPmtDealNo());
        request.setSysCode(getSysCode(checkOrderPo));
        QueryTradeInfoListResponse response = queryTradeInfoListFacade.execute(request);
        return response;
    }

    private SysCodeEnum getSysCode(SimuFundCheckOrderPo checkOrder) {
        String productChannel = checkOrder.getProductChannel();
        SysCodeEnum sysCode;
        if (ProductChannelEnum.HIGH_FUND.getCode().equals(productChannel)) {
            sysCode = SysCodeEnum.HOWBUY_HIGH_END_PRIVATE;
        } else if (ProductChannelEnum.TP_SM.getCode().equals(productChannel)) {
            sysCode = SysCodeEnum.HOWBUY_TP_PRIVATE;
        } else if (ProductChannelEnum.REGULAR.getCode().equals(productChannel)) {
            sysCode = SysCodeEnum.HOWBUY_MIDDLE_GROUND_REGULAR;
        } else {
            sysCode = SysCodeEnum.HOWBUY_MIDDLE_GROUND;
        }
        return sysCode;
    }

    private PaymentOrderPo getByDealNo(String dealNo) {
        return paymentOrderRepository.getByDealNo(dealNo);
    }

    /**
     * 小集合（基金类型为9）的产品的认申购设置交易时间
     */
    public void setEffectiveTime(SimuFundCheckOrderManualSubmitVo checkOrderPo) {
        // 非特殊产品返回
        if (!isSpecialProduct(checkOrderPo.getFundCode())) {
            logger.info("产品{},不需要设置认申购交易时间",checkOrderPo.getFundCode());
            return;
        }

        logger.info("产品{},需要设置认申购交易时间",checkOrderPo.getFundCode());

        if (!isPurOrSub(checkOrderPo)) {
            return;
        }

        String maxTime = getMaxTime(checkOrderPo);
        int compare00 = maxTime.compareTo(checkOrderPo.getSubmitTaDt() + "000500"); // submitTaDt + "00:05:00"
        int compare15 = maxTime.compareTo(checkOrderPo.getSubmitTaDt() + "150000"); // submitTaDt + "15:00:00"

        // max（订单申请时间，资金来款最早时间）>= 上报日 返回
        int index = maxTime.length() - 6;
        String maxDt = maxTime.substring(0, index);
        String maxTm = maxTime.substring(index);
        if (compare00 < 0) {
            // 设置资金来款最早时间
            checkOrderPo.setEffectiveDt(maxDt);
            checkOrderPo.setEffectiveTm(maxTm);
        } else if (compare15 < 0) {
            //当max（订单申请时间，资金来款最早时间）<=当前工作日14:59:59，则申请时间取max（订单申请时间，资金来款最早时间）的时间。
            checkOrderPo.setAppTime(maxTm);
        }
    }

    /**
     * 特殊产品需要走新规则
     * @param fundCode
     * @return
     */
    private boolean isSpecialProduct(String fundCode) {
        HighProductInfoBean highProductInfoBean = queryHighProductOuterService.getHighProductInfo(fundCode);

        return YesOrNoEnum.YES.getCode().equals(highProductInfoBean.getSubmitSpecialFlag());
    }


    /**
     * 是认申购
     * @param checkOrderPo
     * @return
     */
    private boolean isPurOrSub(SimuFundCheckOrderPo checkOrderPo) {
        return (BusinessCodeEnum.PURCHASE.getMCode().equals(checkOrderPo.getmBusiCode())
                || BusinessCodeEnum.SUBS.getMCode().equals(checkOrderPo.getmBusiCode())) ;
    }

    public HighDealOrderDtlPo getHighOrderDtl(String dealDtlNo){
        return highDealOrderDtlRepository.selectByDealDtlNo(dealDtlNo);
    }
}