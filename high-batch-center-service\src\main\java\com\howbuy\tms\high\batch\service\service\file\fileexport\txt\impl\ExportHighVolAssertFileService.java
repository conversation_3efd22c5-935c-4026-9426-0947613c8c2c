package com.howbuy.tms.high.batch.service.service.file.fileexport.txt.impl;

import com.github.pagehelper.Page;
import com.howbuy.tms.high.batch.dao.po.order.ExportHeVolCheckFileRecPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.service.business.generatefile.GenerateHighVolCheckAssertFileService;
import com.howbuy.tms.high.batch.service.common.HighBatchConstants;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.ExportHeVolCheckFileRecRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.AbstractTxtFileExportService;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.bean.TxtFileExportContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Description:给资产中心代销份额确认文件
 * @Author: yun.lu
 * Date: 2024/7/19 19:10
 */
@Service
@Slf4j
public class ExportHighVolAssertFileService extends AbstractTxtFileExportService<ExportHeVolCheckFileRecPo> {
    @Autowired
    private ExportHeVolCheckFileRecRepository exportHeVolCheckFileRecRepository;
    @Autowired
    private GenerateHighVolCheckAssertFileService generateHighVolCheckAssertFileService;


    @Value("${export.high.volCheckFile.fileFileds}")
    private String fileFileds;

    @Value("${export.high.volCheckFile.fileFiledsName}")
    private String fileFiledNames;

    @Override
    public String getBusinessCode() {
        return FilePathStoreBusinessCodeConfig.VOL_CHECK_FILE_PATH;
    }

    @Override
    public Page<ExportHeVolCheckFileRecPo> queryIoByParams(Map<String, Object> params, int pageNum, int pageSize) {
        String taCode = (String) params.get("taCode");
        return exportHeVolCheckFileRecRepository.getByTradeDtTaCode(taCode, pageNum, pageSize);
    }

    @Override
    public String createFileLine(Map<String, Object> params, ExportHeVolCheckFileRecPo object, int currentCount) throws Exception {
        List<String> contentList = generateHighVolCheckAssertFileService.convertContent(fileFileds, Collections.singletonList(object));
        return contentList.get(0) ;
    }

    @Override
    public String createFileHeader(Map<String, Object> params) {
        int totalCount = 0;
        if (params.containsKey("totalCount")) {
            totalCount = (int) params.get("totalCount");
        }
        return totalCount + HighBatchConstants.FILE_ENTER + fileFiledNames;
    }

    @Override
    public void deleteIoByParams(Map<String, Object> params) {
        String taCode = (String) params.get("taCode");
        if (Constant.HOWBUY_CM_TA.equals(taCode)) {
            exportHeVolCheckFileRecRepository.deleteCmBatchVolFile(taCode);
        } else {
            exportHeVolCheckFileRecRepository.deleteBatchVolFile(taCode);
        }
    }

    @Override
    public void insertIoByParams(Map<String, Object> params) {
        //生成数据
        String taCode = (String) params.get("taCode");
        int totalCount = 0;
        if (Constant.HOWBUY_CM_TA.equals(taCode)) {
            totalCount = exportHeVolCheckFileRecRepository.addCmBatchVolFile(taCode);
        } else {
            totalCount = exportHeVolCheckFileRecRepository.addBatchVolFile(taCode);
        }
        params.put("totalCount", totalCount);
        log.info("高端产品确认份额数量,totalCount={}", totalCount);
    }

    @Override
    public void specialProcess(TxtFileExportContext context, String absolutePath) throws Exception {

    }
}
