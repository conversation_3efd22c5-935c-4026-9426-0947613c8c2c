/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.service.batch.ackdayend.task;

import com.howbuy.tms.high.batch.dao.po.batch.HighForceRedMemoChgLogPo;
import com.howbuy.tms.high.batch.service.business.syncmodifyforceredeemmemo.SyncModifyForceRedeemMemoService;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @description:修改强减原因
 * @date 2018年5月29日 下午6:44:31
 * @since JDK 1.6
 */
public class ModifyForceRedeemMemoTask extends AbstractHowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(ModifyForceRedeemMemoTask.class);

    private HighForceRedMemoChgLogPo po;

    private SyncModifyForceRedeemMemoService syncModifyForceRedeemMemoService;

    @Override
    protected void callTask() {
        try {
            syncModifyForceRedeemMemoService.process(po);
        } catch (Exception ex) {
            logger.error("ModifyForceRedeemMemoTask|Error:{} ", ex.getMessage(), ex);
        }
    }

    public ModifyForceRedeemMemoTask(HighForceRedMemoChgLogPo po, SyncModifyForceRedeemMemoService syncModifyForceRedeemMemoService) {
        this.po = po;
        this.syncModifyForceRedeemMemoService = syncModifyForceRedeemMemoService;
    }
}
