package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.high.batch.dao.po.batch.CmFileProcessRecPo;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.service.file.fileimport.AbstractFileWebDavImportService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ParseBlackFileTask extends AbstractHowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(ParseBlackFileTask.class);
    private AbstractFileWebDavImportService cmBlackListFileImportService;
    private CmFileProcessRecPo cmFileProcessRecPo;
    private List<ThreadExceptionStatus> statusList;
    private String lastWorkDay;
    private String blackFilePathName;

    public ParseBlackFileTask(CmFileProcessRecPo cmFileProcessRecPo, List<ThreadExceptionStatus> statusList, String lastWorkDay, AbstractFileWebDavImportService cmBlackListFileImportService, String blackFilePathName) {
        this.cmBlackListFileImportService = cmBlackListFileImportService;
        this.cmFileProcessRecPo = cmFileProcessRecPo;
        this.statusList = statusList;
        this.lastWorkDay = lastWorkDay;
        this.blackFilePathName = blackFilePathName;
    }

    @Override
    protected void callTask() {
        try {
            FileImportContext context = new FileImportContext();
            context.setFileName(blackFilePathName);
            context.setRelationPath(File.separator + cmFileProcessRecPo.getDealDt() + File.separator + cmFileProcessRecPo.getsVersionNo() + File.separator);
            context.setTaTradeDt(cmFileProcessRecPo.getDealDt());
            context.getParams().put("importDt", cmFileProcessRecPo.getDealDt());
            context.getParams().put("versionNo", cmFileProcessRecPo.getVersionNo().toString());
            context.getParams().put("lastWorkDay", lastWorkDay);
            cmBlackListFileImportService.process(context);
        } catch (Exception e) {
            ThreadExceptionStatus status = new ThreadExceptionStatus();
            status.setExsitException(true);
            status.setException(e);
            statusList.add(status);
            String msg = String.format("处理当天全量直销黑名单文件异常,版本号:%s", cmFileProcessRecPo.getVersionNo());
            OpsMonitor.warn(msg, OpsMonitor.ERROR);
            logger.error("处理当天全量直销黑名单文件异常:", e);
        }
    }
}