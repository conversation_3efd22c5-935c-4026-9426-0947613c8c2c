package com.howbuy.tms.high.batch.service.business.ordersubmit;


import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.OrderUpdateType;
import com.howbuy.tms.common.enums.database.DiffProcessStatusEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.enums.database.TradeExcpSrcEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.high.batch.dao.po.batch.HighTradeExceptionPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.service.business.autoecontract.SpecialProductSignEcontractService;
import com.howbuy.tms.high.batch.service.business.refreshdealorderstatus.RefreshDealOrderStatusService;
import com.howbuy.tms.high.batch.service.repository.SimuFundCheckOrderRepository;
import com.howbuy.tms.high.batch.service.service.order.simufundcheckorder.SimuFundCheckOrderService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * 
 * @description:对账订单上报服务
 * @reason:
 * <AUTHOR>
 * @date 2017年7月6日 下午5:49:50
 * @since JDK 1.7
 */
public abstract class CheckOrderSubmitService{
    private Logger logger = LogManager.getLogger(this.getClass());

    /**
     * 私募对账订单表操作服务
     */
    @Autowired
    private SimuFundCheckOrderService simuFundCheckOrderService;
    @Autowired
    private SimuFundCheckOrderRepository simuFundCheckOrderRepository;
    @Autowired
    private RefreshDealOrderStatusService refreshDealOrderStatusService;

    @Autowired
    private CheckOrderSubmitCommonService checkOrderSubmitCommonService;

    @Autowired
    private SpecialProductSignEcontractService specialProductSignEcontractService;

    /**
     * 
     * submit:上报方法
     * 
     * @param checkOrderPo
     * @return
     * <AUTHOR>
     * @date 2017年7月11日 下午5:15:19
     */
    public BaseResponse submit(SimuFundCheckOrderManualSubmitVo checkOrderPo) {
        BaseResponse baseResponse = null;
        try {
            long start = System.currentTimeMillis();
            baseResponse = doSubmit(checkOrderPo);
            long end = System.currentTimeMillis();
            logger.info("doSubmit|cost time:{},response:{}", (end - start), JSON.toJSONString(baseResponse));
        } catch (Throwable e) {
            logger.error("doSubmit|call out System Exception.", e);
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_ACCESS_DUBBO_INTERFACE_FAILED,
                    MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_ACCESS_DUBBO_INTERFACE_FAILED));
        }
        return baseResponse;
    }

    /**
     * 
     * doSubmit:衍生类执行的钩子方法，执行上报操作
     * 
     * @param checkOrderPo
     * @return
     * <AUTHOR>
     * @date 2017年7月12日 上午9:12:54
     */
    protected abstract BaseResponse doSubmit(SimuFundCheckOrderManualSubmitVo checkOrderPo);

    /**
     * 
     * submitSuccess:上报成功时的钩子方法
     * 
     * @param checkOrderPo
     * <AUTHOR>
     * @date 2017年7月12日 上午9:12:44
     */
    protected abstract void submitSuccess(SimuFundCheckOrderPo checkOrderPo);

    /**
     * 
     * submitFail:上报失败时的钩子方法
     * 
     * @param checkOrderPo
     * <AUTHOR>
     * @date 2017年7月12日 上午9:12:34
     */
    protected abstract void submitFail(SimuFundCheckOrderPo checkOrderPo);

    /**
     * 
     * execPostSubmit:上报后处理模板方法
     * 
     * @param simuFundCheckOrderPo
     * @param response
     * <AUTHOR>
     * @date 2017年7月7日 下午2:19:05
     */
    public void execPostSubmit(SimuFundCheckOrderPo simuFundCheckOrderPo, BaseResponse response) {
        try {
            // 回写返回码与返回信息
            checkOrderSubmitCommonService.feedShortReturnCode(response);

            simuFundCheckOrderPo.setRetCode(response.getReturnCode());
            simuFundCheckOrderPo.setRetDesc(response.getDescription());
            // 客户信息重新脱敏
            simuFundCheckOrderPo.setIdNo(PrivacyUtil.encryptIdCard(simuFundCheckOrderPo.getIdNo()));
            simuFundCheckOrderPo.setCustName(simuFundCheckOrderPo.getCustName());
            simuFundCheckOrderPo.setBankAcct(PrivacyUtil.encryptBankAcct(simuFundCheckOrderPo.getBankAcct()));
            if (checkOrderSubmitCommonService.isSuccess(response) || checkOrderSubmitCommonService.isCallSubmited(response)) {
                // 上报成功
                // 更新对账订单状态
                // 更新操作
                // 成功时，TA工作日从私募返回
                String submitTaDt = response.getTaTradeDt();
                if (StringUtils.isEmpty(submitTaDt) || submitTaDt.equals(simuFundCheckOrderPo.getSubmitTaDt())) {
                    // 返回是旧的TA工作日，或返回是空，不更新
                    submitTaDt = null;
                }
                simuFundCheckOrderPo.setSubmitTaDt(submitTaDt);
                simuFundCheckOrderPo.setContractNo(response.getDealNo());

                // set预计确认日期
                String preActDt = checkOrderSubmitCommonService.queryPreAckDt(simuFundCheckOrderPo);
                simuFundCheckOrderPo.setPreAckDt(preActDt);

                // 上报完成后，更新订单状态
                simuFundCheckOrderService.updateSubmitSuccess(simuFundCheckOrderPo);

                // 特殊产品上报完成后，需要立即生成电子合同
                if(BusinessCodeEnum.SUBS.getMCode().equals(simuFundCheckOrderPo.getmBusiCode()) ||
                        BusinessCodeEnum.PURCHASE.getMCode().equals(simuFundCheckOrderPo.getmBusiCode()) ){
                    specialProductSignEcontractService.process(simuFundCheckOrderPo.getDealNo(), simuFundCheckOrderPo.getFundCode());
                }
            } else if (checkOrderSubmitCommonService.isCallError(response)) {
                // 调用失败
                SimuFundCheckOrderPo po = new SimuFundCheckOrderPo();
                po.setSubmitDealNo(simuFundCheckOrderPo.getSubmitDealNo());
                simuFundCheckOrderRepository.updateBySubmitDealNoSelective(po);
            } else {
                // 创建异常订单
                HighTradeExceptionPo highTradeExceptionPo = creeateHighTradeException(simuFundCheckOrderPo);
                // 上报失败
                // 业务类异常
                // 修改订单申请状态后，更新订单状态，新增账务变动明细
                // 失败时，TA工作日从PO获取（即保持与DB中一致）
                simuFundCheckOrderService.updateSubmitFailed(simuFundCheckOrderPo);
                // 更新订单状态或订单支付状态，记录监控表，发送kafka消息到es-center
                refreshDealOrderStatusService.refleshDealOrderStatus(simuFundCheckOrderPo.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
                // 记录上报异常明细
                checkOrderSubmitCommonService.addFundCheckOrderDiff(highTradeExceptionPo);
            }

        } catch (Exception ex) {
            logger.error("Error execPostSubmit {}", simuFundCheckOrderPo.getDealDtlNo(), ex);
        }
    }



    /**
     * 
     * creeateHighTradeException:(创建交易异常)
     * @param po
     * @return
     * <AUTHOR>
     * @date 2018年7月12日 下午4:28:33
     */
    private HighTradeExceptionPo creeateHighTradeException(SimuFundCheckOrderPo po){
        HighTradeExceptionPo highTradeExceptionPo = new HighTradeExceptionPo();
        BeanUtils.copyProperties(po, highTradeExceptionPo);
        highTradeExceptionPo.setTaTradeDt(po.getTradeDt());
        highTradeExceptionPo.setCheckFlag(Constant.CHECK_FLAG_YES);
        highTradeExceptionPo.setOperator(Constant.OPERATOR_SYS);
        highTradeExceptionPo.setChecker(Constant.OPERATOR_SYS);
        highTradeExceptionPo.setProcessStatus(DiffProcessStatusEnum.UNPROCESS.getKey());
        // 交易异常来源
        highTradeExceptionPo.setTradeExcepSrc(TradeExcpSrcEnum.SUBMIT_FAIL.getCode());
        highTradeExceptionPo.setSysCode(SysCodeEnum.BATCH_HIGH.getCode());
        
        return highTradeExceptionPo;
    }

    
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public List<SimuFundCheckOrderPo> getShareTransferCheckOrdersToSubmit(final Date startDtm) {
        return simuFundCheckOrderRepository.getShareTransferCheckOrdersToSubmit(startDtm);
    }
    
    
}
