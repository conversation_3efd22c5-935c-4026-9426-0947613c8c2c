<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.batch.dao.mapper.customize.order.CustomExportHeFundAckFileRecMapper">
    <delete id="deleteByTradeDtTaCode">
        delete
        from EXPORT_HE_FUND_ACK_FILE_REC
        where TA_CODE = #{taCode,jdbcType=VARCHAR}
          and (EXPORT_DT <![CDATA[ <= ]]> #{dDate,jdbcType=VARCHAR} or EXPORT_DT = DATE_FORMAT(NOW(), '%Y%m%d'))
    </delete>

    <insert id="insertBatchAppTrade">
        insert into EXPORT_HE_FUND_ACK_FILE_REC (export_dt, deal_no, deal_dtl_no, tx_acct_no,
                                                 cp_acct_no, dis_code, fund_code, ta_code, ack_dt, ack_amt, ack_vol,
                                                 ta_trade_dt, app_amt, app_vol, nav, fee, m_busi_code,
                                                 busi_code, protocol_no, protocol_type, product_code, dis_trans_seq,
                                                 FUND_DIV_MODE, tx_ack_flag, order_status, join_dt, create_dtm,
                                                 update_dtm,
                                                 PRODUCT_CHANNEL, SALES_MODEL, CURRENCY_UNIT, HBONE_N0,
                                                 PARENT_FUND_CODE, SUBMIT_TA_DT, RECSTAT, TRANSFER_PRICE,
                                                 IS_NO_TRADE_TRANSFER)
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               t.deal_no,
               t.deal_dtl_no,
               t.tx_acct_no,
               t1.cp_acct_no,
               t.dis_code,
               t.fund_code,
               t.ta_code,
               t.ack_dt,
               t.ack_amt,
               t.ack_vol,
               t.ta_trade_dt,
               t.app_amt,
               t.app_vol,
               t.nav,
               t.fee,
               t.m_busi_code,
               t1.z_busi_code,
               t1.protocol_no,
               t1.protocol_type,
               t1.product_code,
               t.deal_dtl_no,
               t.fund_div_mode,
               t.tx_ack_flag,
               t1.order_status,
               t.join_dt,
               NOW(),
               NOW(),
               t.PRODUCT_CHANNEL,
               '2',
               '156',
               '',
               t.fund_code,
               t.SUBMIT_TA_DT,
               '0',
               t.TRANSFER_PRICE,
               t.IS_NO_TRADE_TRANSFER
        from HIGH_DEAL_ORDER_DTL t
                 inner join DEAL_ORDER t1 on t.deal_no = t1.deal_no
        where t.tx_ack_flag in ('3', '4')
          and (t.ack_amt is not null or t.ack_vol is not null)
          and (ACK_DT = #{tradeDt,jdbcType=VARCHAR} or t.UPDATE_DTM <![CDATA[ >= ]]> #{beginDate,jdbcType=TIMESTAMP})
          and TA_CODE = #{taCode,jdbcType=VARCHAR}
          and (CONTINUANCE_FLAG is null or CONTINUANCE_FLAG != '1')
          and (STAGE_FLAG is null or STAGE_FLAG != '1')
        union all
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               a.DEAL_NO,
               a.DEAL_DTL_NO,
               a.TX_ACCT_NO,
               a.CP_ACCT_NO,
               a1.DIS_CODE,
               a.FUND_CODE,
               a2.TA_CODE,
               a.ACK_DT,
               a.ACK_AMT,
               a.ACK_VOL,
               a2.TA_TRADE_DT,
               a2.APP_AMT,
               a2.APP_VOL,
               a.NAV,
               a.FEE,
               a2.M_BUSI_CODE,
               a1.Z_BUSI_CODE,
               a1.PROTOCOL_NO,
               a1.PROTOCOL_TYPE,
               a.FUND_CODE,
               a.DEAL_DTL_NO,
               a2.FUND_DIV_MODE,
               a.TX_ACK_FLAG,
               a1.ORDER_STATUS,
               a.JOIN_DT,
               NOW(),
               NOW(),
               a2.PRODUCT_CHANNEL,
               '2',
               '156',
               '',
               a.FUND_CODE,
               a2.SUBMIT_TA_DT,
               '0',
               0,
               '0'
        from HIGH_REDEEM_SPLIT_DTL a
                 inner join DEAL_ORDER a1 on a.MAIN_DEAL_NO = a1.DEAL_NO
                 inner join HIGH_DEAL_ORDER_DTL a2 on a.MAIN_DEAL_NO = a2.DEAL_NO
        where a.TX_ACK_FLAG = '4'
          and a2.TA_CODE = #{taCode,jdbcType=VARCHAR}
          and (a.ACK_DT = #{tradeDt,jdbcType=VARCHAR} or a.UPDATE_DTM <![CDATA[ >= ]]> #{beginDate,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertCmBatchAppTradeByLastWorkDay">
        insert into EXPORT_HE_FUND_ACK_FILE_REC (export_dt, deal_no, deal_dtl_no, tx_acct_no,
                                                 cp_acct_no, dis_code, fund_code, ta_code, ack_dt, ack_amt, ack_vol,
                                                 ta_trade_dt, app_amt, app_vol, nav, fee, m_busi_code,
                                                 busi_code, protocol_no, protocol_type, product_code, dis_trans_seq,
                                                 FUND_DIV_MODE, tx_ack_flag, order_status, join_dt, create_dtm,
                                                 update_dtm,
                                                 PRODUCT_CHANNEL, SALES_MODEL, CURRENCY_UNIT, HBONE_N0,
                                                 PARENT_FUND_CODE,
                                                 SUBMIT_TA_DT, RECSTAT, TRANSFER_PRICE, IS_NO_TRADE_TRANSFER,
                                                 balance_factor)
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               t.APPSERIALNO,
               t.APPSERIALNO,
               t.TXACCTNO,
               '',
               t.DISCODE,
               t.FUNDCODE,
               'HOWBUYCM',
               t.TRADEDT,
               t.ACKAMT,
               IF(t.BUSICODE = '19E', IF(t.ACKVOL IS NULL, NULL, ABS(t.ACKVOL)), t.ACKVOL),
               t.TRADEDT,
               t.APPAMT,
               IF(t.BUSICODE = '19E', IF(t.APPVOL IS NULL, NULL, ABS(t.APPVOL)), t.APPVOL),
               t.NAV,
               t.FEE,
               case
                   when t.BUSICODE = '122' then '1122'
                   when t.BUSICODE = '124' then '1124'
                   when t.BUSICODE = '120' then '1130'
                   when t.BUSICODE = '142' then '1142'
                   when t.BUSICODE = '143' then '1143'
                   when t.BUSICODE = '144' then '1144'
                   when t.BUSICODE = '145' then '1145'
                   when t.BUSICODE = '151' then '1151'
                   when t.BUSICODE = '12B' then '1122'
                   when t.BUSICODE = '12A' then '112A'
                   when t.BUSICODE = '134' then '1134'
                   when t.BUSICODE = '135' then '1135'
                   when t.BUSICODE = '150' then '1150'
                   when t.BUSICODE = '13B' then '1142'
                   when t.BUSICODE = '13C' then '1122'
                   when t.BUSICODE = '12B' then '1122'
                   when t.BUSICODE = '19E' then IF(t.APPVOL > 0, '1144', '1145')
                   ELSE t.BUSICODE
                   end as BUSICODE,
               '',
               '',
               '',
               t.FUNDCODE,
               t.APPSERIALNO,
               t.DIVMODE,
               t.ORDERSTATE,
               t.ORDERSTATE,
               '',
               NOW(),
               NOW(),
               '',
               '1',
               t.CURRENCY,
               t.HBONENO,
               t.MJJDM,
               t.NEW_TRADE_DT,
               case
                   when t.RECSTAT = '1' then '1'
                   when t.MODDT = t.IMPORTDT then '2'
                   ELSE t.RECSTAT
                   end as RECSTAT,
               TRANSFER_PRICE,
               IS_NO_TRADE_TRANSFER,
               balance_factor
        from CM_CUSTTRADE_DIRECT t
        where t.orderstate in ('2', '3')
          and t.importdt >= #{lastWorkDay,jdbcType=VARCHAR}
          and t.BUSICODE != '136'
        union all
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               CONCAT(t.APPSERIALNO, '1'),
               CONCAT(t.APPSERIALNO, '1'),
               t.TXACCTNO,
               '',
               t.DISCODE,
               t.FUNDCODE,
               'HOWBUYCM',
               t.TRADEDT,
               t.ACKAMT,
               t.ACKVOL,
               t.TRADEDT,
               t.APPAMT,
               t.APPVOL,
               t.NAV,
               t.FEE,
               '1124'  as BUSICODE,
               '',
               '',
               '',
               t.FUNDCODE,
               CONCAT(t.APPSERIALNO, '1'),
               t.DIVMODE,
               t.ORDERSTATE,
               t.ORDERSTATE,
               '',
               NOW(),
               NOW(),
               '',
               '1',
               t.CURRENCY,
               t.HBONENO,
               t.MJJDM,
               t.NEW_TRADE_DT,
               case
                   when t.RECSTAT = '1' then '1'
                   when t.MODDT = t.IMPORTDT then '2'
                   ELSE t.RECSTAT
                   end as RECSTAT,
               TRANSFER_PRICE,
               IS_NO_TRADE_TRANSFER,
               balance_factor
        from CM_CUSTTRADE_DIRECT t
        where t.orderstate in ('2', '3')
          and t.importdt >= #{lastWorkDay,jdbcType=VARCHAR}
          and t.BUSICODE = '136'
        union all
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               CONCAT(t.APPSERIALNO, '2'),
               CONCAT(t.APPSERIALNO, '2'),
               t.TXACCTNO,
               '',
               t.DISCODE,
               t.transfer_in_fund_code,
               'HOWBUYCM',
               t.TRADEDT,
               t.transfer_in_ack_amt,
               t.transfer_in_ack_vol,
               t.TRADEDT,
               t.APPAMT,
               t.APPVOL,
               t.transfer_in_ack_nav,
               t.FEE,
               '1122'  as BUSICODE,
               '',
               '',
               '',
               t.transfer_in_fund_code,
               CONCAT(t.APPSERIALNO, '2'),
               t.DIVMODE,
               t.ORDERSTATE,
               t.ORDERSTATE,
               '',
               NOW(),
               NOW(),
               '',
               '1',
               t.CURRENCY,
               t.HBONENO,
               t.transfer_in_main_fund_code,
               t.NEW_TRADE_DT,
               case
                   when t.RECSTAT = '1' then '1'
                   when t.MODDT = t.IMPORTDT then '2'
                   ELSE t.RECSTAT
                   end as RECSTAT,
               TRANSFER_PRICE,
               IS_NO_TRADE_TRANSFER,
               transfer_in_balance_factor
        from CM_CUSTTRADE_DIRECT t
        where t.orderstate in ('2', '3')
          and t.importdt >= #{lastWorkDay,jdbcType=VARCHAR}
          and t.BUSICODE = '136'
        union all
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               t.APPSERIALNO,
               t.APPSERIALNO,
               t.TXACCTNO,
               '',
               t.DISCODE,
               t.FUNDCODE,
               'HOWBUYCM',
               t.TRADEDT,
               t.ACKAMT,
               t.ACKVOL,
               t.TRADEDT,
               t.APPAMT,
               t.APPVOL,
               t.NAV,
               t.FEE,
               case
                   when t.BUSICODE = '122' then '1122'
                   when t.BUSICODE = '124' then '1124'
                   when t.BUSICODE = '120' then '1130'
                   when t.BUSICODE = '142' then '1142'
                   when t.BUSICODE = '143' then '1143'
                   when t.BUSICODE = '144' then '1144'
                   when t.BUSICODE = '145' then '1145'
                   when t.BUSICODE = '151' then '1151'
                   when t.BUSICODE = '12B' then '1122'
                   when t.BUSICODE = '12A' then '112A'
                   when t.BUSICODE = '134' then '1134'
                   when t.BUSICODE = '135' then '1135'
                   when t.BUSICODE = '150' then '1150'
                   when t.BUSICODE = '13B' then '1142'
                   when t.BUSICODE = '13C' then '1122'
                   when t.BUSICODE = '12B' then '1122'
                   when t.BUSICODE = '19E' then IF(t.APPVOL > 0, '1144', '1145')
                   ELSE t.BUSICODE
                   end as BUSICODE,
               '',
               '',
               '',
               t.FUNDCODE,
               t.APPSERIALNO,
               t.DIVMODE,
               t.ORDERSTATE,
               t.ORDERSTATE,
               '',
               NOW(),
               NOW(),
               '',
               '1',
               t.CURRENCY,
               t.HBONENO,
               t.MJJDM,
               t.NEW_TRADE_DT,
               case
                   when t.RECSTAT = '1' then '1'
                   when t.MODDT = t.IMPORTDT then '2'
                   ELSE t.RECSTAT
                   end as RECSTAT,
               TRANSFER_PRICE,
               IS_NO_TRADE_TRANSFER,
               balance_factor
        from CM_CUSTTRADE_DIRECT t
        where t.orderstate = '1'
          and t.importdt >= #{lastWorkDay,jdbcType=VARCHAR}
          and (t.ACKAMT > 0 or t.ACKVOL > 0)
    </insert>

    <insert id="insertCmBatchAppTradeByLastVersion">
        insert into EXPORT_HE_FUND_ACK_FILE_REC (export_dt, deal_no, deal_dtl_no, tx_acct_no,
                                                 cp_acct_no, dis_code, fund_code, ta_code, ack_dt, ack_amt, ack_vol,
                                                 ta_trade_dt, app_amt, app_vol, nav, fee, m_busi_code,
                                                 busi_code, protocol_no, protocol_type, product_code, dis_trans_seq,
                                                 FUND_DIV_MODE, tx_ack_flag, order_status, join_dt, create_dtm,
                                                 update_dtm,
                                                 PRODUCT_CHANNEL, SALES_MODEL, CURRENCY_UNIT, HBONE_N0,
                                                 PARENT_FUND_CODE, SUBMIT_TA_DT, RECSTAT, TRANSFER_PRICE,
                                                 IS_NO_TRADE_TRANSFER, balance_factor)
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               t.APPSERIALNO,
               t.APPSERIALNO,
               t.TXACCTNO,
               '',
               t.DISCODE,
               t.FUNDCODE,
               'HOWBUYCM',
               t.TRADEDT,
               t.ACKAMT,
               IF(t.BUSICODE = '19E', IF(t.ACKVOL IS NULL, NULL, ABS(t.ACKVOL)), t.ACKVOL),
               t.TRADEDT,
               t.APPAMT,
               IF(t.BUSICODE = '19E', IF(t.APPVOL IS NULL, NULL, ABS(t.APPVOL)), t.APPVOL),
               t.NAV,
               t.FEE,
               case
                   when t.BUSICODE = '122' then '1122'
                   when t.BUSICODE = '124' then '1124'
                   when t.BUSICODE = '120' then '1130'
                   when t.BUSICODE = '142' then '1142'
                   when t.BUSICODE = '143' then '1143'
                   when t.BUSICODE = '144' then '1144'
                   when t.BUSICODE = '145' then '1145'
                   when t.BUSICODE = '151' then '1151'
                   when t.BUSICODE = '12B' then '1122'
                   when t.BUSICODE = '12A' then '112A'
                   when t.BUSICODE = '134' then '1134'
                   when t.BUSICODE = '135' then '1135'
                   when t.BUSICODE = '150' then '1150'
                   when t.BUSICODE = '13B' then '1142'
                   when t.BUSICODE = '12B' then '1122'
                   when t.BUSICODE = '13C' then '1122'
                   when t.BUSICODE = '19E' then IF(t.APPVOL > 0, '1144', '1145')
                   ELSE t.BUSICODE
                   end as BUSICODE,
               '',
               '',
               '',
               t.FUNDCODE,
               t.APPSERIALNO,
               t.DIVMODE,
               t.ORDERSTATE,
               t.ORDERSTATE,
               '',
               NOW(),
               NOW(),
               '',
               '1',
               t.CURRENCY,
               t.HBONENO,
               t.MJJDM,
               t.NEW_TRADE_DT,
               case
                   when t.RECSTAT = '1' then '1'
                   when t.MODDT = t.IMPORTDT then '2'
                   ELSE t.RECSTAT
                   end as RECSTAT,
               t.TRANSFER_PRICE,t.IS_NO_TRADE_TRANSFER,t.balance_factor
        from CM_CUSTTRADE_DIRECT t
        where t.orderstate in ('2', '3') and  t.BUSICODE !='136'
          and t.VERSIONNO > #{lastVersion,jdbcType=VARCHAR}
        union all
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               CONCAT(t.APPSERIALNO, '1'),
               CONCAT(t.APPSERIALNO, '1'),
               t.TXACCTNO,
               '',
               t.DISCODE,
               t.FUNDCODE,
               'HOWBUYCM',
               t.TRADEDT,
               t.ACKAMT,
               t.ACKVOL,
               t.TRADEDT,
               t.APPAMT,
               t.APPVOL,
               t.NAV,
               t.FEE,
               '1124'  as BUSICODE,
               '',
               '',
               '',
               t.FUNDCODE,
               CONCAT(t.APPSERIALNO, '1'),
               t.DIVMODE,
               t.ORDERSTATE,
               t.ORDERSTATE,
               '',
               NOW(),
               NOW(),
               '',
               '1',
               t.CURRENCY,
               t.HBONENO,
               t.MJJDM,
               t.NEW_TRADE_DT,
               case
                   when t.RECSTAT = '1' then '1'
                   when t.MODDT = t.IMPORTDT then '2'
                   ELSE t.RECSTAT
                   end as RECSTAT,
        t.TRANSFER_PRICE,
        t.IS_NO_TRADE_TRANSFER,
        t.balance_factor
        from CM_CUSTTRADE_DIRECT t
        where t.orderstate in ('2', '3')
          and t.VERSIONNO > #{lastVersion,jdbcType=VARCHAR}
          and t.BUSICODE = '136'
        union all
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               CONCAT(t.APPSERIALNO, '2'),
               CONCAT(t.APPSERIALNO, '2'),
               t.TXACCTNO,
               '',
               t.DISCODE,
               t.transfer_in_fund_code,
               'HOWBUYCM',
               t.TRADEDT,
               t.transfer_in_ack_amt,
               t.transfer_in_ack_vol,
               t.TRADEDT,
               t.APPAMT,
               t.APPVOL,
               t.transfer_in_ack_nav,
               t.FEE,
               '1122'  as BUSICODE,
               '',
               '',
               '',
               t.transfer_in_fund_code,
               CONCAT(t.APPSERIALNO, '2'),
               t.DIVMODE,
               t.ORDERSTATE,
               t.ORDERSTATE,
               '',
               NOW(),
               NOW(),
               '',
               '1',
               t.CURRENCY,
               t.HBONENO,
               t.transfer_in_main_fund_code,
               t.NEW_TRADE_DT,
               case
                   when t.RECSTAT = '1' then '1'
                   when t.MODDT = t.IMPORTDT then '2'
                   ELSE t.RECSTAT
                   end as RECSTAT,
        t.TRANSFER_PRICE,
        t.IS_NO_TRADE_TRANSFER,
        t.transfer_in_balance_factor
        from CM_CUSTTRADE_DIRECT t
        where t.orderstate in ('2', '3')
          and t.VERSIONNO > #{lastVersion,jdbcType=VARCHAR}
          and t.BUSICODE = '136'
        union all
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               t.APPSERIALNO,
               t.APPSERIALNO,
               t.TXACCTNO,
               '',
               t.DISCODE,
               t.FUNDCODE,
               'HOWBUYCM',
               t.TRADEDT,
               t.ACKAMT,
               t.ACKVOL,
               t.TRADEDT,
               t.APPAMT,
               t.APPVOL,
               t.NAV,
               t.FEE,
               case
                   when t.BUSICODE = '122' then '1122'
                   when t.BUSICODE = '124' then '1124'
                   when t.BUSICODE = '120' then '1130'
                   when t.BUSICODE = '142' then '1142'
                   when t.BUSICODE = '143' then '1143'
                   when t.BUSICODE = '144' then '1144'
                   when t.BUSICODE = '12B' then '1122'
                   when t.BUSICODE = '12A' then '112A'
                   when t.BUSICODE = '145' then '1145'
                   when t.BUSICODE = '151' then '1151'
                   when t.BUSICODE = '134' then '1134'
                   when t.BUSICODE = '135' then '1135'
                   when t.BUSICODE = '150' then '1150'
                   when t.BUSICODE = '13B' then '1142'
                   when t.BUSICODE = '13C' then '1122'
                   when t.BUSICODE = '12B' then '1122'
                   when t.BUSICODE = '19E' then IF(t.APPVOL > 0, '1144', '1145')
                   ELSE t.BUSICODE
                   end as BUSICODE,
               '',
               '',
               '',
               t.FUNDCODE,
               t.APPSERIALNO,
               t.DIVMODE,
               t.ORDERSTATE,
               t.ORDERSTATE,
               '',
               NOW(),
               NOW(),
               '',
               '1',
               t.CURRENCY,
               t.HBONENO,
               t.MJJDM,
               t.NEW_TRADE_DT,
               case
                   when t.RECSTAT = '1' then '1'
                   when t.MODDT = t.IMPORTDT then '2'
                   ELSE t.RECSTAT
                   end as RECSTAT,
        t.TRANSFER_PRICE,
        t.IS_NO_TRADE_TRANSFER,
        t.balance_factor
        from CM_CUSTTRADE_DIRECT t
        where t.orderstate = '1'
          and t.VERSIONNO > #{lastVersion,jdbcType=VARCHAR}
          and (t.ACKAMT > 0 or t.ACKVOL > 0)
    </insert>

    <select id="selectByTradeDtTaCode" resultType="com.howbuy.tms.high.batch.dao.po.order.ExportHeFundAckFileRecPo">
        select export_dt            exportDt,
               deal_no              dealNo,
               deal_dtl_no          dealDtlNo,
               tx_acct_no           txAcctNo,
               cp_acct_no           cpAcctNo,
               dis_code             disCode,
               fund_code            fundCode,
               ta_code              taCode,
               ack_dt               ackDt,
               ack_amt              ackAmt,
               ack_vol              ackVol,
               ta_trade_dt          taTradeDt,
               app_amt              appAmt,
               app_vol              appVol,
               nav,
               fee,
               m_busi_code          mBusiCode,
               busi_code            busiCode,
               protocol_no          protocolNo,
               protocol_type        protocolType,
               product_code         productCode,
               dis_trans_seq        disTransSeq,
               FUND_DIV_MODE        fundDivMode,
               tx_ack_flag          txAckFlag,
               order_status         orderStatus,
               join_dt              joinDt,
               create_dtm           createDtm,
               update_dtm           updateDtm,
               PRODUCT_CHANNEL      productChannel,
               SALES_MODEL          salesModel,
               CURRENCY_UNIT        currencyUnit,
               HBONE_N0             hboneN0,
               PARENT_FUND_CODE     parentFundCode,
               SUBMIT_TA_DT         submitTaDt,
               RECSTAT              recstat,
               TRANSFER_PRICE       transferPrice,
               IS_NO_TRADE_TRANSFER isNoTradeTransfer,
               balance_factor balanceFactor
        from EXPORT_HE_FUND_ACK_FILE_REC
        where ta_code = #{taCode,jdbcType=VARCHAR}
          and EXPORT_DT = DATE_FORMAT(NOW(), '%Y%m%d')
    </select>

    <insert id="insertBatchApplastWorkDay">
        insert into EXPORT_HE_FUND_ACK_FILE_REC (export_dt, deal_no, deal_dtl_no, tx_acct_no,
                                                 cp_acct_no, dis_code, fund_code, ta_code, ack_dt, ack_amt, ack_vol,
                                                 ta_trade_dt, app_amt, app_vol, nav, fee, m_busi_code,
                                                 busi_code, protocol_no, protocol_type, product_code, dis_trans_seq,
                                                 FUND_DIV_MODE, tx_ack_flag, order_status, join_dt, create_dtm,
                                                 update_dtm,
                                                 PRODUCT_CHANNEL, SALES_MODEL, CURRENCY_UNIT, HBONE_N0,
                                                 PARENT_FUND_CODE, SUBMIT_TA_DT, RECSTAT, TRANSFER_PRICE,
                                                 IS_NO_TRADE_TRANSFER,balance_factor)
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               t.deal_no,
               t.deal_dtl_no,
               t.tx_acct_no,
               t1.cp_acct_no,
               t.dis_code,
               t.fund_code,
               t.ta_code,
               t.ack_dt,
               t.ack_amt,
               t.ack_vol,
               t.ta_trade_dt,
               t.app_amt,
               t.app_vol,
               t.nav,
               t.fee,
               t.m_busi_code,
               t1.z_busi_code,
               t1.protocol_no,
               t1.protocol_type,
               t1.product_code,
               t.deal_dtl_no,
               t.fund_div_mode,
               t.tx_ack_flag,
               t1.order_status,
               t.join_dt,
               NOW(),
               NOW(),
               t.PRODUCT_CHANNEL,
               '2',
               '156',
               '',
               t.fund_code,
               t.SUBMIT_TA_DT,
               '0',
               t.TRANSFER_PRICE,
               t.IS_NO_TRADE_TRANSFER,
               null
        from HIGH_DEAL_ORDER_DTL t
                 inner join DEAL_ORDER t1 on t.deal_no = t1.deal_no
        where t.tx_ack_flag in ('3', '4')
          and (t.ack_amt is not null or t.ack_vol is not null)
          and ACK_DT = #{lastWorkDay,jdbcType=VARCHAR}
          and TA_CODE = #{taCode,jdbcType=VARCHAR}
          and (CONTINUANCE_FLAG is null or CONTINUANCE_FLAG != '1')
          and (STAGE_FLAG is null or STAGE_FLAG != '1')
          and not exists(
                select 1
                from EXPORT_HE_FUND_ACK_FILE_REC LE
                where LE.DEAL_NO = t.DEAL_NO
                  and LE.EXPORT_DT = #{lastWorkDay,jdbcType=VARCHAR}
            )
        union all
        select DATE_FORMAT(NOW(), '%Y%m%d'),
               a.DEAL_NO,
               a.DEAL_DTL_NO,
               a.TX_ACCT_NO,
               a.CP_ACCT_NO,
               a1.DIS_CODE,
               a.FUND_CODE,
               a2.TA_CODE,
               a.ACK_DT,
               a.ACK_AMT,
               a.ACK_VOL,
               a2.TA_TRADE_DT,
               a2.APP_AMT,
               a2.APP_VOL,
               a.NAV,
               a.FEE,
               a2.M_BUSI_CODE,
               a1.Z_BUSI_CODE,
               a1.PROTOCOL_NO,
               a1.PROTOCOL_TYPE,
               a.FUND_CODE,
               a.DEAL_DTL_NO,
               a2.FUND_DIV_MODE,
               a.TX_ACK_FLAG,
               a1.ORDER_STATUS,
               a.JOIN_DT,
               NOW(),
               NOW(),
               a2.PRODUCT_CHANNEL,
               '2',
               '156',
               '',
               a.FUND_CODE,
               a2.SUBMIT_TA_DT,
               '0',
               0,
               '0',
                null
        from HIGH_REDEEM_SPLIT_DTL a
                 inner join DEAL_ORDER a1 on a.MAIN_DEAL_NO = a1.DEAL_NO
                 inner join HIGH_DEAL_ORDER_DTL a2 on a.MAIN_DEAL_NO = a2.DEAL_NO
        where a.TX_ACK_FLAG = '4'
          and a2.TA_CODE = #{taCode,jdbcType=VARCHAR}
          and DATE_FORMAT(a.CREATE_DTM, '%Y%m%d') = #{lastWorkDay,jdbcType=VARCHAR}
          and not exists(
                select 1
                from EXPORT_HE_FUND_ACK_FILE_REC a3
                where a3.DEAL_NO = a.MAIN_DEAL_NO
                  and a3.EXPORT_DT = #{lastWorkDay,jdbcType=VARCHAR}
            )
    </insert>
</mapper>