package com.howbuy.tms.high.batch.service.service.file.fileexport.txt;

import com.github.pagehelper.Page;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.dfile.HTextFileWriter;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.batch.HighFundFileStatusPo;
import com.howbuy.tms.high.batch.service.builder.HighFileStatusBuilder;
import com.howbuy.tms.high.batch.service.common.*;
import com.howbuy.tms.high.batch.service.repository.HighFundFileStatusRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.CommonConfService;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.bean.TxtFileExportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 文件导出基类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public abstract class AbstractTxtFileExportService<T> extends CommonConfService {

    @Autowired
    private HighFundFileStatusRepository highFundFileStatusRepository;
    @Autowired
    private HighFileStatusBuilder highFileStatusBuilder;

    /**
     * 临时文件后缀
     */
    private static final String TMP_FILE_SUFFIX = ".tmp";
    protected static final String SEPARATOR = "|";
    /**
     * 分页大小
     */
    private static final int PAGE_SIZE = 5000;

    public String process(TxtFileExportContext context) throws Exception {
        log.info("file export start, context={}", context);
        // 文件sdk处理
        String fileUrl = fileSdkProcess(context);
        log.info("生成txt文件的url={}", fileUrl);
        return fileUrl;
    }

    /**
     * 文件sdk处理
     *
     * @param context
     * @throws Exception
     */
    private String fileSdkProcess(TxtFileExportContext context) throws Exception {
        // 新文件体系,只有打开开关的时候,才会用正确的路径,否则都是默认路径
        String businessCode = getBusinessCode();
        Map<String, Object> params = context.getParams();
        String relationPath = context.getRelationPath();
        // 正式文件名
        String fileName = context.getFileName();
        // 临时文件名
        String fileNameTmp = fileName + TMP_FILE_SUFFIX;

        if (StringUtils.isEmpty((String) params.get("taTradeDt"))) {
            params.put("taTradeDt", DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD));
        }
        // 临时文件
        FileSdkPathInfo tmpFileSdkPathInfo = new FileSdkPathInfo();
        tmpFileSdkPathInfo.setFileName(fileNameTmp);
        tmpFileSdkPathInfo.setBusinessCode(businessCode);
        tmpFileSdkPathInfo.setMiddlePath(relationPath);
        // 正式文件
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setFileName(fileName);
        fileSdkPathInfo.setBusinessCode(businessCode);
        fileSdkPathInfo.setMiddlePath(relationPath);
        // 文件绝对路径
        String absolutePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);

        // 判断文件导出记录是否存在，若为处理中，则抛出异常，否则更新为处理中;若不存在，则创建记录
        String recordNo = updateFileSatusProcessing(businessCode, params, absolutePath);

        // 删除正式文件fileName
        if (FileSdkUtil.exists(fileSdkPathInfo)) {
            FileSdkUtil.deleteFile(fileSdkPathInfo);
        }
        // 删除临时文件fileNameTmp
        if (FileSdkUtil.exists(tmpFileSdkPathInfo)) {
            FileSdkUtil.deleteFile(tmpFileSdkPathInfo);
        }
        try (HTextFileWriter hTextFileWriter = FileSdkUtil.buildHTextFileWriter(tmpFileSdkPathInfo)) {
            // 删除io导出表数据
            deleteIoByParams(params);
            // 插入数据到io导出表
            insertIoByParams(params);
            // 写入文件头
            String fileHeader = createFileHeader(params);
            if (StringUtils.isNotEmpty(fileHeader)) {
                hTextFileWriter.write(fileHeader);
                hTextFileWriter.newLine();
            }
            // 写文件内容
            int pageNum = 1;
            Page<T> queryResult = new Page<>();
            do {
                queryResult = queryIoByParams(params, pageNum, PAGE_SIZE);
                // 当前数量
                int currentCount = PAGE_SIZE * (pageNum - 1);
                if (CollectionUtils.isNotEmpty(queryResult.getResult())) {
                    for (T object : queryResult.getResult()) {
                        currentCount++;
                        String line = createFileLine(params, object, currentCount);
                        hTextFileWriter.write(line);
                        hTextFileWriter.newLine();
                    }
                }
                pageNum++;
            } while (pageNum <= queryResult.getPages());
            // 手动刷新缓冲区数据到文件里
            hTextFileWriter.flush();
            hTextFileWriter.close();
            // 临时文件重命名为正式文件
            FileSdkUtil.rename(tmpFileSdkPathInfo, fileName);
            // 特殊处理，例如压缩文件
            specialProcess(context, absolutePath);
        } catch (Exception e) {
            log.error("create file{} failed.", fileName, e);
            // 更新文件处理状态为失败
            highFundFileStatusRepository.updateFileOpStatus(recordNo, HighFileOpStatusEnum.GENERATE_FAIL.getCode());
            // 告警
            String msg = "文件导出失败,businessCode:" + businessCode + "middlePath:" + relationPath + "fileName:" + fileName;
            log.info(msg);
            OpsMonitor.warn(msg, OpsMonitor.ERROR);
            throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR, "导出文件失败", e);
        }

        // 更新文件处理状态为成功
        highFundFileStatusRepository.updateFileOpStatus(recordNo, HighFileOpStatusEnum.GENERATE_SUCCESS.getCode());
        log.info("文件{}，生成成功", absolutePath);
        return absolutePath;
    }

    /**
     * 获取文件 businessCode
     *
     * @param
     * @return
     * <AUTHOR>
     */
    public abstract String getBusinessCode();

    /**
     * 分页查询io表数据
     *
     * @param params
     * @param pageNum
     * @param pageSize
     * @return
     */
    public abstract Page<T> queryIoByParams(Map<String, Object> params, int pageNum, int pageSize);

    /**
     * 表数据组装为文件行
     *
     * @param object
     * @return
     */
    public abstract String createFileLine(Map<String, Object> params, T object, int currentCount) throws Exception;

    /**
     * 更新文件处理状态为处理中
     *
     * @param businessCode
     * @param params
     * @param absolutePath
     */
    private String updateFileSatusProcessing(String businessCode, Map<String, Object> params, String absolutePath) {
        HighFundFileStatusPo highFundFileStatusPo = highFundFileStatusRepository.selectByTradeDateAndFileType(params.get("taTradeDt").toString(), businessCode, absolutePath);
        if (highFundFileStatusPo == null) {
            highFundFileStatusPo = highFileStatusBuilder.createHighFileStatus(params.get("taTradeDt").toString(), businessCode, absolutePath, HighFileOptionEnum.GENERATE.getCode());
            highFundFileStatusRepository.insertSelective(highFundFileStatusPo);
        } else {
            if (Objects.equals(highFundFileStatusPo.getFileOpStatus(), HighFileOpStatusEnum.PROCESSING.getCode())) {
                throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_SYSTEM_ERROR, "文件正在生成中，请稍后再试");
            }
            highFundFileStatusPo.setFileOpStatus(HighFileOpStatusEnum.PROCESSING.getCode());
            highFundFileStatusPo.setUpdateDtm(new Date());
            highFundFileStatusRepository.updateByPrimaryKeySelective(highFundFileStatusPo);
        }
        return highFundFileStatusPo.getRecordNo();
    }

    /**
     * 创建文件头
     *
     * @param params
     */
    public abstract String createFileHeader(Map<String, Object> params);

    /**
     * 删除io表数据
     *
     * @param params
     */
    public abstract void deleteIoByParams(Map<String, Object> params);

    /**
     * 插入数据到io导出表
     *
     * @param params
     */
    public abstract void insertIoByParams(Map<String, Object> params);

    /**
     * 特殊处理
     */
    public abstract void specialProcess(TxtFileExportContext context, String absolutePath) throws Exception;
}
