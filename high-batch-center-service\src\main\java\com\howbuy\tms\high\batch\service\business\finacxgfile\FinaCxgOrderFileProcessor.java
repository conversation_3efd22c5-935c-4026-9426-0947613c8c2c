/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.finacxgfile;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProductTypeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.high.batch.dao.po.batch.FinaFileProcessRecPo;
import com.howbuy.tms.high.batch.dao.po.batch.HighCxgSaveFileRecPo;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.business.cxgorderfile.CxgOrderFileGenerateProcessor;
import com.howbuy.tms.high.batch.service.business.finafilemonitor.FinaAbstractFileService;
import com.howbuy.tms.high.batch.service.business.message.FinaFileMessageBean;
import com.howbuy.tms.high.batch.service.common.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import com.howbuy.tms.high.batch.service.repository.HighCxgSaveFileRecRepository;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.FinaCxgOrderFileImportService;
import com.howbuy.tms.high.batch.service.task.CxgOrderFileGenerateTask;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description:资金给中台储蓄罐存入文件
 * @reason:
 * @date 2018年5月30日 下午2:07:59
 * @since JDK 1.6
 */
@Service("finaCxgOrderFileProcessor")
public class FinaCxgOrderFileProcessor extends FinaAbstractFileService {
    private final Logger logger = LogManager.getLogger();

    @Autowired
    private HighCxgSaveFileRecRepository highCxgSaveFileRecRepository;
    @Autowired
    private CxgOrderFileGenerateProcessor cxgOrderFileGenerateProcessor;
    @Autowired
    private FinaCxgOrderFileImportService finaCxgOrderFileImportService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    @Override
    public void process(FinaFileMessageBean finaFileMessageBean) {
        logger.info("FinaCxgOrderFileProcessor|process start");
        FinaFileProcessRecPo finaFileProcessRecPo = new FinaFileProcessRecPo();
        try {
            // 消息数据落库
            preFileProcess(finaFileMessageBean, finaFileProcessRecPo);
            // 解析并保存文件内容
            finaCxgOrderFileImport(finaFileMessageBean, finaFileProcessRecPo);
            // 数据处理
            dataExecute(finaFileMessageBean.getExportDt(), finaFileProcessRecPo.getRecordNo());
        } catch (Exception e) {
            if (!StringUtils.isEmpty(finaFileProcessRecPo.getRecordNo())) {
                finaFileProcessRecPo.setFileOpStatus(FileOpStatusEnum.IMPORT_FAILED.getKey());
            }
            logger.error("FinaCxgOrderFileProcessor|process|msg:{}", e.getMessage(), e);
            String msg = String.format("高端资金文件导入失败，%s", finaFileMessageBean.getFileName());
            OpsMonitor.warn(msg, OpsMonitor.ERROR);
        } finally {
            if (!StringUtils.isEmpty(finaFileProcessRecPo.getRecordNo())) {
                if (!FileOpStatusEnum.IMPORT_FAILED.getKey().equals(finaFileProcessRecPo.getFileOpStatus())) {
                    finaFileProcessRecPo.setFileOpStatus(FileOpStatusEnum.IMPORT_SUCCESSFUL.getKey());
                }
                // 更新文件处理状态
                afterFileProcess(finaFileProcessRecPo);
            }
            logger.info("FinaCxgOrderFileProcessor|process end,recordNo:{} processStatus:{}", finaFileProcessRecPo.getRecordNo(), finaFileProcessRecPo.getProcessStatus());
        }
    }

    private void finaCxgOrderFileImport(FinaFileMessageBean finaFileMessageBean, FinaFileProcessRecPo finaFileProcessRecPo) throws Exception {
        FileImportContext fileImportContext = new FileImportContext();
        fileImportContext.setFileName(finaFileMessageBean.getFileName());
        fileImportContext.setRelationPath(File.separator + finaFileMessageBean.getExportDt() + File.separator);
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("workDay", finaFileMessageBean.getExportDt());
        paramMap.put("dataSourceType", getDataSourceType(finaFileMessageBean.getDisFileType()));
        paramMap.put("batchNo", finaFileProcessRecPo.getRecordNo());
        paramMap.put("now", new Date());
        fileImportContext.setParams(paramMap);
        finaCxgOrderFileImportService.process(fileImportContext);
    }

    /**
     * dataExecute:异步生成储蓄罐存入文件
     *
     * @param taTradeDt
     * @param batchNo
     * <AUTHOR>
     * @date 2019年7月9日 下午4:13:50
     */
    private void dataExecute(final String taTradeDt, final String batchNo) {
        howBuyRunTaskUil.runTask(new CxgOrderFileGenerateTask(taTradeDt, batchNo, cxgOrderFileGenerateProcessor));
    }

    /**
     * buildFinaDirectionFileRecPo:回款方向
     *
     * @param recDtl
     * @param date
     * @return
     * <AUTHOR>
     * @date 2018年6月15日 下午1:44:52
     */
    @Override
    public HighCxgSaveFileRecPo build(String[] recDtl, String workDay, Date date, String dataSourceType, String batchNo) {
        // 只处理专户和私募的数据
        if (!ProductTypeEnum.ZHUANHU.getCode().equals(recDtl[3]) &&
                !ProductTypeEnum.QUANSHANG_XIAOJIHE.getCode().equals(recDtl[3]) &&
                !ProductTypeEnum.SM.getCode().equals(recDtl[3])) {
            return null;
        }
        HighCxgSaveFileRecPo highCxgSaveFileRecPo = new HighCxgSaveFileRecPo();
        // 控管表流水号|申请金额|产品代码|产品类型|产品简称|分销机构|客户真实姓名|客户身份证|资金账号|银行卡号|业务类型|来源业务流水号|业务流水号|交易账号
        // 唯一
        highCxgSaveFileRecPo.setDealNo(recDtl[0]);
        highCxgSaveFileRecPo.setApplyAmt(recDtl[1]);
        highCxgSaveFileRecPo.setProdCode(recDtl[2]);
        highCxgSaveFileRecPo.setProductType(recDtl[3]);
        highCxgSaveFileRecPo.setProdName(recDtl[4]);
        highCxgSaveFileRecPo.setDisCode(recDtl[5]);
        highCxgSaveFileRecPo.setCustName(recDtl[6]);
        highCxgSaveFileRecPo.setIdNo(recDtl[7]);
        highCxgSaveFileRecPo.setCustBankId(recDtl[8]);
        highCxgSaveFileRecPo.setCustBankNo(recDtl[9]);
        highCxgSaveFileRecPo.setBusiType(recDtl[10]);
        // 不唯一
        highCxgSaveFileRecPo.setBusiDealNo(recDtl[11]);
        // recDtl[12] 不需要
        highCxgSaveFileRecPo.setTxAcctNo(recDtl[13]);

        highCxgSaveFileRecPo.setBatchNo(batchNo);
        highCxgSaveFileRecPo.setApplyDate(workDay);
        highCxgSaveFileRecPo.setWorkDay(workDay);
        // 是否处理
        highCxgSaveFileRecPo.setDataStat(YesOrNoEnum.NO.getCode());
        highCxgSaveFileRecPo.setCreateDate(date);
        return highCxgSaveFileRecPo;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void insert(List<?> list) {
        highCxgSaveFileRecRepository.batchInsert((List<HighCxgSaveFileRecPo>) list);
    }

    @Override
    public void validate(String[] recDtl) {
        if (recDtl.length < 12) {
            logger.error("资金下发储蓄罐文件格式错误：{}", JSON.toJSONString(recDtl));
            // 文件格式错误
            throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_FINA_DIRECTION_FILE_FAIL);
        }
    }
}
