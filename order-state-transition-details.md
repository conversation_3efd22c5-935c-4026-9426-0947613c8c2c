# 高端批处理订单状态流转详细说明 (High-Batch Order State Transition Details)

## 字段状态值说明 (Field Status Value Description)

### 1. NOTIFY_SUBMIT_FLAG (HIGH_DEAL_ORDER_DTL.NOTIFY_SUBMIT_FLAG)
通知上报标识 (Notification Submit Flag)

| 状态值 (Value) | 枚举名称 (Enum Name) | 中文描述 (Chinese) | 英文描述 (English) | 触发条件 (Trigger Condition) |
|---|---|---|---|---|
| 0 | NO_NOTIFY | 未通知 | Not Notified | 订单创建后，支付成功或无需支付时的初始状态 |
| 1 | HAS_BEEN_NOTIFY | 已通知 | Has Been Notified | 订单已发送到对账系统进行处理 |
| 2 | RE_NOTIFY | 需重新通知 | Need Re-notification | 系统检测到需要重新发送通知的情况 |

### 2. SUBMIT_APP_FLAG (SIMU_FUND_CHECK_ORDER.SUBMIT_APP_FLAG)
上报申请标识 (Submit Application Flag)

| 状态值 (Value) | 枚举名称 (Enum Name) | 中文描述 (Chinese) | 英文描述 (English) | 触发条件 (Trigger Condition) |
|---|---|---|---|---|
| 1 | SUBMITTING | 上报中 | Submitting | 对账订单创建后，正在向后台系统上报 |
| 2 | SUBMITTED | 上报完成 | Submitted | 成功上报到后台系统 |
| 3 | SUBMIT_AGAIN | 需重新上报 | Need Submit Again | 上报失败，需要重新上报 |
| 4 | NO_NEED_SUBMITTING | 无需上报 | No Need Submitting | 订单已撤单，无需继续上报 |

### 3. TX_APP_FLAG (交易申请状态)
交易申请标识 (Transaction Application Flag)

| 状态值 (Value) | 枚举名称 (Enum Name) | 中文描述 (Chinese) | 英文描述 (English) | 触发条件 (Trigger Condition) |
|---|---|---|---|---|
| 1 | APP_SUCCESS | 申请成功 | Application Success | 后台系统确认交易申请成功 |
| 2 | SELF_REVOCATION | 自行撤销 | Self Revocation | 用户主动撤销交易 |
| 3 | FORCE_REVOCATION | 强制撤销 | Force Revocation | 系统强制撤销交易 |

### 4. TX_COMP_FLAG (交易对账状态)
交易对账标识 (Transaction Reconciliation Flag)

| 状态值 (Value) | 枚举名称 (Enum Name) | 中文描述 (Chinese) | 英文描述 (English) | 触发条件 (Trigger Condition) |
|---|---|---|---|---|
| 0 | NOT_NEED | 无需对账 | No Need Check | 某些特殊交易类型无需对账 |
| 1 | COMPLETED | 对账匹配 | Reconciliation Completed | 对账成功，数据匹配 |
| 2 | UNMATCHED | 对账不匹配 | Reconciliation Unmatched | 对账失败，数据不匹配 |

### 5. TX_ACK_FLAG (订单确认状态)
订单确认标识 (Order Confirmation Flag)

| 状态值 (Value) | 枚举名称 (Enum Name) | 中文描述 (Chinese) | 英文描述 (English) | 触发条件 (Trigger Condition) |
|---|---|---|---|---|
| 0 | NO_NEED_CONFIRM | 无需确认 | No Need Confirmation | 某些交易类型无需确认 |
| 1 | CONFIRM_UN | 未确认 | Unconfirmed | 等待确认的初始状态 |
| 2 | CONFIRMING | 确认中 | Confirming | 正在进行确认处理 |
| 3 | CONFIRM_PART | 部分确认 | Partial Confirmation | 部分交易确认成功 |
| 4 | CONFIRM_SUCCESS | 确认成功 | Confirmation Success | 全部交易确认成功 |
| 5 | CONFIRM_FAIL | 确认失败 | Confirmation Failed | 交易确认失败 |

## 状态流转触发场景 (State Transition Trigger Scenarios)

### 场景1: 正常认购/申购流程 (Normal Subscription/Purchase Flow)
1. **订单创建** → `NOTIFY_SUBMIT_FLAG = 0` (未通知)
2. **支付成功** → 触发通知流程
3. **创建对账订单** → `NOTIFY_SUBMIT_FLAG = 1` (已通知), `SUBMIT_APP_FLAG = 1` (上报中)
4. **上报成功** → `SUBMIT_APP_FLAG = 2` (上报完成)
5. **交易申请成功** → `TX_APP_FLAG = 1` (申请成功)
6. **对账匹配** → `TX_COMP_FLAG = 1` (对账匹配)
7. **确认成功** → `TX_ACK_FLAG = 4` (确认成功)

### 场景2: 赎回流程 (Redemption Flow)
1. **订单创建** → `NOTIFY_SUBMIT_FLAG = 0` (未通知)
2. **无需支付** → 直接触发通知流程
3. **创建对账订单** → `NOTIFY_SUBMIT_FLAG = 1` (已通知), `SUBMIT_APP_FLAG = 1` (上报中)
4. 后续流程同认购/申购

### 场景3: 支付失败撤单 (Payment Failed Cancellation)
1. **订单创建** → `NOTIFY_SUBMIT_FLAG = 0` (未通知)
2. **支付失败** → 订单撤单，不进入通知流程
3. **订单状态更新** → 订单状态变为支付失败

### 场景4: 上报失败重试 (Submit Failed Retry)
1. **上报失败** → `SUBMIT_APP_FLAG = 3` (需重新上报)
2. **重新上报** → `SUBMIT_APP_FLAG = 1` (上报中)
3. **再次上报成功** → `SUBMIT_APP_FLAG = 2` (上报完成)

### 场景5: 用户主动撤单 (User Initiated Cancellation)
1. **用户撤单** → `TX_APP_FLAG = 2` (自行撤销)
2. **更新撤单来源** → `CANCEL_ORDER_SRC = USER_INITIATED`
3. **订单状态更新** → 订单状态变为自行撤销

### 场景6: 系统强制撤单 (System Force Cancellation)
1. **系统检测异常** → `TX_APP_FLAG = 3` (强制撤销)
2. **更新撤单来源** → `CANCEL_ORDER_SRC = FORCE_CANCEL`
3. **订单状态更新** → 订单状态变为强制撤销

### 场景7: 对账不匹配处理 (Reconciliation Mismatch Handling)
1. **对账不匹配** → `TX_COMP_FLAG = 2` (对账不匹配)
2. **创建交易异常** → 记录异常信息
3. **人工处理** → 等待人工干预或自动重试

### 场景8: 重新通知流程 (Re-notification Flow)
1. **检测到需重新通知** → `NOTIFY_SUBMIT_FLAG = 2` (需重新通知)
2. **重新创建对账订单** → 重新进入上报流程
3. **更新通知状态** → `NOTIFY_SUBMIT_FLAG = 1` (已通知)

## 关键业务规则 (Key Business Rules)

### 支付相关规则 (Payment Related Rules)
- 认购/申购订单必须支付成功后才能进入通知流程
- 赎回订单无需支付，直接进入通知流程
- 支付失败的订单会被自动撤单，不进入后续流程

### 上报相关规则 (Submission Related Rules)
- 每个订单明细对应一个对账订单
- 上报失败的订单会标记为"需重新上报"
- 撤单状态的订单会标记为"无需上报"
- 支持合并上报和单独上报两种模式

### 对账相关规则 (Reconciliation Related Rules)
- 对账匹配后才能进入确认流程
- 对账不匹配会创建交易异常记录
- 某些交易类型可能无需对账

### 确认相关规则 (Confirmation Related Rules)
- 确认成功表示交易完全完成
- 部分确认表示交易部分成功
- 确认失败需要进行异常处理

## 监控和告警 (Monitoring and Alerting)

### 关键监控指标 (Key Monitoring Metrics)
- 长时间停留在"上报中"状态的订单
- 重复上报失败的订单
- 对账不匹配的订单数量
- 确认失败的订单数量

### 告警规则 (Alert Rules)
- 上报中状态超过30分钟的订单
- 连续3次上报失败的订单
- 对账不匹配率超过阈值
- 确认失败率超过阈值
