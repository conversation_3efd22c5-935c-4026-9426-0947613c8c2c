package com.howbuy.tms.high.batch.service.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.queryacccustinfo.bean.AccCustInfoBean;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.service.common.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.common.OpsMonitor;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;
import java.util.List;

/**
 * @Description:校验高端客户身份证过期
 * @Author: yun.lu
 * Date: 2025/1/23 13:32
 */
@Data
@AllArgsConstructor
public class CheckCustomerIdCardExpireTask extends AbstractHowbuyBaseTask {
    private static Logger log = LogManager.getLogger(CheckCustomerIdCardExpireTask.class);

    private List<HighDealOrderDtlPo> highDealOrderDtlPoList;
    private QueryCustInfoOuterService queryCustInfoOuterService;
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    private String currentTaDt;


    @Override
    protected void callTask() {
        if (CollectionUtils.isEmpty(highDealOrderDtlPoList)) {
            return;
        }
        for (HighDealOrderDtlPo highDealOrderDtlPo : highDealOrderDtlPoList) {
            String hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(highDealOrderDtlPo.getTxAcctNo());
            AccCustInfoBean accCustInfoBean = queryCustInfoOuterService.queryAccCustomerBaseInfo(hbOneNo);
            if (accCustInfoBean.getIdAlwaysValidFlag() != null && YesOrNoEnum.YES.getCode().equals(accCustInfoBean.getIdAlwaysValidFlag())) {
                log.info("身份证长期有效,认为没有过期,queryIdCardInfoResult={}", JSON.toJSONString(accCustInfoBean));
                return;
            } else {
                if (StringUtils.isNotBlank(accCustInfoBean.getIdValidityEnd())) {
                    Date expireDate = DateUtil.parseDate(accCustInfoBean.getIdValidityEnd(), DateUtil.SHORT_DATEPATTERN);
                    if (expireDate.compareTo(DateUtil.string2Date(highDealOrderDtlPo.getSubmitTaDt(), DateUtil.SHORT_DATEPATTERN)) < 0) {
                        log.info("身份证非长期有效,但是身份证已经过期,queryIdCardModel={}", JSON.toJSONString(accCustInfoBean));
                        String errorMsg = getErrorMsg(highDealOrderDtlPo.getDealNo(), highDealOrderDtlPo.getTxAcctNo(), highDealOrderDtlPo.getSubmitTaDt());
                        OpsMonitor.warn(errorMsg, OpsMonitor.INFO);
                    }
                } else {
                    log.info("获取不到身份证过期时间,认为已经过期,queryIdCardInfoResult={}", JSON.toJSONString(accCustInfoBean));
                    String errorMsg = "客户号:" + highDealOrderDtlPo.getTxAcctNo() + ",该客户查不到身份证有效期,订单号:" + highDealOrderDtlPo.getDealNo() + "请在上报日:" + highDealOrderDtlPo.getSubmitTaDt() + "前处理";
                    OpsMonitor.warn(errorMsg, OpsMonitor.INFO);
                }
            }

        }
    }

    /**
     * 获取错误告警
     */
    private String getErrorMsg(String dealNo, String txAcctNo, String submitTaDt) {
        int submitTaDtNum = Integer.parseInt(submitTaDt);
        int currentTaDtNum = Integer.parseInt(currentTaDt);
        if (currentTaDtNum >= submitTaDtNum) {
            return "今天为上报日，客户号：" + txAcctNo + "， 订单号：" + dealNo + "， 用户证件有效期已过，请尽快联系客户更新资料，否则交易会被判失败";
        } else {
            return "客户号：" + txAcctNo + "， 订单号：" + dealNo + "， 用户证件有效期于上报日将失效，请于上报日：" + submitTaDt + " 前联系客户更新资料，否则交易会被判失败";
        }
    }
}
